//
//  LsLogger.swift
//  swi mit
//
//  Created by <PERSON><PERSON> on 30.08.20.
//  Copyright © 2020 Switcher Inc. All rights reserved.
//

import Foundation
#if CRASHLYTICS
import FirebaseCrashlytics
#endif

struct LsLogger {

    static let `default` = LsLogger()

    private let reporter: LsReporter
    let category: StaticString?

    init(subsystem: StaticString? = nil, category: StaticString? = nil) {
        self.category = category
        self.reporter = LsReporter(subsystem: subsystem, category: category)
    }

    private init() {
        reporter = LsReporter.default
        self.category = nil
    }

    func setDebugLogLevel(_ level: Int) {
        reporter.setLogLevel(type: .debug, level: level)
    }

    func log(type: LsLogType = .info, level: Int = 0, _ message: String) {
        reporter.log(type: type, level: level, message)
        logCrashlytics(message)
    }

    func debug(level: Int, _ message: String) {
        log(type: .debug, level: level, message)
    }

    func info(_ message: String) {
        log(type: .info, level: 0, message)
    }

    func warning(_ message: String) {
        log(type: .warning, level: 0, message)
    }

    func error(_ error: any SwitcherError) {
#if CRASHLYTICS
        Crashlytics.crashlytics().record(error: error)
#endif
        log(type: .error, level: 0, error.devLog)
    }

    func error(_ message: String) {
#if CRASHLYTICS
        let error = NSError(domain: message, code: -1, userInfo: nil)
        Crashlytics.crashlytics().record(error: error)
#endif
        log(type: .error, level: 0, message)
    }

    private func logCrashlytics(_ message: String) {
#if CRASHLYTICS
        if let category {
            let msg = "[\(category)] \(message)"
            Crashlytics.crashlytics().log(msg)
        } else {
            Crashlytics.crashlytics().log(message)
        }
#endif
    }
}

@objc class CLsLogger: NSObject {
    private let lsLogger: LsLogger = .default

    let subsystem: String?
    let category: String?

    @objc init(subsystem: String, category: String) {
        self.category = category
        self.subsystem = subsystem
    }

    func setDebugLogLevel(_ level: Int) {
        lsLogger.setDebugLogLevel(level)
    }

    private func log(type: LsLogType = .info, level: Int = 0, _ message: String) {
        if let category {
            lsLogger.log(type: type, level: level, "[\(category)] \(message)")
        } else {
            lsLogger.log(type: type, level: level, message)
        }
    }

    @objc public func debug(level: Int, _ message: String) {
        log(type: .debug, level: level, message)
    }

    @objc public func info(_ message: String) {
        log(type: .info, level: 0, message)
    }

    @objc public func info(_ format: String, stringValue: String) {
        let message = String(format: format, stringValue)
        info(message)
    }

    @objc public func info(_ format: String, intValue: Int) {
        let message = String(format: format, intValue)
        info(message)
    }

    @objc public func info(_ format: String, doubleValue: Double) {
        let message = String(format: format, doubleValue)
        info(message)
    }

    @objc public func warning(_ message: String) {
        log(type: .warning, level: 0, message)
    }

    @objc public func error(_ error: any Error) {
        lsLogger.error(error.localizedDescription)
    }

    @objc public func error(message: String) {
        lsLogger.error(message)
    }
}
