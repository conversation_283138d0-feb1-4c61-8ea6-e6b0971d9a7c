//
//  DFlowPushableStringBuffer.swift
//  swi mit
//
//  Created by <PERSON><PERSON> on 06.07.21.
//  Copyright © 2021 Switcher Inc. All rights reserved.
//

import Foundation

class DFlowPushableStringBuffer: DFlowPushable {

    private var me: UnsafeMutablePointer<DFLOW_PUSHABLE_SB> {
        return ptr.assumingMemoryBound(to: DFLOW_PUSHABLE_SB.self)
    }

    init() {
        let me = dflow_pushable_sb_create()!
        super.init(dflow_pushable: &me.pointee.z, transfer: true)
    }

    /**
     * Extract data without any copy.
     * Once extracted, the data is no longer in the string buffer. So, extracting twice does not
     * return twice the same data.
     */
    final func ejectData() -> Data {
        let size = gmu_sb_size(me.pointee.sb)
        if let ptr = gmu_sb_eject(me.pointee.sb) {
            return Data(bytesNoCopy: ptr, count: size, deallocator: .free)
        } else {
            assert(size == 0)
            return Data()
        }
    }

    /**
     * Extract data as a string without any copy.
     * Once extracted, the data is no longer in the string buffer. So, extracting twice does not
     * return twice the same data.
     */
    final func ejectString() -> String {
        let size = gmu_sb_size(me.pointee.sb)
        if let ptr = gmu_sb_eject(me.pointee.sb) {
            return String(cString: ptr, encoding: .utf8) ?? ""
        } else {
            assert(size == 0)
            return ""
        }
    }
}
