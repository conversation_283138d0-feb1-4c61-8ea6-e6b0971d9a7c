//
//  aengine.c
//  swi mit
//
//  Created by <PERSON><PERSON> on April 9, 2016.
//  Copyright (c) 2016 Switcher Inc.
//  This software is part of the SWIRL project. See the COPYRIGHT-SWIRL file.
//

#include <gmutil/gmutil.h>
#include <mtech/amixer/aengine.h>
#include "amath.h"


/*** literals ***/

/*
 * Levels used to detect clock drift between the hardware audio interface and the
 * OS timer.
 * We define two levels, one above which we start adjusting the sample clock
 * (thanks to the clock adjuster) and one above which we roughly resync
 * everything (by inserting silent data or skiping existing audio data).
 * On iPhone 5 iOS9 we saw the audio frame timestamp jittering up to 12 samples.
 * On iPhone 14 iOS16 we saw the audio frame timestamp jittering in voice mode up
 * to 363 samples (ts->mHostTime is an average and does not correspond to the
 * variable amount of samples delivered / requested by the rendering callbacks)!
 */
#define _ADJUST_LEVEL                     40    // in samples
#define _RESYNC_LEVEL                    200    // in samples
#define _RESYNC_LEVEL_VOICE_MODE_JITTER  500    // in samples

#define _CHECK_OSSTATUS(osStatus__M) do { \
    OSStatus status__M = osStatus__M; \
    if (status__M != noErr) { \
        GMU_FATAL_GEN(GMU_ERR_SYSTEM, "aengine: error %s (%d)\n", _osstatus2str(status__M), (int)status__M); \
    } \
} while(false)


/*** types ***/

union _dual_buf {
    AudioBufferList buf_list;
    struct {
        UInt32      mNumberBuffers;
        AudioBuffer mBuffers[2];
    } dual_buf_list;
};


/*** globals ***/

C3_CLASS aengine_class = {
    .name = "aengine"
};


/*** functions ***/

static const char *_osstatus2str(OSStatus st)
{
#define _OSSTATUS_CASE(x) case x: return #x;
    switch (st) {
        _OSSTATUS_CASE(kAudioUnitErr_InvalidProperty)
        _OSSTATUS_CASE(kAudioUnitErr_InvalidParameter)
        _OSSTATUS_CASE(kAudioUnitErr_InvalidElement)
        _OSSTATUS_CASE(kAudioUnitErr_NoConnection)
        _OSSTATUS_CASE(kAudioUnitErr_FailedInitialization)
        _OSSTATUS_CASE(kAudioUnitErr_TooManyFramesToProcess)
        _OSSTATUS_CASE(kAudioUnitErr_InvalidFile)
        _OSSTATUS_CASE(kAudioUnitErr_UnknownFileType)
        _OSSTATUS_CASE(kAudioUnitErr_FileNotSpecified)
        _OSSTATUS_CASE(kAudioUnitErr_FormatNotSupported)
        _OSSTATUS_CASE(kAudioUnitErr_Uninitialized)
        _OSSTATUS_CASE(kAudioUnitErr_InvalidScope)
        _OSSTATUS_CASE(kAudioUnitErr_PropertyNotWritable)
        _OSSTATUS_CASE(kAudioUnitErr_CannotDoInCurrentContext)
        _OSSTATUS_CASE(kAudioUnitErr_InvalidPropertyValue)
        _OSSTATUS_CASE(kAudioUnitErr_PropertyNotInUse)
        _OSSTATUS_CASE(kAudioUnitErr_Initialized)
        _OSSTATUS_CASE(kAudioUnitErr_InvalidOfflineRender)
        _OSSTATUS_CASE(kAudioUnitErr_Unauthorized)
        _OSSTATUS_CASE(kAudioUnitErr_MIDIOutputBufferFull)
        _OSSTATUS_CASE(kAudioComponentErr_InstanceInvalidated)
        _OSSTATUS_CASE(kAudioUnitErr_RenderTimeout)
        _OSSTATUS_CASE(kAudioUnitErr_ExtensionNotFound)
        _OSSTATUS_CASE(kAudioUnitErr_InvalidParameterValue)

        // iOS only?
        _OSSTATUS_CASE(kAudioComponentErr_DuplicateDescription)
        _OSSTATUS_CASE(kAudioComponentErr_UnsupportedType)
        _OSSTATUS_CASE(kAudioComponentErr_TooManyInstances)
        _OSSTATUS_CASE(kAudioComponentErr_NotPermitted)
        _OSSTATUS_CASE(kAudioComponentErr_InitializationTimedOut)
        _OSSTATUS_CASE(kAudioComponentErr_InvalidFormat)
    }
    return "?";
#undef _OSSTATUS_CASE
}

static void _dest_buffer_enlarge(AENGINE *me, int slen)
{
    if (slen > me->_dest_buf_slen) {
        free(me->_dest_mono_buf);
        free(me->_dest_silent_buf);
        me->_dest_buf_slen = slen;
        me->_dest_mono_buf = malloc(slen * sizeof(float));
        me->_dest_silent_buf = calloc(slen, sizeof(float));
    }
}

static OSStatus _source_render_callback(
    void *                       refCon,
    AudioUnitRenderActionFlags * actionFlags, // in/out
    const AudioTimeStamp *       ts,
    UInt32                       bus,
    UInt32                       frames,
    AudioBufferList * __nullable audio)
{
    AENGINE *me = refCon;

    // catch start time
    if (me->_source_first) {
        me->_source_first = false;

        mflow_clock_adjuster_reset(me->_source_clock_adjuster);

        if (!(ts->mFlags & kAudioTimeStampHostTimeValid))
            printf("aengine: WARNING: source host time not valid\n");
        if ((ts->mFlags & kAudioTimeStampRateScalarValid) && ts->mRateScalar != 1.0)
            printf("aengine: WARNING: strange source rate scalar %g\n", (double)ts->mRateScalar);

        int64_t mts = gmu_apl_mach_abs_time_to_us(ts->mHostTime);
        if (me->_time_ref_first) {
            me->_time_ref_first = false;
            me->_time_ref.offset_us = mts;
        }

        me->_source_sts = mflow_atime_us_to_sample(&me->_time_ref, mts);
        me->_source_data_gap = 0;

        struct mflow_pcm_def pcm_def = {
            .stereo = true,
        };

        mflow_pcm_pushable_array_begin(&me->_source_outputs, &pcm_def, &me->_time_ref);

        // printf("aengine: src: first sample mHostTime: %lld\n", me->_src_timestamp.mHostTime);
        // printf("aengine: src: first sample mSampleTime: %g\n", me->_src_timestamp.mSampleTime);
    } else {
        int64_t mts = gmu_apl_mach_abs_time_to_us(ts->mHostTime);
        int64_t sts = mflow_atime_us_to_sample(&me->_time_ref, mts);
        int64_t delta = sts - me->_source_sts;
        if (gmu_abs_i64(delta) > me->_resync_level) {
            /*
             * We have more that me->_resync_level sample delay between the timestamp
             * and the sample counter. This means that audio is no in sync with
             * system clock or there is a gap in the audio data.
             * Resync.
             */
            me->_source_data_gap += gmu_abs_i64(delta);
            printf("aengine: WARNING: sample gap detected in source (gap=%lld) => resync\n", delta);
            me->_source_sts = sts;
            mflow_clock_adjuster_reset(me->_source_clock_adjuster);
        } else if (delta > _ADJUST_LEVEL) {
            /*
             * The host time is running faster than the hardware clock.
             * We have to increase the clock adjuster shift in order to push out
             * more samples than the hardware produces.
             */
            int shift = mflow_clock_adjuster_get_real_shift(me->_source_clock_adjuster);
            shift += _ADJUST_LEVEL;
            mflow_clock_adjuster_set_target_shift(me->_source_clock_adjuster, shift);
            // printf("aengine: source clock adjustment: shift=%d\n", shift);
        } else if (delta < -_ADJUST_LEVEL) {
            /*
             * The host time is running slower than the hardware clock.
             * We need to decrease the clock adjuster shift in order to push out
             * less samples than the hardware produces.
             */
            int shift = mflow_clock_adjuster_get_real_shift(me->_source_clock_adjuster);
            shift -= _ADJUST_LEVEL;
            mflow_clock_adjuster_set_target_shift(me->_source_clock_adjuster, shift);
            // printf("aengine: source clock adjustment: shift=%d\n", shift);
        }
    }

    bool stereo = me->_source_stereo;
    int buf_count = stereo ? 2 : 1;

    // build a AudioBufferList on the stack, valid also for stereo audio
    union _dual_buf _dual_buf = {
        .dual_buf_list = {
            .mNumberBuffers = buf_count,
            .mBuffers = {
                {
                    .mNumberChannels = 1,
                    .mDataByteSize = frames * sizeof(float),
                }, {
                    .mNumberChannels = 1,
                    .mDataByteSize = frames * sizeof(float),
                }
            },
        }
    };

    assert(_dual_buf.buf_list.mBuffers == _dual_buf.dual_buf_list.mBuffers);
    assert(sizeof(union _dual_buf) == sizeof(AudioBufferList) + sizeof(AudioBuffer));

    // build a segment
    assert(frames <= UINT16_MAX);
    MFLOW_PCM_SEG seg = {
        .slen = (uint16_t)frames,
        .flags = stereo ? MFLOW_PCM_BUF_FLAG_STEREO : 0,
    };

    /*
     * If the user muted the source or if there is no buffer in the pool,
     * we push silent data.
     */

    bool silent = false;

    if (me->_source_muted) {
        silent = true;
    } else {
        MFLOW_PCM_BUF *left = mflow_pcm_buf_pool_get_entry(me->_source_buf_pool, frames);
        if (left) {
            _dual_buf.buf_list.mBuffers[0].mData = left->samples;
            seg.left = left;
            if (stereo) {
                MFLOW_PCM_BUF *right = mflow_pcm_buf_pool_get_entry(me->_source_buf_pool, frames);
                if (right) {
                    _dual_buf.buf_list.mBuffers[1].mData = right->samples;
                    seg.right = right;
                } else {
                    _dual_buf.buf_list.mBuffers[0].mData = NULL;
                    seg.left = NULL;
                    c3_release(&left->z.z);
                    silent = true;
                }
            }
        } else {
            silent = true;
        }
    }

    // ????? (from internet)
    // we need to store the original buffer size, since AudioUnitRender seems to change the value
    // of the AudioBufferList's mDataByteSize (at least in the simulator). We need to write it back
    // later, or else we'll end up reallocating continuously in the render callback (BAD!)

    if (!silent) {
        AudioUnitRenderActionFlags flags = { 0 };

        OSStatus status = AudioUnitRender(me->_io_unit,
                                          &flags,
                                          ts,
                                          1,
                                          frames,
                                          &_dual_buf.buf_list);

        if (status != noErr) {
            // TODO
            assert(false);
        }

        assert(_dual_buf.buf_list.mBuffers[0].mData == seg.left->samples);
        assert(_dual_buf.buf_list.mBuffers[0].mDataByteSize == frames * sizeof(float));
    }

#if 0
    int crc = 0;
    void *ptr = NULL;
    if (seg.left) {
        crc = bll_crc32_block(seg.left->samples + seg.soff, seg.slen * sizeof(float));
        ptr = seg.left;
    }

    static int a;
    a++;
    if ((a % 16) == 0) {
        printf("tick: sts=%lld frame=%d crc=0x%08x ptr=%p silent=%D\n", me->_src_sts, (int)frames, crc, ptr, (int)silent);
    }

    if ((a % 256) == 0) {
        if (seg.left) {
            memset(seg.left->samples + seg.soff, 0, seg.slen * sizeof(float));
        }
    }
#endif

    if (me->_enable_drift_compensation) {
        mflow_clock_adjuster_put_input_segment(me->_source_clock_adjuster, seg);

        if (seg.left)
            c3_release(&seg.left->z.z);
        if (seg.right)
            c3_release(&seg.right->z.z);

        seg.slen = 0;
        mflow_clock_adjuster_emit_output_segment(me->_source_clock_adjuster, &seg);
    }

    mflow_pcm_pushable_array_push(&me->_source_outputs, me->_source_sts, &seg);

    if (seg.left)
        c3_release(&seg.left->z.z);
    if (seg.right)
        c3_release(&seg.right->z.z);

    me->_source_sts += seg.slen;

    return noErr;
}

/**
 * Populate the given audio buffer list with data coming from me->_dest_seg.
 * If the data is copied, me->_dest_seg buffers are released. Otherwise the
 * caller has to release them once the audio buffer list is not used anymore.
 * If me->_dest_muted is set, silent data is used instead.
 */
static void _dest_seg_to_audio_buffer_list(AENGINE *me, AudioBufferList *audio)
{
    bool in_stereo = me->_dest_seg.flags & MFLOW_PCM_BUF_FLAG_STEREO;
    bool out_stereo = me->_dest_stereo;
    int slen = me->_dest_seg.slen;
    int byteSize = slen * sizeof(float);
    bool keep = false;

    _dest_buffer_enlarge(me, slen);

    if (!out_stereo) {

        // destination audio is mono

        assert(audio->mNumberBuffers >= 1);
        assert(audio->mBuffers[0].mDataByteSize == byteSize);

        if (me->_dest_muted) {
            // ignore received audio data - replace by silent data

            if (audio->mBuffers[0].mData)
                memset(audio->mBuffers[0].mData, 0, byteSize);
            else
                audio->mBuffers[0].mData = me->_dest_silent_buf;

        } else if (in_stereo) {
            // received audio is stereo

            if (me->_dest_seg.left && me->_dest_seg.right) {
                // data of both channels is provided, no channel is silent

                if (!audio->mBuffers[0].mData)
                    audio->mBuffers[0].mData = me->_dest_mono_buf;

                amath_merge_samples(audio->mBuffers[0].mData,
                                    me->_dest_seg.left->samples + me->_dest_seg.soff,
                                    me->_dest_seg.right->samples + me->_dest_seg.soff,
                                    slen);

            } else if (me->_dest_seg.left) {
                // left data is provided, right channel is silent

                if (!audio->mBuffers[0].mData)
                    audio->mBuffers[0].mData = me->_dest_mono_buf;

                amath_copy_half_samples(audio->mBuffers[0].mData,
                                        me->_dest_seg.left->samples + me->_dest_seg.soff,
                                        slen);

            } else if (me->_dest_seg.right) {
                // right data is provided, left channel is silent

                if (!audio->mBuffers[0].mData)
                    audio->mBuffers[0].mData = me->_dest_mono_buf;

                amath_copy_half_samples(audio->mBuffers[0].mData,
                                        me->_dest_seg.right->samples + me->_dest_seg.soff,
                                        slen);

            } else {
                // no channel data is provided, both channels are silent

                if (audio->mBuffers[0].mData)
                    memset(audio->mBuffers[0].mData, 0, byteSize);
                else
                    audio->mBuffers[0].mData = me->_dest_silent_buf;
            }
        } else  {
            // received audio is mono

            if (me->_dest_seg.left) {
                // received audio is not silent

                if (audio->mBuffers[0].mData) {
                    memcpy(audio->mBuffers[0].mData, me->_dest_seg.left->samples + me->_dest_seg.soff, byteSize);
                } else {
                    audio->mBuffers[0].mData = me->_dest_seg.left->samples + me->_dest_seg.soff;
                    keep = true;
                }
            } else {
                // received audio is silent

                if (audio->mBuffers[0].mData)
                    memset(audio->mBuffers[0].mData, 0, byteSize);
                else
                    audio->mBuffers[0].mData = me->_dest_silent_buf;
            }
        }
    } else {

        // destination audio is stereo (could be multi-channel as well)

        assert(audio->mNumberBuffers >= 2);
        assert(audio->mBuffers[0].mDataByteSize == byteSize);
        assert(audio->mBuffers[1].mDataByteSize == byteSize);

        if (me->_dest_muted) {
            // ignore received audio data - replace by silent data

            if (audio->mBuffers[0].mData)
                memset(audio->mBuffers[0].mData, 0, byteSize);
            else
                audio->mBuffers[0].mData = me->_dest_silent_buf;

            if (audio->mBuffers[1].mData)
                memset(audio->mBuffers[1].mData, 0, byteSize);
            else
                audio->mBuffers[1].mData = me->_dest_silent_buf;

        } else if (in_stereo) {
            // received audio is stereo

            if (me->_dest_seg.left && me->_dest_seg.right) {
                // data of both channels is provided, no channel is silent

                if (audio->mBuffers[0].mData) {
                    memcpy(audio->mBuffers[0].mData, me->_dest_seg.left->samples + me->_dest_seg.soff, byteSize);
                } else {
                    audio->mBuffers[0].mData = me->_dest_seg.left->samples + me->_dest_seg.soff;
                    keep = true;
                }
                if (audio->mBuffers[1].mData) {
                    memcpy(audio->mBuffers[1].mData, me->_dest_seg.right->samples + me->_dest_seg.soff, byteSize);
                } else {
                    audio->mBuffers[1].mData = me->_dest_seg.right->samples + me->_dest_seg.soff;
                    keep = true;
                }
            } else if (me->_dest_seg.left) {
                // left data is provided, right channel is silent

                if (audio->mBuffers[0].mData) {
                    memcpy(audio->mBuffers[0].mData, me->_dest_seg.left->samples + me->_dest_seg.soff, byteSize);
                } else {
                    audio->mBuffers[0].mData = me->_dest_seg.left->samples + me->_dest_seg.soff;
                    keep = true;
                }
                if (audio->mBuffers[1].mData)
                    memset(audio->mBuffers[1].mData, 0, byteSize);
                else
                    audio->mBuffers[1].mData = me->_dest_silent_buf;
            } else if (me->_dest_seg.right) {
                // right data is provided, left channel is silent

                if (audio->mBuffers[0].mData)
                    memset(audio->mBuffers[0].mData, 0, byteSize);
                else
                    audio->mBuffers[0].mData = me->_dest_silent_buf;

                if (audio->mBuffers[1].mData) {
                    memcpy(audio->mBuffers[1].mData, me->_dest_seg.right->samples + me->_dest_seg.soff, byteSize);
                } else {
                    audio->mBuffers[1].mData = me->_dest_seg.right->samples + me->_dest_seg.soff;
                    keep = true;
                }
            } else {
                // no channel data is provided, both channels are silent

                if (audio->mBuffers[0].mData)
                    memset(audio->mBuffers[0].mData, 0, byteSize);
                else
                    audio->mBuffers[0].mData = me->_dest_silent_buf;

                if (audio->mBuffers[1].mData)
                    memset(audio->mBuffers[1].mData, 0, byteSize);
                else
                    audio->mBuffers[1].mData = me->_dest_silent_buf;
            }
        } else {
            // received audio is mono

            if (me->_dest_seg.left) {
                // audio data is provided, channel is not silent

                if (audio->mBuffers[0].mData) {
                    memcpy(audio->mBuffers[0].mData, me->_dest_seg.left->samples + me->_dest_seg.soff, byteSize);
                } else {
                    audio->mBuffers[0].mData = me->_dest_seg.left->samples + me->_dest_seg.soff;
                    keep = true;
                }
                if (audio->mBuffers[1].mData) {
                    memcpy(audio->mBuffers[1].mData, me->_dest_seg.left->samples + me->_dest_seg.soff, byteSize);
                } else {
                    audio->mBuffers[1].mData = me->_dest_seg.left->samples + me->_dest_seg.soff;
                    keep = true;
                }
            } else {
                // audio data is not provided, channel is silent

                if (audio->mBuffers[0].mData)
                    memset(audio->mBuffers[0].mData, 0, byteSize);
                else
                    audio->mBuffers[0].mData = me->_dest_silent_buf;

                if (audio->mBuffers[1].mData)
                    memset(audio->mBuffers[0].mData, 0, byteSize);
                else
                    audio->mBuffers[1].mData = me->_dest_silent_buf;
            }
        }

        for (int i = 2; i < audio->mNumberBuffers; i++)  {
            assert(audio->mBuffers[i].mDataByteSize == byteSize);

            if (audio->mBuffers[i].mData)
                memset(audio->mBuffers[i].mData, 0, byteSize);
            else
                audio->mBuffers[i].mData = me->_dest_silent_buf;
        }
    }

    if (!keep) {
        if (me->_dest_seg.left) {
            c3_release(&me->_dest_seg.left->z.z);
            me->_dest_seg.left = NULL;
        }
        if (me->_dest_seg.right) {
            c3_release(&me->_dest_seg.right->z.z);
            me->_dest_seg.right = NULL;
        }
    }
}

static OSStatus _dest_render_callback(
    void *                       refCon,
    AudioUnitRenderActionFlags * actionFlags, // in/out
    const AudioTimeStamp *       ts,
    UInt32                       bus,
    UInt32                       frames,
    AudioBufferList * __nullable audio)
{
    AENGINE *me = refCon;

    athread_queue_pump(me->_queue);

    // release previous buffers

    if (me->_dest_seg.left) {
        c3_release(&me->_dest_seg.left->z.z);
        me->_dest_seg.left = NULL;
    }
    if (me->_dest_seg.right) {
        c3_release(&me->_dest_seg.right->z.z);
        me->_dest_seg.right = NULL;
    }

    // look at time and detect synchonization problems

    assert(frames <= UINT16_MAX);

    if (me->_dest_first) {
        me->_dest_first = false;
        me->_dest_sts = 0;
        me->_io_proc_slen = 0; // force a mflow_pcm_pullable_io_proc_len_changed to be sent
        me->_dest_data_gap = 0;
        me->_dest_is_filling_gap = false;

        mflow_clock_adjuster_reset(me->_dest_clock_adjuster);

        if (!(ts->mFlags & kAudioTimeStampHostTimeValid))
            printf("aengine: WARNING: host time not valid\n");
        if ((ts->mFlags & kAudioTimeStampRateScalarValid) && ts->mRateScalar != 1.0)
            printf("aengine: WARNING: strange rate scalar %g\n", (double)ts->mRateScalar);

        int64_t mts = gmu_apl_mach_abs_time_to_us(ts->mHostTime);
        if (me->_time_ref_first) {
            me->_time_ref_first = false;
            me->_time_ref.offset_us = mts;
        }

        memset(&me->_dest_input_pcm_def, 0, sizeof(struct mflow_pcm_def));
        me->_dest_input_pcm_def.stereo = me->_dest_stereo;
        mflow_pcm_pullable_begin(me->_dest_input, &me->_time_ref, &me->_dest_input_pcm_def);

        // printf("aengine: dst: first sample mHostTime: %lld\n", ts->mHostTime);
        // printf("aengine: dst: first sample mSampleTime: %g\n", ts->mSampleTime);
    } else {
        int64_t mts = gmu_apl_mach_abs_time_to_us(ts->mHostTime);
        int64_t sts = mflow_atime_us_to_sample(&me->_time_ref, mts);
        int64_t delta = sts - me->_dest_sts;
        if (me->_dest_is_filling_gap) {
            if (delta >= 0) {
                /*
                 * The gap was filled with silent data. We may have filled too
                 * much and need to discard some pulled audio data now. We
                 * do that by moving me->_dest_sts forward.
                 */
                me->_dest_sts = sts;
                mflow_clock_adjuster_reset(me->_dest_clock_adjuster);
                me->_dest_is_filling_gap = 0;
            }
        } else if (delta > me->_resync_level) {
            /*
             * We have more that me->_resync_level sample delay between the timestamp
             * and the sample counter. This means that there is a gap in the output
             * data and we have to resync.
             * 'delta' is positive which means that we have to discard some pulled
             * audio samples by moving me->_dest_sts forward.
             */
            printf("aengine: WARNING: sample gap detected in destination (gap=%lld) => resync\n", delta);
            me->_dest_sts = sts;
            mflow_clock_adjuster_reset(me->_dest_clock_adjuster);
            me->_dest_data_gap += delta;

        } else if (delta < -me->_resync_level) {
            /*
             * We have more than me->_resync_level sample delay between the timestamp
             * and the sample counter. This means that there is a gap in the output
             * data and we have to resync.
             * The gap is negative, i.e. we are handling samples we already
             * requested. We need to provide silent data until the gap is
             * closed.
             */
            printf("aengine: WARNING: sample gap detected in destination (gap=%lld) => resync\n", delta);
            me->_dest_is_filling_gap = true;
            // me->_dest_data_gap is updated later

        } else if (delta > _ADJUST_LEVEL) {
            /*
             * The host time is running faster than the hardware clock.
             * We have to decrease the clock adjuster shift in order to pull
             * more samples than the hardware consumes.
             */
            int shift = mflow_clock_adjuster_get_real_shift(me->_dest_clock_adjuster);
            shift -= _ADJUST_LEVEL;
            mflow_clock_adjuster_set_target_shift(me->_dest_clock_adjuster, shift);
            // printf("aengine: dest clock adjustment: shift=%d\n", shift);
            // TODO: me->_dst_clock_drift +=? delta;

        } else if (delta < -_ADJUST_LEVEL) {
            /*
             * The host time is running slower than the hardware clock.
             * We need to increase the clock adjuster shift in order to pull
             * less samples than the hardware consumes.
             */
            int shift = mflow_clock_adjuster_get_real_shift(me->_dest_clock_adjuster);
            shift += _ADJUST_LEVEL;
            mflow_clock_adjuster_set_target_shift(me->_dest_clock_adjuster, shift);
            // printf("aengine: dest clock adjustment: shift=%d\n", shift);
            // TODO: me->_dst_clock_drift +=? delta;
        }
    }

    // prepare data to send to hardware output

    if (me->_dest_is_filling_gap) {
        // generate silent data
        me->_dest_seg.slen = frames;
        me->_dest_seg.flags = 0;
        me->_dest_data_gap += frames;
    } else {
        // Inform about a changed io processing buffer length with some filtering

        if ((frames > me->_io_proc_slen) || (frames < (me->_io_proc_slen - 100))) {
            me->_io_proc_slen = frames + 10; // Add some margin for resampling (+/- 1 sample here and later)
            mflow_pcm_pullable_io_proc_len_changed(me->_dest_input, me->_io_proc_slen);
        }

        // get audio data from input

        if (me->_enable_drift_compensation) {
            int slen = mflow_clock_adjuster_prepare(me->_dest_clock_adjuster, frames);

            MFLOW_PCM_SEG seg;
            mflow_pcm_pullable_prepare(me->_dest_input, me->_dest_sts, slen);
            mflow_pcm_pullable_pull(me->_dest_input, me->_dest_sts, slen, &seg);
            mflow_clock_adjuster_put_input_segment(me->_dest_clock_adjuster, seg);
            if (seg.left)
                c3_release(&seg.left->z.z);
            if (seg.right)
                c3_release(&seg.right->z.z);

            mflow_clock_adjuster_emit_output_segment(me->_dest_clock_adjuster, &me->_dest_seg);
            assert(me->_dest_seg.slen == frames);
            me->_dest_sts += slen;
        } else {
            mflow_pcm_pullable_prepare(me->_dest_input, me->_dest_sts, frames);
            mflow_pcm_pullable_pull(me->_dest_input, me->_dest_sts, frames, &me->_dest_seg);
            assert(me->_dest_seg.slen == frames);
            me->_dest_sts += frames;
        }
    }

    // convert data to audio buffer list

    _dest_seg_to_audio_buffer_list(me, audio);

    return noErr;
}

static void _print_format(AudioComponentInstance unit,
                          AudioUnitScope         scope,
                          AudioUnitElement       element)
{
    AudioStreamBasicDescription desc = { 0 };
    UInt32 desc_size = sizeof(desc);

    OSStatus status = AudioUnitGetProperty(unit, kAudioUnitProperty_StreamFormat, scope, element, &desc, &desc_size);
    _CHECK_OSSTATUS(status);

    printf(" mSampleRate=%g\n",       (double)desc.mSampleRate);
    printf(" mFormatID=%d\n",         (int)desc.mFormatID);
    printf(" mFormatFlags=%d\n",      (int)desc.mFormatFlags);
    printf(" mBytesPerPacket=%d\n",   (int)desc.mBytesPerPacket);
    printf(" mFramesPerPacket=%d\n",  (int)desc.mFramesPerPacket);
    printf(" mBytesPerFrame=%d\n",    (int)desc.mBytesPerFrame);
    printf(" mChannelsPerFrame=%d\n", (int)desc.mChannelsPerFrame);
    printf(" mBitsPerChannel=%d\n",   (int)desc.mBitsPerChannel);

    UInt32 value;
    UInt32 value_size = sizeof(value);
    status = AudioUnitGetProperty(unit, kAudioUnitProperty_MaximumFramesPerSlice, scope, element, &value, &value_size);
    if (status != noErr)
        value = -1;

    printf(" maximumFramesPerSlice=%d\n", (int)value);

    Float64 sample_rate;
    UInt32 sample_rate_size = sizeof(sample_rate);
    status = AudioUnitGetProperty(unit, kAudioUnitProperty_SampleRate, scope, element, &sample_rate, &sample_rate_size);
    _CHECK_OSSTATUS(status);

    printf(" sampleRate=%g\n", (double)sample_rate);
}

/*
static void _get_format(AudioComponentInstance unit,
                        AudioUnitScope         scope,
                        AudioUnitElement       element,
                        int *sample_rate,
                        bool *stereo)
{
    AudioStreamBasicDescription desc = { 0 };
    UInt32 desc_size = sizeof(desc);

    OSStatus status = AudioUnitGetProperty(unit, kAudioUnitProperty_StreamFormat, scope, element, &desc, &desc_size);
    _CHECK_OSSTATUS(status);

    if (sample_rate)
        *sample_rate = (int)round(desc.mSampleRate);
    if (stereo)
        *stereo = desc.mChannelsPerFrame == 2;
}
*/

static OSStatus _set_format(AudioComponentInstance unit,
                        AudioUnitScope         scope,
                        AudioUnitElement       element,
                        int sample_rate,
                        bool stereo)
{
    AudioStreamBasicDescription desc = {
        .mSampleRate       = sample_rate,
        .mFormatID         = kAudioFormatLinearPCM,
        .mFormatFlags      = kAudioFormatFlagsNativeFloatPacked | kLinearPCMFormatFlagIsNonInterleaved,
        .mBytesPerPacket   = 4,
        .mFramesPerPacket  = 1,
        .mBytesPerFrame    = 4,
        .mChannelsPerFrame = stereo ? 2 : 1,
        .mBitsPerChannel   = 32,
    };

    return AudioUnitSetProperty(unit,
                                kAudioUnitProperty_StreamFormat,
                                scope,
                                element, // output bus
                                &desc,
                                sizeof(desc));
}

static void _update_formats(AENGINE *me)
{
    OSStatus status;

    // set  destination format

    status = _set_format(me->_io_unit,
                         kAudioUnitScope_Input,
                         0, // output bus
                         me->_sample_rate,
                         me->_dest_stereo);

    _CHECK_OSSTATUS(status);

    // set source format

    status = _set_format(me->_io_unit,
                         kAudioUnitScope_Output,
                         1, // input bus
                         me->_sample_rate,
                         me->_source_stereo);

    _CHECK_OSSTATUS(status);
}

static void _create_io_unit(AENGINE *me)
{
    OSStatus status;

    AudioComponentDescription io_desc = {
        .componentType         = kAudioUnitType_Output,
        .componentSubType      = me->_voice_processing ? kAudioUnitSubType_VoiceProcessingIO :
                                                         kAudioUnitSubType_RemoteIO,
        .componentManufacturer = kAudioUnitManufacturer_Apple,
    };

    AudioComponent comp = AudioComponentFindNext(NULL, &io_desc);
    if (comp == NULL)
        GMU_FATAL_GEN(GMU_ERR_SYSTEM, "aengine: IO audio unit not found");

    status = AudioComponentInstanceNew(comp, &me->_io_unit);
    _CHECK_OSSTATUS(status);

    // enable IO for recording

    UInt32 flag = 1;
    status = AudioUnitSetProperty(me->_io_unit,
                                  kAudioOutputUnitProperty_EnableIO,
                                  kAudioUnitScope_Input,
                                  1, // input bus
                                  &flag,
                                  sizeof(flag));
    _CHECK_OSSTATUS(status);

    // enable IO for playback

    status = AudioUnitSetProperty(me->_io_unit,
                                  kAudioOutputUnitProperty_EnableIO,
                                  kAudioUnitScope_Output,
                                  0, // output bus
                                  &flag,
                                  sizeof(flag));
    _CHECK_OSSTATUS(status);

    // set formats

    _update_formats(me);

    flag = 0; // I woudl like to provide my own buffers, but sometimes CoreAudio provides its own buffers anyway
    status = AudioUnitSetProperty(me->_io_unit,
                                  kAudioUnitProperty_ShouldAllocateBuffer,
                                  kAudioUnitScope_Input,
                                  0,
                                  &flag,
                                  sizeof(flag));
    _CHECK_OSSTATUS(status);


    AURenderCallbackStruct render_callback_struct = {
        .inputProc = _dest_render_callback,
        .inputProcRefCon = me,
    };

    status = AudioUnitSetProperty(me->_io_unit,
                                  kAudioUnitProperty_SetRenderCallback,
                                  kAudioUnitScope_Global,
                                  0,
                                  &render_callback_struct,
                                  sizeof(render_callback_struct));
    _CHECK_OSSTATUS(status);

    // input callback

    AURenderCallbackStruct input_callback_struct = {
        .inputProc = _source_render_callback,
        .inputProcRefCon = me
    };
    status = AudioUnitSetProperty(me->_io_unit,
                                  kAudioOutputUnitProperty_SetInputCallback,
                                  kAudioUnitScope_Global,
                                  1,
                                  &input_callback_struct,
                                  sizeof(input_callback_struct));
    _CHECK_OSSTATUS(status);

    // AGC in case of VoiceProcessingIO, otherwise there is no AGC (remoteIO has no AGC)

    if (me->_voice_processing) {
        flag = me->_agc ? 1 : 0;
        status = AudioUnitSetProperty(me->_io_unit,
                                      kAUVoiceIOProperty_VoiceProcessingEnableAGC,
                                      kAudioUnitScope_Global,
                                      1, // input bus
                                      &flag,
                                      sizeof(flag));
        _CHECK_OSSTATUS(status);
    }

    // See comment about jitter in the header of this file
    if (me->_voice_processing && (gmu_ios_get_cpu() == GMU_IOS_CPU_A16)) {
        me->_resync_level = _RESYNC_LEVEL_VOICE_MODE_JITTER;
    } else {
        me->_resync_level = _RESYNC_LEVEL;
    }
}

static void _mediator_invoke_async(MFLOW_MEDIATOR *mediator, void *ctx, void (* func)(void *ctx))
{
    AENGINE *me = (AENGINE *)mediator->_owner;
    if ((me->_state == AENGINE_STATE_RUNNING) || (me->_state == AENGINE_STATE_PAUSED))
        athread_queue_invoke_async(me->_queue, ctx, func);
    else
        func(ctx);
}

static void _mediator_invoke_async_d(MFLOW_MEDIATOR *mediator, void *data, size_t size, void (* func)(void *data))
{
    AENGINE *me = (AENGINE *)mediator->_owner;
    if ((me->_state == AENGINE_STATE_RUNNING) || (me->_state == AENGINE_STATE_PAUSED))
        athread_queue_invoke_async_d(me->_queue, data, size, func);
    else
        func(data);
}

/*** init & dealloc ***/

static void _dealloc(C3_OBJ *zz_me)
{
    AENGINE *me = (AENGINE *)zz_me;

    aengine_set_dest_input(me, NULL);

    assert(!me->_dest_seg.left);
    assert(!me->_dest_seg.right);

    free(me->_dest_mono_buf);
    me->_dest_mono_buf = NULL;
    free(me->_dest_silent_buf);
    me->_dest_silent_buf = NULL;

    if (me->_source_buf_pool) {
        c3_release(&me->_source_buf_pool->z.z.z);
        me->_source_buf_pool = NULL;
    }

    c3_release(&me->_source_clock_adjuster->z);
    c3_release(&me->_dest_clock_adjuster->z);

    mflow_pcm_pushable_array_deinit(&me->_source_outputs);

    c3_release(&me->_queue->z);
    me->_queue = NULL;

    _c3_obj_dealloc(&me->z);
}

void _aengine_init(AENGINE *me, const AENGINE_PARAMS *params)
{
    _c3_obj_init(&me->z);
    memset((char *)me + sizeof(me->z), 0, sizeof(*me) - sizeof(me->z));
    me->z.clazz = &aengine_class;

    me->z.dealloc = _dealloc;

    _dest_buffer_enlarge(me, AENGINE_DEFAULT_BUF_SLEN);

    me->_sample_rate = 48000;
    me->_source_stereo = true;
    me->_new_source_stereo = true;
    me->_resync_level = _RESYNC_LEVEL;

    mflow_pcm_pushable_array_init(&me->_source_outputs);

    me->_source_buf_pool = mflow_pcm_buf_pool_create(10, AENGINE_DEFAULT_BUF_SLEN);

    me->_source_clock_adjuster = mflow_clock_adjuster_create();
    mflow_clock_adjuster_set_name(me->_source_clock_adjuster, "source");
    mflow_clock_adjuster_set_pool(me->_source_clock_adjuster, me->_source_buf_pool);

    me->_dest_clock_adjuster = mflow_clock_adjuster_create();
    mflow_clock_adjuster_set_name(me->_dest_clock_adjuster, "dest");

    me->_queue = athread_queue_create();
}

AENGINE *aengine_create(const AENGINE_PARAMS *params)
{
    AENGINE *me;

    me = malloc(sizeof(AENGINE));
    if (!me)
        GMU_FATAL_GEN(GMU_ERR_SYSTEM, "cannot allocate an aengine object");
    _aengine_init(me, params);
    return me;
}

MFLOW_MEDIATOR *aengine_create_mediator(AENGINE *me)
{
    MFLOW_MEDIATOR *ret = mflow_mediator_create();
    mflow_mediator_set_owner(ret, &me->z);
    ret->invoke_async = _mediator_invoke_async;
    ret->invoke_async_d = _mediator_invoke_async_d;
    return ret;
}

void aengine_set_source_pool(AENGINE *me, MFLOW_PCM_BUF_POOL *pool)
{
    if (me->_source_buf_pool)
        c3_release(&me->_source_buf_pool->z.z.z);
    me->_source_buf_pool = pool;
    if (me->_source_buf_pool)
        c3_retain(&me->_source_buf_pool->z.z.z);

    mflow_clock_adjuster_set_pool(me->_source_clock_adjuster, me->_source_buf_pool);
}

void aengine_set_clock_drift_compensation_enabled(AENGINE *me, bool enabled)
{
    me->_enable_drift_compensation = enabled;
}

void aengine_set_dest_input(AENGINE *me, MFLOW_PCM_PULLABLE *pullable)
{
    if (me->_dest_input)
        c3_release(&me->_dest_input->z);

    me->_dest_input = pullable;

    if (me->_dest_input)
        c3_retain(&me->_dest_input->z);
}

void aengine_begin_configuration(AENGINE *me)
{
    me->_conf_level++;
}

void aengine_commit_configuration(AENGINE *me)
{
    OSStatus status;
    bool change_unit = false;

    me->_conf_level--;

    if (me->_conf_level == 0) {

        printf("aengine: commit configuration\n");

        bool restart = false;

        if (me->_new_source_stereo != me->_source_stereo)
            restart = true;
        if (me->_new_dest_stereo != me->_dest_stereo)
            restart = true;
        if (me->_new_voice_processing != me->_voice_processing) {
            restart = true;
            change_unit = true;
        }
        if (me->_new_agc != me->_agc) {
            restart = true;
            change_unit = true;
        }
        if (me->_new_reconf) {
            restart = true;
            change_unit = true;
        }

        if ((me->_state == AENGINE_STATE_RUNNING) && restart) {
            status = AudioOutputUnitStop(me->_io_unit);
            _CHECK_OSSTATUS(status);
            status = AudioUnitUninitialize(me->_io_unit);
            _CHECK_OSSTATUS(status);
        }

        if (restart) {
            me->_source_stereo = me->_new_source_stereo;
            me->_dest_stereo = me->_new_dest_stereo;
            me->_voice_processing = me->_new_voice_processing;
            me->_agc = me->_new_agc;
            me->_new_reconf = false;

            if (me->_state == AENGINE_STATE_RUNNING)
                _update_formats(me);
        }

        if ((me->_state == AENGINE_STATE_RUNNING) && restart) {
            if (change_unit) {
                status = AudioComponentInstanceDispose(me->_io_unit);
                _CHECK_OSSTATUS(status);
                _create_io_unit(me);
            }

            status = AudioUnitInitialize(me->_io_unit);
            if (status != noErr) {
                athread_queue_pump(me->_queue);

                me->_state = AENGINE_STATE_IN_ERROR;
                me->_error = -1;
                me->_os_error = status;
                me->_error_context = 1;
                return;
            }

            // print formats
            printf("aengine: audio-source-out:\n");
            _print_format(me->_io_unit, kAudioUnitScope_Output, 1);
            printf("aengine: audio-dest-in:\n");
            _print_format(me->_io_unit, kAudioUnitScope_Input, 0);

            status = AudioOutputUnitStart(me->_io_unit);
            if (status != noErr) {
                AudioUnitUninitialize(me->_io_unit);
                athread_queue_pump(me->_queue);

                me->_state = AENGINE_STATE_IN_ERROR;
                me->_error = -1;
                me->_os_error = status;
                me->_error_context = 2;
                return;
            }
        }
    }
}

/**
 * aengine_set_sample_rate
 * aengine must be in state AENGINE_STATE_IDLE in order to change the sample rate (stop it if running)
 */
void aengine_set_sample_rate(AENGINE *me, int sample_rate)
{
    assert(me->_state == AENGINE_STATE_IDLE);
    me->_sample_rate = sample_rate;
}

void aengine_set_source_stereo(AENGINE *me, bool stereo)
{
    aengine_begin_configuration(me);
    me->_new_source_stereo = stereo;
    aengine_commit_configuration(me);
}

void aengine_set_dest_stereo(AENGINE *me, bool stereo)
{
    aengine_begin_configuration(me);
    me->_new_dest_stereo = stereo;
    aengine_commit_configuration(me);
}

void aengine_set_voice_processing(AENGINE *me, bool voice_processing)
{
    aengine_begin_configuration(me);
    me->_new_voice_processing = voice_processing;
    me->_new_agc = me->_new_voice_processing ? true : false;
    aengine_commit_configuration(me);
}

void aengine_force_reconfiguration(AENGINE *me)
{
    aengine_begin_configuration(me);
    me->_new_reconf = true;
    aengine_commit_configuration(me);
}

void aengine_start(AENGINE *me)
{
    OSStatus status;

    assert(me->_state == AENGINE_STATE_IDLE);
    assert(!me->_error);

    mflow_clock_adjuster_reset(me->_source_clock_adjuster);
    mflow_clock_adjuster_reset(me->_dest_clock_adjuster);

    _create_io_unit(me);

    me->_dest_first = true;
    me->_source_first = true;
    me->_time_ref_first = true;
    mflow_atime_ref_init(&me->_time_ref, 0, me->_sample_rate);

    status = AudioUnitInitialize(me->_io_unit);
    if (status != noErr) {
        me->_state = AENGINE_STATE_IN_ERROR;
        me->_error = -1;
        me->_os_error = status;
        me->_error_context = 3;
        return;
    }

    // print formats
    printf("aengine: audio-source-out:\n");
    _print_format(me->_io_unit, kAudioUnitScope_Output, 1);
    printf("aengine: audio-dest-in:\n");
    _print_format(me->_io_unit, kAudioUnitScope_Input, 0);

    status = AudioOutputUnitStart(me->_io_unit);
    if (status != noErr) {
        AudioUnitUninitialize(me->_io_unit);
        me->_state = AENGINE_STATE_IN_ERROR;
        me->_error = -1;
        me->_os_error = status;
        me->_error_context = 4;
        return;
    }

    me->_state = AENGINE_STATE_RUNNING;
}

void aengine_stop(AENGINE *me)
{
    OSStatus status;

    assert(me->_state != AENGINE_STATE_IDLE);

    if (me->_state == AENGINE_STATE_RUNNING) {
        /*
         * AudioOutputUnitStop() blocks until the render thread is stopped.
         * See http://lists.apple.com/archives/coreaudio-api/2005/Dec/msg00048.html
         */
        status = AudioOutputUnitStop(me->_io_unit);
        _CHECK_OSSTATUS(status);
        status = AudioUnitUninitialize(me->_io_unit);
        _CHECK_OSSTATUS(status);
        status = AudioComponentInstanceDispose(me->_io_unit);
        _CHECK_OSSTATUS(status);

        athread_queue_pump(me->_queue);
    }

    if (!me->_dest_first)
        mflow_pcm_pullable_end(me->_dest_input);
    if (!me->_source_first)
        mflow_pcm_pushable_array_end(&me->_source_outputs);

    me->_state = AENGINE_STATE_IDLE;
    me->_error = 0;
    me->_os_error = 0;
    me->_error_context = 0;
}

/**
 * aengine_pause()
 * Stops (removes) the audio unit wich allows the audio session (in RLAudioEngine) to be set inactive and changed. This method shall
 * be followed by the execution of aengine_resume(). Execute this method only when the audio engine is running.
 * The audio pipeline is not touched by this method (no end , begin, nor ts reinitialisation) and stays active
 */
void aengine_pause(AENGINE *me)
{
    OSStatus status;
    assert(me->_state != AENGINE_STATE_IDLE);

    if ((me->_state == AENGINE_STATE_IN_ERROR) || (me->_state == AENGINE_STATE_PAUSED))
        return;

    me->_state = AENGINE_STATE_PAUSED;

    /*
     * AudioOutputUnitStop() blocks until the render thread is stopped.
     * See http://lists.apple.com/archives/coreaudio-api/2005/Dec/msg00048.html
     */
    status = AudioOutputUnitStop(me->_io_unit);
    _CHECK_OSSTATUS(status);
    status = AudioUnitUninitialize(me->_io_unit);
    _CHECK_OSSTATUS(status);
    status = AudioComponentInstanceDispose(me->_io_unit);
    _CHECK_OSSTATUS(status);
}

/**
 * aengine_resume()
 * Run this method only after a call to aengine_pause() to recreate an audio unit and start it. See comment on aengine_pause().
 */
void aengine_resume(AENGINE *me)
{
    OSStatus status;

    if (me->_state != AENGINE_STATE_PAUSED)
        return;

    _create_io_unit(me);

    status = AudioUnitInitialize(me->_io_unit);
    if (status != noErr) {
        me->_state = AENGINE_STATE_IN_ERROR;
        me->_error = -1;
        me->_os_error = status;
        me->_error_context = 7;
        return;
    }

    // print formats
    printf("aengine: audio-source-out:\n");
    _print_format(me->_io_unit, kAudioUnitScope_Output, 1);
    printf("aengine: audio-dest-in:\n");
    _print_format(me->_io_unit, kAudioUnitScope_Input, 0);

    status = AudioOutputUnitStart(me->_io_unit);
    if (status != noErr) {
        AudioUnitUninitialize(me->_io_unit);
        me->_state = AENGINE_STATE_IN_ERROR;
        me->_error = -1;
        me->_os_error = status;
        me->_error_context = 8;
        return;
    }

    me->_state = AENGINE_STATE_RUNNING;
}

/**
 * aengine_recover()
 * Execute this method if the audio engine is running but in error (AENGINE_STATE_IN_ERROR returned by aengine_get_state())
 * The audio pipeline is not touched executing this method (no end , begin, nor ts reinitialisation) and stays active
 */
void aengine_recover(AENGINE *me)
{
    OSStatus status;

    if (me->_state != AENGINE_STATE_IN_ERROR)
        return;

    status = AudioComponentInstanceDispose(me->_io_unit);
    _CHECK_OSSTATUS(status);

    _create_io_unit(me);

    status = AudioUnitInitialize(me->_io_unit);
    if (status != noErr) {
        me->_state = AENGINE_STATE_IN_ERROR;
        me->_error = -1;
        me->_os_error = status;
        me->_error_context = 5;
        return;
    }

    // print formats
    printf("aengine: audio-source-out:\n");
    _print_format(me->_io_unit, kAudioUnitScope_Output, 1);
    printf("aengine: audio-dest-in:\n");
    _print_format(me->_io_unit, kAudioUnitScope_Input, 0);

    status = AudioOutputUnitStart(me->_io_unit);
    if (status != noErr) {
        AudioUnitUninitialize(me->_io_unit);
        me->_state = AENGINE_STATE_IN_ERROR;
        me->_error = -1;
        me->_os_error = status;
        me->_error_context = 6;
        return;
    }

    // clear errors
    me->_error = 0;
    me->_os_error = 0;
    me->_error_context = 0;
    me->_state = AENGINE_STATE_RUNNING;
}

enum aengine_state aengine_get_state(AENGINE *me)
{
    return me->_state;
}

int aengine_get_error(AENGINE *me)
{
    return me->_error;
}

char *aengine_get_error_message(AENGINE *me)
{
    char *ret = NULL;
    asprintf(&ret, "error=%d context=%d os_error=%d os_name=%s",
             me->_error, me->_error_context, me->_os_error, _osstatus2str(me->_os_error));
    return ret;
}

void aengine_add_source_output(AENGINE *me, MFLOW_PCM_PUSHABLE *pushable)
{
    mflow_pcm_pushable_array_add_output(&me->_source_outputs, pushable);
}

void aengine_remove_source_output(AENGINE *me, MFLOW_PCM_PUSHABLE *pushable)
{
    mflow_pcm_pushable_array_remove_output(&me->_source_outputs, pushable);
}

/**
 * Multi-thread safe.
 */
void aengine_set_source_muted(AENGINE *me, bool muted)
{
    me->_source_muted = muted;
}

/**
 * Multi-thread safe.
 */
void aengine_set_dest_muted(AENGINE *me, bool muted)
{
    me->_dest_muted = muted;
}

float aengine_get_source_clock_drift(AENGINE *me)
{
    // WARNING: not 100% thread-safe
    int64_t time = me->_source_sts;
    if (time < 100000)
        return 0;
    int shift = mflow_clock_adjuster_get_real_shift(me->_source_clock_adjuster);
    return (float)((double)shift / (double)time);
}

float aengine_get_dest_clock_drift(AENGINE *me)
{
    // WARNING: not 100% thread-safe
    int64_t time = me->_source_sts;
    if (time < 100000)
        return 0;
    int shift = mflow_clock_adjuster_get_real_shift(me->_dest_clock_adjuster);
    return (float)((double)shift / (double)time);
}
