"global-action.cancel.title" = "Cancelar";
"global-action.skip.title" = "Skip";
"global-action.ok.title" = "OK";
"global-action.dismiss.title" = "Dismiss";
"global-action.done.title" = "Done";
"global-action.continue.title" = "Seguir";
"global-action.close.title" = "Cerrar";
"global-action.remove.title" = "Remove";
"global-text.error.title" = "Error";
"global-text.warning.title" = "Advertencia";
"global-text.slash-separator" = "/";
"global-text.yes" = "Yes";
"global-text.no" = "No";
"global-text.none" = "None";
"global-action.reset.title" = "Reset";
"brand.kit" = "Brand Kit";
"media-lib.text.Journal" = "Diario";
"media-lib.text.Angle" = "Ángulo";
"media-lib.text.Audio" = "Audio";
"media-lib.text.Part" = "Parte";
"filter-list.title" = "Efectos";
"filter-list.transition.title" = "Transiciones";
"filter-list.transition.reorder" = "Reorder and Customize";
"filter-list.transition.text" = "Reorder or hide transitions you don't use to personalize your view.";
"filter-list.multi-view.title" = "Múlti-Vistas";
"filter-list.vfilter-none.title" = "Ninguna";
"filter-list.vfilter-cut.title" = "Cortar";
"filter-list.vfilter-crossfade.title" = "Disolución cruzada";
"filter-list.vfilter-cube.title" = "Cubo";
"filter-list.vfilter-twist.title" = "Giro";
"filter-list.vfilter-wipe.title" = "Limpiar";
"filter-list.vfilter-cockpit.title" = "Dashboard";
"filter-list.vfilter-flap.title" = "Solapa";
"filter-list.vfilter-slot.title" = "Ranuras";
"filter-list.vfilter-pinp.title" = "Imagen en Imagen";
"filter-list.vfilter-split-view.title" = "Pantalla Dividida";
"filter-list.vfilter-grid.title" = "Cuadrícula";
"filter-list.vfilter-gen.title" = "Relación de Aspecto de Fotograma";
"filter-list.vfilter-input.title" = "Relación de Aspecto de Fotograma";
"vfilter-prop.duration.title" = "Duración de transición";
"vfilter-prop.bg_color.title" = "Color de Fondo";
"vfilter-prop.bg_image.title" = "Imagen de Fondo";
"vfilter-prop.bg_alpha.title" = "Opacidad de la Imagen de Fondo";
"vfilter-prop.direction.title" = "Dirección";
"vfilter-prop.test_pattern.title" = "Patrón de Prueba";
"vfilter-prop.border_color.title" = "Color del Borde";
"vfilter-prop.border_thickness.title" = "Grosor del Borde";
"vfilter-prop.corner_radius.title" = "Radio de Redondeo";
"vfilter-prop.pos_x.title" = "Posición X";
"vfilter-prop.pos_y.title" = "Posición Y";
"vfilter-prop.pos_z.title" = "Posición Z";
"vfilter-prop.stopwatch.title" = "Stopwatch";
"vfilter-prop.timer.title" = "Timer";
"vfilter-prop.yaw.title" = "Guiñada";
"vfilter-prop.pitch.title" = "Inclination";
"vfilter-prop.roll.title" = "Rodar";
"vfilter-prop.center.title" = "Centrar";
"vfilter-prop.frame_crop_h.title" = "Recorte Horizontal";
"vfilter-prop.frame_crop_v.title" = "Recorte Vertical";
"vfilter-prop.gap_h.title" = "Espacio Horizontal";
"vfilter-prop.gap_v.title" = "Espacio Vertical";
"vfilter-prop.reflection.title" = "Reflexión";
"vfilter-prop.reflection_gap.title" = "Brecha de Reflexión";
"vfilter-prop.reframing_mode.title" = "Cuadro";
"vfilter-prop.bg_mode.title" = "Capas";
"vfilter-prop.bg_mode.enum.source.title" = "Pantalla Completa";
"vfilter-prop.bg_mode.enum.overlay.title" = "Superposición";
"vfilter-prop.angle.title" = "Ángulo";
"vfilter-prop.angle.flap.title" = "Ángulo de aleta";
"vfilter-prop.frame_format.title" = "Formato";
"vfilter-prop.frame_format.flap.title" = "Formato de Solapa";
"vfilter-prop.separator_direction.title" = "Dirección del Separador";
"vfilter-prop.separator_position.title" = "Posición del Separador";
"vfilter-prop.separator_thickness.title" = "Espesor del Separador";
"vfilter-prop.separator_color.title" = "Color del Separador";
"vfilter-prop.spacing.title" = "Espaciado";
"vfilter-prop.uniform_gap.title" = "Espacio Uniforme";
"vfilter-prop.bottom_margin.title" = "Margen inferior";
"vfilter-prop.view_angle.cockpit.title" = "Perspectiva";
"vfilter-prop.dark_color_transp.title" = "Fundido de Negro a Transparente";
"vfilter-prop.chroma_key.header" = "Chroma Key";
"vfilter-prop.chroma_key.title" = "Enable Chroma Key";
"vfilter-prop.chroma_key_color.title" = "Key Color";
"vfilter-prop.chroma_key_range.title" = "Range";
"vfilter-prop.chroma_key_softness.title" = "Softness";
"vfilter-prop.chroma_key_edge_desat.title" = "Edge Desaturation";
"vfilter-prop.chroma_key_alpha_crop.title" = "Alpha Crop";
"vfilter-prop.chroma_key_bg_image.title" = "Background Image";
"UIFrameFormatPropViewController.title" = "Formato";
"PropBundleEditor.disabled-because-no-reframing.msg" = "Estas propiedades no tienen ningún efecto porque el contenido se ajusta perfectamente al marco de salida.";
"PropBundleEditorViewController.restore-defaults-button.title" = "Restaurar la Configuración Predeterminada";
"source-list.title" = "Cámaras";
"source-list.inputs.title" = "Entradas";
"source-list.angle.title" = "Ángulo";
"source-list.audio.title" = "Audio";
"source-list.other-item.title" = "Agregue una Fuente";
"source-list.built-in-source.title" = "Fuentes Integradas";
"source-list.network-source.title" = "Fuentes en su Red Wi-Fi";
"source-list.alert.busy.msg.text" = "%@ está ocupado";
"source-list.alert.bad-version.msg.text" = "%@ versión no coincide";
"source-list.alert.bad-format.msg.text" = "El formato %@ no es compatible";
"source-list.alert.bad-prod.msg.text" = "%@ no soportado";
"source-list.alert.ok-button.text" = "OK";
"source-list.multi-src-name.fmt" = "%@ en %@";
"source-list.not-available-item-local.title" = "No disponible";
"source-list.not-available-item-wifi.title" = "Ninguna";
"SourceListIcons.menu.title" = "Fuentes";
"SourceListIcons.source.camera" = "Cámara";
"SourceListIcons.source.mobile-screen" = "Pantalla Móvil";
"SourceListIcons.source.desktop-screen" = "Pantalla de Computadora";
"SourceListIcons.source.connect-url" = "Conectarse por URL";
"SourceListIcons.source.videochat" = "Videochat";
"SourceInformationsTableViewController.instructions.header" = "Instrucciones";
"SourceInformationsTableViewController.title.camera" = "Cámara";
"SourceInformationsTableViewController.instructions.camera-1" = "Abra Switcher Studio en su dispositivo iOS adicional.";
"SourceInformationsTableViewController.instructions.camera-2" = "Toca \"Compartir este dispositivo\".";
"SourceInformationsTableViewController.instructions.camera-3" = "Toca \"Compartir esta cámara\".";
"SourceInformationsTableViewController.instructions.camera-4" = "Toque el nombre del dispositivo en la lista de Fuentes en su dispositivo principal.";
"SourceInformationsTableViewController.camera-extra" = "If you're connected on your additional iOS device, the \"Share Camera or Screen\" option is available in the Menu page";
"SourceInformationsTableViewController.title.mobile-screen" = "Pantalla Móvil";
"SourceInformationsTableViewController.instructions.mobile-screen-1" = "Abra Switcher Studio en su dispositivo iOS adicional.";
"SourceInformationsTableViewController.instructions.mobile-screen-2" = "Toca \"Compartir este dispositivo\".";
"SourceInformationsTableViewController.instructions.mobile-screen-3" = "Toca \"Compartir esta pantalla\".";
"SourceInformationsTableViewController.instructions.mobile-screen-4" = "Toque el nombre del dispositivo en la lista de Fuentes en su dispositivo principal.";
"SourceInformationsTableViewController.title.desktop-screen" = "Pantalla de escritorio";
"SourceInformationsTableViewController.instructions.desktop-screen-1" = "Instale Switcher Cast en su computadora.";
"SourceInformationsTableViewController.instructions.desktop-screen-2" = "Conecte su computadora a la misma red Wi-Fi que su(s) dispositivo(s) iOS.";
"SourceInformationsTableViewController.instructions.desktop-screen-3" = "Inicie Switcher Cast para permitir compartir la pantalla de la computadora.";
"SourceInformationsTableViewController.instructions.desktop-screen-4" = "Toque la(s) fuente(s) de su computadora en la lista de Fuentes en su dispositivo principal.";
"SourceInformationsTableViewController.connect-url.header" = "¿No ves tu dispositivo en la lista?";
"SourceInformationsTableViewController.connect-url.title" = "Conectarse por URL";
"SourceInformationsTableViewController.download-link.title" = "Compartir enlace de descarga";
"SourceInformationsTableViewController.download-link.desktop-win" = "Switcher Cast para Windows";
"SourceInformationsTableViewController.download-link.desktop-mac" = "Switcher Cast para MacOS";
"CameraUrlInformationsTableViewController.instructions.header" = "Instrucciones";
"CameraUrlInformationsTableViewController.instructions.camera-1" = "Abra la aplicación Switcher en su dispositivo iOS principal.";
"CameraUrlInformationsTableViewController.instructions.camera-2" = "Inicie sesión y toque \"Usar como Switcher\".";
"CameraUrlInformationsTableViewController.instructions.camera-3" = "Toque la pestaña Entradas (icono de cámara de video).";
"CameraUrlInformationsTableViewController.instructions.camera-4" = "Si su dispositivo no aparece como fuente, toque \"Agregar Fuente\".";
"CameraUrlInformationsTableViewController.instructions.camera-5" = "Toca \"Conectar por URL\".";
"CameraUrlInformationsTableViewController.instructions.camera-6" = "Ingrese la URL que se muestra en este dispositivo iOS adicional.";
"RecordingInterruptedAlertController.msg" = "La desconexión de una cámara interrumpirá la grabación de audio y video en ese dispositivo.";
"RecordingInterruptedAlertController.ok" = "OK";
"RecordingInterruptedAlertController.dont-show-again" = "No volver a mostrar";
"URLConnectionController.title.text" = "Conectarse a URL";
"URLConnectionController.url.title.text" = "Ingrese la URL de la cámara:";
"URLConnectionController.history.title.text" = "Recientemente usado:";
"URLConnectionController.history.empty.text" = "Ninguna";
"URLConnectionController.action-connect.text" = "Conectar";
"URLConnectionController.alert.no-wifi.text" = "Sin red Wi-Fi";
"URLConnectionController.alert.bad-url.text" = "URL Incorrecta";
"URLConnectionController.alert.itself.text" = "No puedes conectarte a ti mismo";
"IOSAVProvider.camera.title" = "Cámara Integrada";
"IOSAVProvider.back-camera.title" = "Camara Trasera";
"IOSAVProvider.front-camera.title" = "Cámara Frontal";
"LocalAudio.source.title" = "Micrófono Incorporado/Entrada de Audio";
"MixerViewController.program.text" = "En Vivo";
"MixerViewController.preview.text" = "Avance";
"MixerViewController.sources.text" = "Fuentes";
"MixerViewController.prev-to-prog-button.left.text" = "Avance";
"MixerViewController.prev-to-prog-button.right.text" = "En Vivo";
"MixerViewController.stream-validation.msg" = "Validación de la Transmisión en Curso...";
"CheckDevice.generic-alert.msg" = "%@ te permite filmar desde múltiples ángulos con múltiples dispositivos en tu red Wi-Fi. Para ello, es necesario el acceso a la cámara, micrófono y a la red local. Puedes habilitar el acceso en Ajustes > Switcher.";
"CheckDevice.settings.title" = "Ajustes";
"CheckDevice.no-local-network.title" = "Switcher no tiene acceso a tu red local";
"CheckDevice.no-microphone.title" = "Switcher no tiene acceso a tu micrófono";
"CheckDevice.no-camera.title" = "Switcher no tiene acceso a tu cámara";
"MediaViewController.byte-unit" = "B";
"MediaViewController.media-state.searching.title" = "Buscando...";
"MediaViewController.media-state.missing.title" = "Faltante";
"MediaViewController.media-state.missing-hostname.title" = "Faltante (grabado en %@)";
"MediaViewController.media-state.missing-connection.title" = "Faltante (conecte %@)";
"MediaViewController.media-state.cancelled.title" = "Cancelado";
"MediaViewController.media-state.cancelling.title" = "Cancelando...";
"MediaViewController.media-state.error.title" = "Error";
"MediaViewController.media-state.out-of-mem.title" = "Sin memoria (RAM)";
"MediaViewController.media-state.out-of-storage.title" = "No hay suficiente almacenamiento disponible en su dispositivo";
"MemoryViewController.title" = "Memoria";
"MemoryViewController.available-label.text" = "Memoria Disponible";
"MemoryViewController.enlarge-button.text" = "Agrandar";
"MemoryViewController.build-button.text" = "Construir Composición";
"MemoryViewController.ignore-switch.text" = "Ignorar Límites de Memoria";
"MemoryViewController.help" = "Si se queda sin memoria mientras procesa la composición, cierre todas las aplicaciones, apague el dispositivo y vuelva a encenderlo. Para obtener más información, por favor visite nuestro sitio web.";
"MemoryViewController.byte-unit" = "B";
"CameraControl.zoom" = "Zoom";
"CameraControl.focus" = "Enfoque";
"CameraControl.expo" = "Exposición";
"CameraControl.wbal" = "Balance de Blancos";
"CameraControl.light" = "Luz LED";
"CameraControl.stab" = "Estabilización";
"CameraControl.noFocusWhenLive" = "Bloquear el Enfoque Automático en Vivo";
"CameraControl.resetToDefaults" = "Reset to Defaults";
"CameraControl.saveSettings" = "Save Camera Settings";
"CameraControl.vptz.restore-default-presets" = "Restore Default Presets";
"CameraControl.vptz.enable" = "Enable ePTZ";
"CameraControl.vptz.unsupported-iphone-camera-tip" = "This feature is not compatible with iPhone's build-in camera. Please select a different camera.";
"CameraControl.tab.vptz" = "ePTZ";
"CameraControl.tab.controls" = "Controls";
"AudioViewController.title" = "Audio";
"AudioViewController.main-grp.title" = "Canal Principal";
"AudioViewController.main-grp.switcher.title" = "Audio para Grabación/Transmisión";
"AudioViewController.aux-grp.title" = "Canales auxiliares";
"AudioViewController.aux-grp.footer" = "Los canales auxiliares se graban pero no se incluyen en la composición final.";
"AudioViewController.global-settings-grp.title" = "Opciones";
"AudioViewController.global-settings-grp.footer" = "El monitoreo de audio le permite escuchar el audio grabado/transmitido a través de sus auriculares u otra salida actualmente activa. En algunas interfaces de audio, esto podría generar eco.\nEl modo de voz proporciona funciones de procesamiento de audio como la cancelación de eco. Es necesario para AirPods y auriculares Bluetooth. Desactive el modo de voz cuando utilice dispositivos de audio externos o con cable o reproduzca música.";
"AudioViewController.monitoring.title" = "Monitoreo de Audio";
"AudioViewController.voice-mode.title" = "Modo de Voz";
"CameraControlTableViewController.title" = "Control de Cámara";
"ResyncDelayLevel.0.long" = "Modo de Baja Latencia";
"ResyncDelayLevel.1.long" = "Modo Wi-Fi Estándar";
"ResyncDelayLevel.2.long" = "Modo Wi-Fi Reforzado";
"ResyncDelayLevel.0.short" = "Baja Latencia";
"ResyncDelayLevel.1.short" = "Modo Wi-Fi Estándar";
"ResyncDelayLevel.2.short" = "Wi-Fi Reforzado";
"ResyncDelayLevel.0.description" = "En el modo de baja latencia, el video se muestra muy poco después de ser capturado, pero la calidad puede verse comprometida. En este modo, no se permiten la estabilización ni la grabación 4K.";
"ResyncDelayLevel.1.description" = "El modo estándar es la mejor compensación entre latencia y calidad de video. Requiere una red Wi-Fi robusta.";
"ResyncDelayLevel.2.description" = "Este modo no compromete la calidad del video, pero introduce un retraso de 1 segundo en el video mostrado para evitar la mayoría de las limitaciones introducidas por las redes Wi-Fi que pueden ser débiles, sobrecargadas o sujetas a interferencias.";
"ResyncDelayLevel.2.description-temporary-addon" = "(temporal)";
"OutputViewController.title" = "Salidas";
"OutputViewController.outputs.title" = "Salidas";
"OutputViewController.outputs.camo.title" = "A Zoom, Meet, Teams, Skype, ...";
"OutputViewController.external-display.source.title" = "A HDMI/AirPlay";
"OutputViewController.external-display.not-allowed.title" = "HDMI / AirPlay";
"OutputViewController.external-display.not-allowed.msg" = "Para usar HDMI o AirPlay, considere usar Switcher Studio";
"OutputViewController.external-display.not-allowed.more-info" = "Más Información";
"OutputViewController.streaming.title" = "Transmitiendo";
"OutputViewController.streamingOptions.title" = "Opciones de Transmisión";
"OutputViewController.streaming.recordonly.title" = "Sólo Grabar";
"OutputViewController.streaming.recordonly-autosave.title" = "Recording";
"OutputViewController.streaming.simulcast.title" = "Livestreaming";
"OutputViewController.practice.mode.title" = "Practice";
"OutputViewController.streaming.selectedLivestream" = "Active Livestream";
"OutputViewController.streaming.selectAnotherLivestream" = "Select Another Livestream";
"OutputViewController.event.noevents.header" = "No Livestreams";
"OutputViewController.event.noevents.body" = "Create your livestreams in advance and they will appear here.";
"OutputViewController.streaming.generic.title" = "RTMP Personalizado";
"OutputViewController.event.noevent" = "No Livestreams";
"OutputViewController.event.noevent.subtitle" = "Create one to get started";
"OutputViewController.event.nochannel" = "No Channel";
"OutputViewController.event.nochannel.subtitle" = "Create new or select existing";
"OutputViewController.event.golive" = "Go Live Now";
"OutputViewController.event.button.create" = "Create";
"OutputViewController.event.button.change" = "Change";
"OutputViewController.event.destinations %d" = "%d destinations";
"OutputViewController.event.destinations.sing" = "1 destination";
"OutputViewController.event.share" = "Share Link";
"OutputViewController.rec.title" = "Grabación";
"OutputViewController.rec.director-mode.title" = "Modo Director";
"OutputViewController.rec.rtmp.reconnect.title" = "Reconexión RTMP";
"OutputViewController.rec.options.title" = "Ajustes Avanzados";
"OutputViewController.rec.tip.msg" = "Habilite el Modo Director para grabar videos de alta calidad.";
"OutputViewController.isocam-warning.msg" = "Habilitar el \"Modo Director\" deshabilitará la cámara incorporada en este dispositivo. Cuando inicie su producción, cada cámara hará su propia grabación. Luego, se puede hacer un video completo de 1080p HD o 4K utilizando los archivos de origen. Asegúrese de tener suficiente espacio de almacenamiento en cada dispositivo antes de continuar. Visite nuestra base de conocimientos para obtener más información.";
"OutputViewController.wi-fi.title" = "Comunicación de Cámara Wi-Fi";
"OutputViewController.wi-fi.resync-delay.title" = "Modo de Optimización de Wi-Fi";
"OutputViewController.refresh-control.title" = "Sincronizando con el Dashboard...";
"OutputViewController.display-selection.program.short" = "En Vivo";
"OutputViewController.display-selection.mirroring.short" = "Todos";
"OutputViewController.resolution.recording" = "Video Quality";
"OutputViewController.resolution.streaming" = "Stream Quality";
"OutputSourcesTableViewController.display-selection.mirroring.long" = "Salida de la Pantalla Completa";
"OutputSourcesTableViewController.display-selection.program.long" = "Salida de la Transmisión en Vivo";
"OutputSourcesTableViewController.airplay-tip.msg" = "Para habilitar o deshabilitar AirPlay, use el botón correspondiente en el Centro de Control. La duplicación debe estar activada.";
"OutputSourcesTableViewController.underscan.title" = "Underscan";
"OutputSourcesTableViewController.underscan.auto.title" = "Auto";
"OutputSourcesTableViewController.rotation.title" = "Rotación";
"LimitedAccessOutputViewController.header" = "Cuenta Caducada";
"LimitedAccessOutputViewController.body" = "Su cuenta ha caducado. Póngase en <NAME_EMAIL> si tiene alguna pregunta.";
"LimitedAccessOutputViewController.iap.header" = "No subscription";
"LimitedAccessOutputViewController.iap.body" = "You need an active subscription to access Outputs settings.";
"BCProfileListViewController.title" = "Canales RTMP";
"BCProfileListViewController.new-profile.name" = "Mi Canal";
"BCProfileListViewController.new-profile.button" = "New Channel";
"BCProfileListViewController.no-profile.msg" = "Toque [+] para agregar un canal.";
"BCProfileListViewController.new.msg" = "Swipe down to refresh";
"BCProfileListViewController.refresh.button" = "Refresh";
"BCProfileListViewController.delete" = "Delete";
"RTMPDefViewController.title" = "Parámetros de RTMP";
"RTMPDefViewController.name.title" = "Apodo del Canal";
"RTMPDefViewController.name.placeholder" = "Enter channel name";
"RTMPDefViewController.url.title" = "URL del Servidor";
"RTMPDefViewController.url.placeholder" = "Enter your stream URL";
"RTMPDefViewController.stream.title" = "Clave / ID de transmisión";
"RTMPDefViewController.stream.placeholder" = "Enter your stream key or ID";
"RTMPDefViewController.options.title" = "Opciones de Transmisión";
"RTMPDefViewController.vformat.title" = "Vídeo Resolución";
"RTMPDefViewController.vbitrate.title" = "Vídeo Bitrate";
"RTMPDefViewController.aformat.title" = "Formato de Audio";
"RTMPDefViewController.abitrate.title" = "Audio Bitrate";
"RTMPDefViewController.compat.title" = "Opciones de Compatibilidad";
"RTMPDefViewController.compat-fmle.title" = "Emule Flash Media Live Encoder";
"RTMPDefViewController.speed-test" = "Speed Test";
"RTMPDefViewController.speed-test.title" = "PRUEBA DE VELOCIDAD";
"RTMPDefViewController.speed-test.msg" = "Toque PRUEBA DE VELOCIDAD para configurar OPCIONES DE TRANSMISIÓN según la velocidad de su conexión a Internet actual.";
"RTMPDefViewController.default.short-form.title" = "Predeterminado";
"RTMPDefViewController.default.long-form.title" = "Valor predeterminado";
"RTMPDefViewController.error.title" = "Unable to Save Changes";
"RTMPDefViewController.error.msg" = "Please check your connection and try again.";
"menu.title" = "Perfil";
"library.title" = "Videoteca";
"home.title" = "Home";
"create.new.title.loggedIn" = "Nuevo Video";
"menu.knowledge-base.title" = "Centro de Ayuda";
"menu.settings.title" = "Abrir configuración";
"menu.media.title" = "Videoteca";
"menu.diag.title" = "Datos de Diagnóstico";
"menu.home.title" = "Volver a la Pantalla de Inicio";
"menu.home.confirm.title" = "¿Seguro que quieres salir?";
"menu.home.confirm.comment" = "Esto desconectará todas las cámaras y volverá a la pantalla de inicio.";
"menu.home.confirm.yes" = "Desconectar y Salir";
"menu.home.confirm.no" = "Reanudar";
"menu.account.title" = "Cuenta";
"menu.brand-profile.title" = "Perfil de Marca";
"menu.iphone.studio-mixer.info.title" = "Acerca de los Dispositivos";
"GalileoConnectionViewController.auto-detect.help.text" = "La autodetección le permite conectar Galileo tan pronto como se inicia la aplicación.";
"GalileoConnectionViewController.auto-detect.switch.text" = "Autodetección";
"GalileoConnectionViewController.status.connected" = "Conectado";
"GalileoConnectionViewController.status.unconnected" = "Girar para Conectar";
"GalileoConnectionViewController.title" = "Motrr Galileo";
"ArtworkProdFlow.error.asset-on-icloud.mgs" = "Este activo no está almacenado en su dispositivo.";
"ArtworkProdFlow.error.unsupported-video.mgs" = "Formato de video no admitido. Convierta a H.264.";
"ArtworkProdFlow.error.noavasset.mgs" = "Ningún activo disponible";
"ArtworkProdFlow.error.notvalid.mgs" = "El formato del activo no es válido";
"ArtworkProdFlow.download-switchercloud.msg" = "Descarga desde Switcher Cloud";
"ArtworkProdFlow.download-icloud.msg" = "Descarga desde iCloud";
"ArtworkProdFlow.download-switchercloud.error.msg" = "Descarga desde Switcher Cloud ha fallado";
"ArtworkProdFlow.error.no-phasset.mgs" = "Este activo no está disponible";
"ElementCollectionViewController.title %@" = "Slot %@ Source";
"ElementCollectionViewController.menu2.edit.title" = "Editar";
"ElementCollectionViewController.menu2.duplicate.title" = "Duplicar";
"ElementCollectionViewController.menu2.move.title" = "Reordenar";
"ElementCollectionViewController.menu2.remove.title" = "Eliminar";
"ElementCollectionViewController.menu2.quick-multiview.title" = "Combinar";
"ElementCollectionViewController.multiview-menu.alone.title" = "Solo";
"ElementCollectionViewController.plus-button.tip" = "Toque [+] para agregar cámaras, fotos, videos, títulos, logos y múlti-vistas.";
"TitleListViewController.title" = "Títulos";
"TitleListViewController.generic.title" = "Etiquetas Genéricas";
"TitleListViewController.generic.free-title.title" = "Texto de Pantalla Completa";
"TitleListViewController.generic.free-label.title" = "Superposición de Texto";
"TitleListViewController.specific-layout.title" = "Marcos";
"TitleListViewController.specific-layout.postcard.title" = "Tarjeta Postal";
"TitleListViewController.specific-content.title" = "Plantillas";
"LowerThirdListViewController.title" = "Tercios Inferiores";
"PlatformOverlayListViewController.title" = "Platform Overlays";
"PlatformOverlayBannerContent.title" = "Customize overlays for your social media or donation platforms.";
"BroadcastNotificationListViewController.title" = "Notificaciones de Transmisión";
"VMakerEditorViewController.title.blank" = "Blank";
"VMakerEditorViewController.title.live" = "Live";
"VMakerEditorViewController.picker.blank" = "Blank Canvas";
"VMakerEditorViewController.picker.live" = "Live Output";
"VMakerEditorViewController.tips" = "Para cambiar el tamaño de un gráfico, pellizque y amplíe. Para reposicionar, toque y arrastre.";
"VMakerEditorViewController.apply-on-preview" = "Aplicar en Vista Previa";
"VMakerEditorViewController.stay-on-top" = "Permanecer en Cima";
"VMakerEditorViewController.restore-defaults" = "Restaurar la Configuración Predeterminada";
"VMakerEditorViewController.transition-picker" = "In & Out Transition";
"ElementGroupsViewController.transitions.disabled" = "Disabled";
"VMakerEditorViewController.group-picker" = "Grupo de Activos";
"ArtworkEditorViewController.title" = "Propiedades";
"CameraEditorViewController.title" = "Propiedades de la Cámara";
"CameraEditorViewController.screen-content.title" = "Propiedades de la Pantalla";
"PlayedVideoEditorViewController.title" = "Propiedades de Video";
"PlayedVideoEditorViewController.loop.title" = "Loop";
"PlayedVideoEditorViewController.loop.description" = "When loop is enabled, the video asset will play continually until removed from screen.";
"PlayedVideoEditorViewController.thumbnail-selector.title" = "Selección de Miniaturas";
"PlayedVideoEditorViewController.end-on-last-frame.title" = "Finalizar en el Último Fotograma";
"PlayedVideoEditorViewController.audio-enabled.title" = "Habilitar audio";
"PlayedVideoEditorViewController.trimmer.title" = "Recortadora";
"PlayedVideoEditorViewController.trimmer.from" = "De";
"PlayedVideoEditorViewController.trimmer.to" = "a";
"PlayedVideoEditorViewController.trimmer.end" = "el fin";
"PlayedVideoEditorViewController.volume.title" = "Volumen";
"PlayedVideoEditorViewController.volume.value-db" = "%@ dB";
"PlayedVideoThumbnailSelectorViewController.title" = "Selección de Miniaturas";
"PlayedVideoTrimmerViewController.feature-trimmer-only.title" = "Recortador";
"PlayedVideoTrimmerViewController.infos.from" = "De :";
"PlayedVideoTrimmerViewController.infos.to" = "A :";
"PlayedVideoTrimmerViewController.infos.duration" = "Duración :";
"MultiviewEditorViewController.title" = "Propiedades de vista múltiple";
"MultiviewInputSubtableController.num-of-inputs.title" = "Numero de Fuentes";
"UserAccount.subscription-expired.msg" = "Cuenta inactive - suscripción caducada";
"UserAccount.access-refused.msg" = "Acceso rechazado";
"AccountViewController.buy.no-product.title" = "No hay suscripción disponible";
"AccountViewController.buy.success.title" = "¡Felicidades!";
"AccountViewController.buy.success.message" = "Se realizó su compra";
"AccountViewController.buy.failed.title" = "La compra falló";
"AccountViewController.restore.no-product.title" = "Terminado";
"AccountViewController.restore.no-product.message" = "Ningún producto para restaurar";
"AccountViewController.restore.success.title" = "¡Felicidades!";
"AccountViewController.restore.success.message" = "Suscripciones restauradas";
"AccountViewController.restore.failed.title" = "Fallo";
"AccountViewController.processing.title" = "Procesando";
"AccountViewController.processing.message" = "Esto solo tomará un momento.";
"AccountViewController.restore.button.title" = "Restaurar compra";
"AccountViewController.restore.receipt-warning.title" = "Hubo un problema durante su última transacción. Restaure su suscripción comprada.";
"AccountViewController.restore.logout-warning.title" = "Hubo un problema durante su última transacción, vuelva a la pantalla principal e inicie su sesión.";
"AppleInAppManagerError.noAppleReceiptFound" = "El recibo de Apple no se encontró en el teléfono";
"AppleInAppManagerError.noPurchaseToRestore" = "No hay compra para restaurar";
"AppleInAppManagerError.paymentWasCancelled" = "Se canceló el proceso de compra en la aplicación";
"AppleInAppManagerError.productRequestFailed" = "No se pueden recuperar los productos de compra en la aplicación en este momento";
"AppleInAppManagerError.noProductsFound" = "No se encontraron compras en la aplicación";
"AppleInAppManagerError.noProductIDsFound" = "No se encontraron identificadores de productos de compras en la aplicación";
"CloudInAppManagerError.uploadReceiptError" = "No se puede cargar el recibo en el servidor";
"CloudInAppManagerError.productListError" = "No se puede obtener la lista de productos en la aplicación";
"CloudInAppManagerError.decodingDatas" = "Problema de decodificación de datos";
"MediaManViewController.title" = "Enviar a Computadora";
"MediaPlayerViewController.trash-menu.remove.title" = "Eliminar";
"MediaPlayerViewController.asset-type.title" = "tipo";
"MediaPlayerViewController.creation-date.title" = "fecha de creación";
"MediaPlayerViewController.frame-size.title" = "tamaño del marco";
"MediaPlayerViewController.file-size.title" = "tamaño del archivo";
"MediaPlayerViewController.duration.title" = "duración";
"MediaPlayerViewController.encoding.title" = "codificacion";
"AssetExporter.send-menu.share-to-fb-reels.title" = "Share to Facebook Reels";
"AssetExporter.send-menu.share-to-fb-stories.title" = "Share to Facebook Stories";
"AssetExporter.send-menu.share-to-ig-stories.title" = "Share to Instagram Stories";
"AssetExporter.send-menu.share-to-ig-feed.title" = "Share to Instagram Feed";
"AssetExporter.send-menu.share-to-tiktok.title" = "Share to TikTok";
"AssetExporter.send-menu.share-to-meta-loo-long.msg" = "Videos longer than %d minutes cannot be shared to %@.";
"AssetExporter.send-menu.share-to-app-not-installed.msg" = "%@ app is required for this action, please install it first.";
"AssetExporter.send-menu.share-video-could-not-be-read.msg" = "Video file could not be read.";
"AssetExporter.send-menu.share-video-all-photos-perm-req.msg" = "This feature requires 'All Photos' access to Photo Library. You can adjust Photos permission for Switcher in the Settings app.";
"AssetExporter.open-in.no-app.msg" = "No se encontró ninguna aplicación para abrir este medio";
"AssetExporter.progress.title" = "Transferencia en curso";
"AssetExporter.send-menu.webdav.title" = "Envia a Media Manager";
"RecProfileViewController.director-mode.title" = "Modo Director";
"RecProfileViewController.director-mode.disabled" = "These options are only available when Director Mode is enabled.";
"RecProfileViewController.director-mode.frame-size.title" = "Formato de Composición";
"RecProfileViewController.director-mode.frame-size.auto.short" = "Auto";
"RecProfileViewController.director-mode.frame-size.auto.long" = "Auto";
"RecProfileViewController.director-mode.frame-size.tip" = "Solo los dispositivos compatibles grabarán en la resolución seleccionada. Otros dispositivos grabarán en sus respectivas resoluciones más altas. NOTA: La grabación 4K no está permitida en el modo Wi-Fi de baja latencia, no es compatible con dispositivos que ejecutan iOS14 y está limitada a 30 fps.";
"RecProfileViewController.director-mode.frame-rate.title" = "Velocidad de Fotogramas de Composición";
"RecProfileViewController.director-mode.frame-rate.variable.title" = "Modo Cámara VFR";
"RecProfileViewController.director-mode.frame-rate.variable.tip" = "Utilice el modo VFR (velocidad de fotogramas variable) para obtener alta calidad en condiciones de poca luz";
"RecProfileViewController.director-mode.bit-rate.title" = "Grabación Bitrate";
"RecProfileViewController.remote-camera.title" = "Cámaras Remotas";
"RecProfileViewController.remote-camera.rec.title" = "Grabar Cámaras Remotas";
"RecProfileViewController.remote-camera.rec.disabled" = "This option is only available when Director Mode is disabled.";
"RecProfileViewController.wifi.title" = "Wi-Fi Camera Communication";
"RecProfileViewController.outputs.title" = "Outputs";
"RecProfileViewController.live-output.title" = "Salida en Vivo";
"RecProfileViewController.live-output.rec.title" = "Grabar Salida en Vivo";
"RecProfileViewController.live-output.frame-size.title" = "Formato Salida en Vivo";
"RecProfileViewController.live-output.frame-size.tip" = "Estos parámetros solo se aplican cuando no se selecciona ningún canal de transmisión.";
"prod-action.alert.stop-rec-and-broadcast-button.title" = "Detener Grab + Transmisión";
"prod-action.alert.stop-broadcast-button.title" = "Continuar Grabando";
"prod-action.alert.recover-broadcast-button.title" = "Recuperar Transmisión";
"prod-action.alert.stop-button.title" = "Detener Transmisión";
"ProdError.error.BadUrlScheme.msg" = "Protocolo no admitido";
"ProdError.error.NoHost.msg" = "No se puede acceder al servidor de transmisión";
"ProdError.error.NoSocket.msg" = "Sin enchufe de conexión";
"ProdError.error.NoConnection.msg" = "Error en la conexión del servidor de transmisión";
"ProdError.error.NoESocket.msg" = "Error de conexión: Sin capa de transporte";
"ProdError.error.NoESocketHandshake.msg" = "Error de conexión: Falta la validación de seguridad";
"ProdError.error.NoRTMPHandshake.msg" = "El servidor de transmisión no responde";
"ProdError.error.BadRTMPHandshake.msg" = "Mala respuesta del servidor de transmisión";
"ProdError.error.UnknownRTMPError.msg" = "Error de protocolo RTMP";
"ProdError.error.SocketClosedByPeer.msg" = "Error de transmisión: Conexión cerrada por el servidor";
"ProdError.error.SocketError.msg" = "Error de comunicación de transmisión";
"ProdError.error.SocketTxError.msg" = "Error de comunicación de transmisión: La transmisión falló";
"ProdError.error.SocketRxError.msg" = "Error de comunicación de transmisión: La recepción falló";
"ProdError.error.PracticeModeFailed.msg" = "Practice livestream failed";
"ProdError.error.SocketSecurityError.msg" = "Error de seguridad";
"ProdError.error.InvalidAccount.msg" = "No hay una cuenta válida en el servicio de transmisión seleccionado";
"ProdError.error.InvalidIngestionSettings.msg" = "Resolución de transmisión no válida";
"ProdError.error.StreamingProviderGenericError.msg" = "Error del proveedor de transmisión";
"ProdError.error.InconsistentProfile.msg" = "Los parámetros de transmisión no son consistentes con los del servidor. Regrese a la configuración del livestreamo para volver a cargar todos los parámetros";
"ProdError.error.BadHTTPResponseCode.msg" = "El servidor rechazó la solicitud";
"ProdError.error.BadHTTPResponseFormat.msg" = "Mala respuesta del servidor";
"ProdError.error.HTTPRequestFailed.msg" = "El servidor no está disponible";
"ProdError.error.DualStreamingNotSupported.msg" = "Modo de transmisión no admitido";
"ProdError.error.NothingToStream.msg" = "La secuencia no tiene contenido";
"ProdError.error.HLSFailed.msg" = "Error de transmisión de HLS";
"ProdError.error.sec.TLS_INVALID_CERT.msg" = "Error de conexión: Certificado de seguridad no válido";
"ProdError.error.sec.TLS_NO_CERT.msg" = "Error de conexión: Sin certificado de seguridad";
"ProdError.error.sec.TLS_EXPIRED_CERT.msg" = "Error de conexión: El certificado de seguridad caducó";
"ProdError.error.sec.TLS_HANDSHAKE.msg" = "Error de conexión: La validación de seguridad falló";
"ProdError.error.sec.NET_CLOSED.msg" = "Conexión segura interrumpida por el servidor";
"ProdError.error.RecordingError.msg" = "Error de grabación";
"ProdError.error.network.connectivity.title" = "Poor Network Connection";
"ProdError.error.network.connectivity.msg" = "Make sure you are connected to a stable network and recover your broadcast.";
"ProdError.error.DiskFull.msg" = "No hay suficiente espacio de almacenamiento disponible para continuar con la grabación";
"VideoBitRate.title" = "Bitrate de Video";
"VideoBitRate.value.title" = "Bitrate de Video";
"VideoBitRate.mode.title" = "Modo de Bitrate de Vídeo";
"VideoBitRate.mode.default.title.long" = "Bitrate Estándar";
"VideoBitRate.mode.default.title.short" = "Estándar";
"VideoBitRate.mode.uniform.title" = "Bitrate Uniforme";
"VideoBitRate.mode.uniform.tip" = "Se usa el mismo bitrate para todos los formatos de video";
"VideoBitRate.mode.adaptative.title" = "Escalar Bitrate con la Velocidad de Fotogramas y el Tamaño";
"VideoBitRate.mode.adaptative.tip" = "Para los formatos de video que no se muestran aquí arriba, el bitrate se ajusta para permanecer proporcional al tamaño de fotograma y la frecuencia de fotogramas utilizados.";
"VideoBitRate.mode.avc720p.title" = "Para 1280 x 720, %d fps, video AVC/H.264:";
"VideoBitRate.mode.avc1080p.title" = "Para 1920 x 1080, %d fps, video AVC/H.264:";
"VideoBitRate.mode.hevc2160p.title" = "Para 3840 x 2160 (4K), %d fps, vídeo HEVC:";
"RLBatchViewController.collect-remote-media.title" = "Recopile medios de dispositivos remotos";
"RLBatchViewController.remove-remote-media.title" = "Quitar medios remotos";
"RLBatchViewController.render.title" = "Renderizar composiciones finales";
"RLBatchViewController.execute.title" = "Ejecutar";
"RLBatchViewController.done.title" = "Operación realizada";
"RLInfoViewController.title" = "Dispositivos";
"RLInfoViewController.audio-input.title" = "Entrada de Audio";
"RLInfoViewController.audio-output.title" = "Salida de Audio";
"RLInfoViewController.audio-output.disabled-speaker.title" = "Ninguno";
"RLInfoViewController.battery.title" = "Batería";
"RLInfoViewController.storage.title" = "Almacenamiento Usado";
"RLInfoViewController.cpu-load.title" = "Carga de CPU";
"RLInfoViewController.capture-size.title" = "Tamaño de Fuente";
"RLInfoViewController.stream-size.title" = "Tamaño de Secuencia";
"RLInfoViewController.stream-type.title" = "Tipo de Secuencia";
"RLInfoViewController.this-device.title" = "Este Dispositivo";
"RLInfoViewController.audio-source-drift.title" = "Deriva Entrada Audio";
"RLInfoViewController.audio-dest-drift.title" = "Deriva Salida Audio";
"RLInfoViewController.pkt-loss.title" = "Paquete Perdido";
"RLInfoViewController.latency.title" = "Latencia de Conexion";
"RLInfoViewController.speedtest.title" = "Upload Speed Test";
"RLInfoViewController.speedtest.status" = "Status";
"RLInfoViewController.speedtest.status.idle" = "Idle";
"RLInfoViewController.speedtest.status.inProgress %lld" = "In progress (%lld%%)";
"RLInfoViewController.speedtest.status.success" = "Completed";
"RLInfoViewController.speedtest.status.error" = "Error";
"RLInfoViewController.speedtest.status.skipped" = "Skipped";
"RLInfoViewController.speedtest.status.cancelled" = "Cancelled";
"RLInfoViewController.speedtest.result" = "Result";
"RLInfoViewController.speedtest.result.na" = "N/A";
"RLInfoWarningView.warning.msg" = "Su red Wi-Fi parece estar perdiendo datos de video. Cambie al modo Wi-Fi Reforzado para una transmisión más fluida.";
"RLInfoWarningView.warning.btn.title" = "Cambiar Ahora";
"CameraValidationViewController.device.text" = "Dispositivo";
"CameraValidationViewController.perm-msg.text" = "está tomando el control";
"CameraValidationViewController.perm-allow-button.text" = "Permitir";
"CameraValidationViewController.perm-reject-button.text" = "Rechazar";
"CameraValidationViewController.perm-auto-mode-switch.text" = "Permitir Automáticamente";
"MixerModel.create-new-event.title" = "Crear Nuevo Livestreamo";
"MixerModel.create-new-event.subtitle" = "Please create a livestream and try again.";
"MixerModel.create-new-event.button" = "Create New Livestream";
"MixerModel.create-new-event.button.short" = "Schedule";
"MixerModel.no-rtmp-channel.title" = "No RTMP Channel Available";
"MixerModel.no-rtmp-channel.subtitle" = "Please set up an RTMP channel and try again.";
"MixerModel.no-rtmp-channel.button" = " Set Up RTMP ";
"MixerModel.create-edit-event.title" = "Editar Livestreamo";
"MixerModel.audio-warning.title" = "Advertencia";
"MixerModel.audio-warning.msg" = "El audio está actualmente desactivado o está siendo utilizado por otra aplicación.";
"MixerModel.audio-warning.restart" = "Habilitar Audio";
"OnePaneMixerViewController.stream-validation.msg" = "Validación de la Transmisión en Curso...";
"OnePaneCameraViewController.remote-xfer-progress.text" = "Transferencia de Medios";
"OnePaneCameraViewController.menu.title" = "Menú";
"OnePaneCameraViewController.disconnect.title" = "Desconectarse del Switcher";
"OnePaneCameraViewController.disconnect.named.title" = "Desconectarse del Switcher \"%@\"";
"OnePaneCameraViewController.disconnect.confirm.msg" = "¿Realmente quieres desconectarte del switcher?";
"OnePaneCameraViewController.disconnect.confirm.named.msg" = "¿Realmente quieres desconectarte del switcher \"%@\"?";
"OnePaneCameraViewController.disconnect.confirm.yes" = "Desconectar";
"OnePaneCameraViewController.disconnect.confirm.no" = "Mantente Conectado";
"OsmoConnectionViewController.title" = "DJI Osmo Mobile";
"OsmoConnectionViewController.searching.title" = "Buscando...";
"OsmoConnectionViewController.error.connection.msg" = "No puede conectar a %@";
"MediaCollectionViewController.location.photo-lib.title" = "Librería Fotográfica";
"MediaCollectionViewController.media-type.journal.title" = "Diario";
"MediaCollectionViewController.media-type.live.title" = "En Vivo";
"MediaCollectionViewController.media-type.composition.title" = "Composición";
"MediaCollectionViewController.media-type.aux.title" = "Activo Auxiliar";
"MediaCollectionViewController.media-type.unknown.title" = "Activo Desconocido";
"MediaCollectionViewController.media-type.special.title" = "Activo Especial";
"LocalMediaTableViewController.media-type.unknown.title" = "Activo Desconocido";
"LocalMediaTableViewController.media-type.special.title" = "Activo Especial";
"LocalMediaTableViewController.type.recorded-audio.title" = "Fuente de Audio";
"LocalMediaTableViewController.type.recorded-video.title" = "Fuente de Vídeo";
"LocalMediaTableViewController.type.recorded-compo.title" = "Composición";
"LocalMediaTableViewController.type.recorded-live.title" = "Programa en Vivo";
"LocalMediaTableViewController.type.recorded-clip.title" = "Clip";
"LocalMediaTableViewController.type.imported-audio.title" = "Activo de Audio Importado";
"LocalMediaTableViewController.type.imported-video.title" = "Activo de Video Importado";
"LocalMediaTableViewController.type.imported-image.title" = "Imagen Importada";
"LocalMediaTableViewController.photo-imported.title" = "Fotos Importadas";
"LocalMediaTableViewController.video-imported.title" = "Videos Importados";
"LocalMediaTableViewController.video-recordings.title" = "Grabaciones Locales";
"JournalViewController.remove.nothing.title" = "No se puede borrar nada.";
"JournalViewController.remove.single.title" = "¿Está seguro de que desea eliminar 1 activo?";
"JournalViewController.remove.multiple.title" = "¿Está seguro de que desea eliminar %d activos?";
"JournalViewController.remove.msg" = "Los activos auxiliares y los activos almacenados en la biblioteca de fotos no se eliminarán.";
"JournalViewController.remove.single.action" = "Eliminar 1 activo";
"JournalViewController.remove.multiple.action" = "Eliminar %d Activos";
"JournalViewController.send-to.compo.action" = "Composición";
"JournalViewController.send-to.all.action" = "Todos";
"MediaProcessing.photo-lib.error.no-access" = "Esta aplicación no tiene acceso a su biblioteca de fotos.";
"MediaProcessing.photo-lib.error.failed" = "Falló la Transferencia a su Biblioteca de Fotos.";
"MediaProcessing.photo-lib.success" = "La transferencia tuvo éxito.";
"CityProducerViewController.title" = "Enviar a CTpro";
"CityProducerViewController.local.title" = "CTpro en este dispositivo";
"CityProducerViewController.local-dest.title" = "Aplicaciones";
"CityProducerViewController.remote-dest.title" = "Dispositivos remotos";
"CityProducerViewController.missing.msg" = "CTpro no está disponible en este dispositivo.";
"welcome-d3.button.close" = "Cerrar";
"welcome-d3.button.back" = "Atrás";
"welcome-d3.button.login" = "Iniciar Sesión";
"welcome-d3.button.logout" = "Cerrar Sesión";
"welcome-d3.button.login.info" = "Acceso a su cuenta";
"welcome-d3.button.create-account" = "Crear una cuenta";
"welcome-d3.button.share-device" = "Comparte este dispositivo";
"welcome-d3.button.switcher-mode" = "Usar como Switcher";
"welcome-d3.separator.or" = "O";
"welcome-d3.button.troubleshoot" = "¿Problemas para iniciar sesión?";
"welcome-d3.field.user" = "Dirección de Correo Electrónico";
"welcome-d3.field.password" = "Contraseña";
"welcome-d3.field.forgot.password" = "¿Has olvidado tu contraseña?";
"welcome-d3.field.create-account.user.placeholder" = "Ingrese su dirección de Correo electrónico";
"welcome-d3.field.create-account.password.placeholder" = "Crear contraseña";
"welcome-d3.field.create-account.confirmation.placeholder" = "Confirmar Contraseña";
"welcome-d3.alert.authentication-error" = "Error de autenticación";
"welcome-d3.alert.authentication-connection-error.title" = "Sin conexión a Internet";
"welcome-d3.alert.authentication-connection-error.message" = "Su conexión a Internet parece estar deshabilitada. Verifique su conexión e intente nuevamente.";
"welcome-d3.alert.authentication-input-error.title" = "No se puede iniciar sesión";
"welcome-d3.alert.authentication-input-error.message" = "Esta combinación de correo electrónico y contraseña es incorrecta. Por favor, revise y vuelva a intentarlo.";
"welcome-d3.message.go-live-on" = "Transmitir en Vivo en";
"welcome-d3.warning.refresh-denied" = "Su sesión expiró o inició sesión en otro dispositivo.";
"welcome-login.text.value-prop" = "**Todo lo que es video, en Switcher**";
"welcome-login.button.login-signup" = "Iniciar Sesión o Registrarse";
"welcome-login.button.share" = "Compartir Cámara o Pantalla";
"account-creation.generic.error" = "";
"account-creation.web-error.error" = "La creación de la cuenta falló durante la autenticación";
"account-creation.auth-error.error" = "Error de autenticación";
"account-creation.no-connection.error" = "La conexión falló";
"account-creation.invalid-user.error" = "Dirección de correo electrónico no válida";
"account-creation.unmatched-passwords.error" = "La confirmación de la contraseña no coincide";
"email-confirmation.title" = "Confirmación de correo";
"email-confirmation.text1" = "Revise su correo electrónico para verificar su cuenta.";
"email-confirmation.text2" = "<html>Hemos enviado un correo electrónico con instrucciones a <a action=\"email\">%@</a> . Para confirmar, haga clic en el enlace del mensaje.</html>";
"email-confirmation.resend.title" = "¿No recibiste un correo electrónico?";
"email-confirmation.resend.ack" = "Se ha enviado otra copia del correo electrónico a %@.";
"sharing-choice.button.share-screen.start" = "Comparte Pantalla";
"sharing-choice.button.share-screen.subtitle" = "Utilice la pantalla de este dispositivo como fuente en su producción.";
"sharing-choice.button.share-screen.stop" = "Deja de compartir esta pantalla";
"sharing-choice.button.share-camera.start" = "Compartir Cámara";
"sharing-choice.button.share-camera.subtitle" = "Utilice la cámara de este dispositivo como fuente en tu producción.";
"waiting-connection.title.share-screen" = "Estás compartiendo esta pantalla";
"waiting-connection.message.waiting-for-connection" = "Esperando conexión desde el conmutador";
"waiting-connection.more-informations.title" = "Más información";
"waiting-connection.share-camera.message %@" = "Tap this iOS device’s name in the Inputs tab (%@) on your main iOS device.";
"waiting-connection.infos-line1.message" = "¿No ves esta cámara en la lista?";
"waiting-connection.infos-line2.message" = "Toque Agregar Fuente y Conectar por URL, luego ingrese esta URL";
"waiting-connection.infos-line3.message" = "Wi-Fi no disponible";
"AssetExportToPHLibViewController.phlib.title.label" = "Envia a la biblioteca de fotos";
"AssetExportToPHLibViewController.ctpro.title.label" = "Enviar a CTpro";
"AssetExportToPHLibViewController.countmedias.label" = "%d medios seleccionados";
"AssetExportToPHLibViewController.move.button" = "Mover";
"AssetExportToPHLibViewController.move.description" = "Eliminando archivo(s) de Switcher";
"AssetExportToPHLibViewController.copy.button" = "Copiar";
"AssetExportToPHLibViewController.copy.description" = "Mantener archivo(s) en Switcher";
"AssetExportToPHLibViewController.loading.label" = "Espere hasta que se complete la transferencia.";
"AssetExportToPHLibViewController.ctpro.opening" = "Abrir CTpro";
"ToolLibrary.reorder.title" = "Personalizar";
"ToolLibrary.reorder.titleForDeleteConfirmationButton" = "Eliminar";
"ToolLibrary.section.visibleTools" = "Incluido";
"ToolLibrary.section.hiddenTools" = "Más herramientas";
"ToolLibrary.section.restoreTools" = "";
"ToolLibrary.section.restoreTools.restore-defaults-button.title" = "Restaurar Orden Predeterminado";
"ToolLibrary.section.restoreTools.confirmation.msg" = "¿Está seguro que desea restaurar el orden predeterminado?";
"ToolLibrary.section.restoreTools.confirmation.button.title" = "Restaurar";
"ToolLibrary.tool.input" = "Entradas";
"ToolLibrary.tool.output" = "Salidas";
"ToolLibrary.tool.effect" = "Efectos";
"ToolLibrary.tool.camera-control" = "Control de Cámara";
"ToolLibrary.tool.audio" = "Audio";
"ToolLibrary.tool.comment" = "Comentarios en Vivo";
"ToolLibrary.tool.more" = "Más";
"ToolLibrary.tool.info" = "Info";
"ToolLibrary.tool.encoding" = "Codificación";
"ToolLibrary.tool.star" = "Estrellar";
"ToolLibrary.tool.custom" = "Personalizado";
"ToolLibrary.tool.react-native" = "React Native";
"ToolLibrary.tool.auto-switch" = "Cambio Automático";
"ToolLibrary.tool.development" = "Herramienta de Desarrollo";
"ToolLibrary.tool.markers" = "Timestamps";
"RLAudioEngine.SrcLineIn.title" = "Entrada de Audio Analógica (Jack)";
"RLAudioEngine.SrcBuiltInMic.title" = "Micrófono Incorporado";
"RLAudioEngine.SrcWiredInput.title" = "Entrada de micrófono / línea cableada";
"RLAudioEngine.SrcBluetoothHFP.title" = "Micrófono Bluetooth";
"RLAudioEngine.SrcUSBAudio.title" = "Entrada de Audio USB/Lightning";
"RLAudioEngine.SrcUSBCAudio.title" = "Entrada de Audio USB";
"RLAudioEngine.SrcCarAudio.title" = "Audio del Coche";
"RLAudioEngine.SrcVirtual.title" = "Salida de Audio Virtual";
"RLAudioEngine.SrcPCI.title" = "Salida PCI";
"RLAudioEngine.SrcFireWire.title" = "Salida FireWire";
"RLAudioEngine.SrcDisplayPort.title" = "Salida Display Port";
"RLAudioEngine.SrcAVB.title" = "Interconexión de Audio y Video";
"RLAudioEngine.SrcThunderbolt.title" = "Salida Thunderbolt";
"RLAudioEngine.DestLineOut.title" = "Salida de Audio Analógica (Conector)";
"RLAudioEngine.DestHeadphones.title" = "Auriculares";
"RLAudioEngine.DestBluetoothA2DP.title" = "Salida Bluetooth";
"RLAudioEngine.DestBuiltInReceiver.title" = "Altavoz de Teléfono Incorporado";
"RLAudioEngine.DestBuiltInSpeaker.title" = "Altavoz Incorporado";
"RLAudioEngine.DestHDMI.title" = "Salida HDMI";
"RLAudioEngine.DestAirPlay.title" = "Salida Airplay";
"RLAudioEngine.DestBluetoothLE.title" = "Salida LE Output";
"RLAudioEngine.DestBluetoothHFP.title" = "Salida HFP Output";
"RLAudioEngine.DestUSBAudio.title" = "Salida de Audio USB";
"RLAudioEngine.DestCarAudio.title" = "Salida de Audio Automóvil";
"RLAudioEngine.DestVirtual.title" = "Salida de Audio Virtual";
"RLAudioEngine.DestPCI.title" = "Salida PCI";
"RLAudioEngine.DestFireWire.title" = "Salida FireWire";
"RLAudioEngine.DestDisplayPort.title" = "Salida Display Port";
"RLAudioEngine.DestAVB.title" = "Interconexión de Audio y Video";
"RLAudioEngine.DestThunderbolt.title" = "Salida Thunderbolt";
"RLAudioEngine.unknown.title" = "Desconocido";
"CloudAssetsListViewController.title" = "Switcher Cloud";
"CloudAssetsListViewController.edit.title" = "Seleccione";
"CloudAssetsListViewController.error.title" = "No se pudieron cargar los activos desde el Cloud: %@";
"CloudAssetsListViewController.switcher-filetype.mmsrc.title" = "Audio / Vídeo";
"CloudAssetsListViewController.switcher-filetype.mmvideo.title" = "Vídeo";
"CloudAssetsListViewController.switcher-filetype.mmaudio.title" = "Audio";
"CloudAssetsListViewController.switcher-filetype.mmfx.title" = "Efecto";
"CloudAssetsListViewController.switcher-filetype.mmart.title" = "Elemento gráfico";
"CloudAssetsListViewController.switcher-filetype.mmjrnl.title" = "Diario";
"CloudAssetsListViewController.switcher-filetype.undefined.title" = "Archivo de Switcher indefinido";
"CloudAssetsListViewController.switcher-filetype.image.title" = "Imagen";
"CloudAssetsListViewController.switcher-filetype.movie.title" = "Película";
"CloudAssetsListViewController.switcher-filetype.audio.title" = "Audio";
"CloudAssetsListViewController.no-asset.title" = "Ningún activo disponible.";
"CloudAsseUploadViewController.title.label" = "Subir activos a Switcher Cloud";
"CloudAssetUploadViewController.countmedias.label" = "%d activo(s) seleccionados";
"CloudAssetUploadViewController.success.label" = "Subió %d activo(s) con éxito";
"CloudAssetUploadViewController.failed.label" = "Error al cargar a Switcher Cloud";
"CloudAssetUploadViewController.failed.max-assets.label" = "Ha superado la cantidad permitida de activos en Switcher Cloud";
"CloudVideoStorageView.text.primary.mid" = "Storage limit approaching";
"CloudVideoStorageView.text.primary.high" = "Storage limit reached";
"CloudVideoStorageView.text.studio.attributed" = "You are approaching your overall storage limit of video uploads. Upgrade Your Plan";
"CloudVideoStorageView.text.studio.limit.attributed" = "You have reached your overall storage limit of video uploads. Upgrade Your Plan";
"CloudVideoStorageView.text.attributed.substring" = "Upgrade Your Plan";
"CloudVideoStorageView.text.business.attributed" = "You are approaching your overall storage limit of video uploads. Delete videos or contact us for more storage";
"CloudVideoStorageView.text.business.limit.attributed" = "You have reached your overall storage limit of video uploads. Delete videos or contact us for more storage";
"CloudVideoStorageView.text.business.attributed.substring" = "contact us for more storage";
"CloudVideoStorageView.text.studio.no.sub.attributed" = "You are approaching your overall storage limit of video uploads. Visit your account at switcherstudio.com to manage your subscription.";
"CloudVideoStorageView.text.studio.no.sub.limit.attributed" = "You have reached your overall storage limit of video uploads. Visit your account at switcherstudio.com to manage your subscription.";
"CloudVideoStorageView.text.studio.no.sub.attributed.substring" = "switcherstudio.com";
"CloudVideoStorageView.progress.text %lld %lld" = "%lld of %lld";
"SelectMenuManager.edit.title" = "Seleccione";
"D3UserMainViewController.badge.dev" = "Dev";
"D3UserMainViewController.badge.trial" = "Muestra";
"D3UserMainViewController.badge.limited-access" = "Cuenta Caducada";
"D3UserMainViewController.badge.dev.alert" = "Estás en el servidor de desarrollo. ¡¡¡Eres increíble!!!";
"D3UserMainViewController.badge.trial.alert" = "Actualmente se encuentra en una prueba gratuita de 14 días que le brinda acceso temporal a todas las funciones de Switcher. Los videos que cree durante la prueba incluirán la marca de agua de Switcher. Si tiene preguntas sobre el uso de Switcher después de la prueba y la eliminación de la marca de agua, comuníquese con nuestro equipo de <NAME_EMAIL>.";
"UserMainView.message" = "¡Hola!\n ¿Qué quieres crear hoy?";
"UserMainView.Creation.title" = "Transmitir en Vivo o Grabar Video";
"UserMainView.Creation.text" = "Utiliza nuestra suite completa de herramientas de edición en vivo y multicámara.";
"UserMainView.horizontal" = "Horizontal";
"UserMainView.vertical" = "Vertical";
"UserMainView.ratio" = "VIDEO RATIO";
"AssetProdCollectionViewController.title" = "Activos";
"AssetProdCollectionViewController.new" = "New";
"AssetProdCollectionViewController.choice.photo" = "Foto";
"AssetProdCollectionViewController.choice.video" = "Vídeo";
"AssetProdCollectionViewController.choice.audio" = "Audio";
"AssetProdCollectionViewController.choice.shopping" = "Tarjeta";
"AssetProdCollectionViewController.choice.text-graphics" = "Texto y gráficas";
"AssetProdCollectionViewController.choice.multiviews" = "Múlti-vista";
"AssetProdCollectionViewController.choice.cloud" = "Switcher Cloud";
"AssetProdCollectionViewController.choice.camera" = "Entrada";
"AssetProdCollectionViewController.choice.logo" = "Logo";
"AssetProdCollectionViewController.choice.imagesoverlay" = "Image Overlays";
"AssetProdCollectionViewController.choice.lowerthird" = "Tercio inferior";
"AssetProdCollectionViewController.choice.image-overlay" = "Superposición de imágen";
"MediaListTableViewController.photo.title" = "Foto";
"MediaListTableViewController.video.title" = "Vídeo";
"MediaListTableViewController.audio.title" = "Audio";
"MediaListTableViewController.choice.audio-from-video" = "Audio de Video";
"MediaListTableViewController.choice.audio-imported" = "Audios importados";
"MediaListTableViewController.choice.photo-library" = "Librería Fotográfica";
"MediaListTableViewController.choice.photo-imported" = "Imágenes Importadas";
"MediaListTableViewController.choice.video-imported" = "Vídeos Importados";
"MediaListTableViewController.choice.my-recordings" = "Grabaciones Locales";
"MediaListTableViewController.choice.samples" = "Muestras";
"MediaListTableViewController.choice.switcher-cloud" = "Switcher Cloud";
"MediaListTableViewController.choice.ios-picker" = "Archivos/Unidad USB";
"MediaListTableViewController.choice.no-image" = "Sin imágen";
"MediaListTableViewController.choice.backgrounds" = "Antecedentes";
"MediaListTableViewController.choice.gradient" = "Degradado";
"MediaListTableViewController.choice.platform-icons" = "Platform Icons";
"MediaListTableViewController.choice.patterns" = "Patterns";
"MediaListTableViewController.choice.frames" = "Frames";
"GradientGeneratorViewController.title" = "Degradado";
"GradientGeneratorViewController.params.main-color" = "Color principal";
"GradientGeneratorViewController.params.nuance" = "Matiz";
"GradientGeneratorViewController.params.brightness" = "Brillo";
"GradientGeneratorViewController.params.orientation" = "Orientación";
"MediaListTableViewController.social-icons.title" = "Sociales";
"MediaListTableViewController.choice.social.facebook" = "Facebook";
"MediaListTableViewController.choice.social.instagram" = "Instagram";
"MediaListTableViewController.choice.social.youtube" = "Youtube";
"MediaListTableViewController.choice.social.twitter" = "Twitter";
"MediaListTableViewController.choice.social.linkedin" = "LinkedIn";
"MediaListTableViewController.donation-icons.title" = "Donaciones";
"MediaListTableViewController.choice.donation.cashapp" = "Cashapp";
"MediaListTableViewController.choice.donation.givelify" = "Givelify";
"MediaListTableViewController.choice.donation.patreon" = "Patreon";
"MediaListTableViewController.choice.donation.paypal" = "Paypal";
"MediaListTableViewController.choice.donation.tithely" = "Tithely";
"MediaListTableViewController.choice.donation.venmo" = "Venmo";
"AssetGridViewController.photo.title" = "Todas las fotos";
"AssetGridViewController.video.title" = "Todos los videos";
"AlbumListTableViewController.title" = "Álbumes";
"AlbumListTableViewController.section.smart-albums" = "Álbumes inteligentes";
"AlbumListTableViewController.section.user-albums" = "Álbumes de usuario";
"AlbumListTableViewController.section.icloud-albums" = "Fotos de iCloud";
"AlbumListTableViewController.section.empty.title" = "No hay álbum disponible";
"SamplesCollectionViewController.title" = "Muestras";
"SamplesCollectionViewController.choice.pattern" = "Patrón de Prueba";
"SamplesCollectionViewController.choice.cornerbug" = "Logo";
"SamplesCollectionViewController.choice.slide" = "Diapositiva";
"SamplesCollectionViewController.choice.image-fullscreen" = "Foto";
"SamplesCollectionViewController.choice.video-cereals" = "Cereales";
"SamplesCollectionViewController.choice.video-ocean" = "Océano";
"BackgroundsCollectionViewController.title" = "Fondos";
"OpacityViewManager.loading.title" = "Cargando...";
"TextAndGraphicsListTableViewController.title" = "Texto y Gráficas";
"TextAndGraphicsListTableViewController.choice.title" = "Títulos";
"TextAndGraphicsListTableViewController.choice.lower-third" = "Tercios Inferiores";
"TextAndGraphicsListTableViewController.choice.animated-text" = "Texto animado";
"TextAndGraphicsListTableViewController.choice.timers" = "Temporizadores";
"TimersCollectionViewController.timers.stopwatches.section" = "Timers and Stopwatches";
"TimersCollectionViewController.timers.section" = "Timers Only";
"TextAndGraphicsListTableViewController.choice.platform-overlay" = "Platform Overlays";
"TextAndGraphicsListTableViewController.choice.broadcast-notification" = "Notificaciones de Transmisión";
"TextAndGraphicsListTableViewController.choice.corner-bug" = "Logos - De la Esquina";
"TextAndGraphicsListTableViewController.choice.image-overlay" = "Superposiciones de imágenes";
"TextAndGraphicsListTableViewController.choice.slideshow" = "Image Slideshow";
"TextAndGraphicsListTableViewController.choice.image-lower-third" = "Imagen como Tercio Inferior";
"MultiviewsCollectionViewController.title" = "Múlti-vistas";
"MultiviewsCollectionViewController.choice.dashboard" = "Dashboard";
"MultiviewsCollectionViewController.choice.split-screen" = "Pantalla Dividida";
"MultiviewsCollectionViewController.choice.flap" = "Solapa";
"MultiviewsCollectionViewController.choice.slots" = "Ranuras";
"MultiviewsCollectionViewController.choice.picture-in-picture" = "Imagen en Imagen";
"MultiviewsCollectionViewController.choice.grid" = "Cuadrícula";
"BaseProdFlow.alert.cloud.available-soon" = "Disponible Pronto";
"AudioSettingTableViewController.title" = "Configuraciones de audio";
"AudioSettingsTableViewController.boost-section.title" = "Aumentar";
"AudioSettingsTableViewController.boost-section.value-template" = "%d dB";
"AudioSettingsTableViewController.routing-section.title" = "Enrutamiento";
"AudioSettingsTableViewController.routing-section.none" = "Ninguno";
"AudioSettingsTableViewController.routing-section.leftToMono" = "Izquierda a mono";
"AudioSettingsTableViewController.routing-section.rightToMono" = "Derecho a mono";
"AudioSettingsTableViewController.routing-section.sumToMono" = "Suma a mono";
"LimitedAccessViewController.header" = "Cuenta Caducada";
"LimitedAccessViewController.body" = "Su cuenta ha caducado. Póngase en <NAME_EMAIL> si tiene alguna pregunta.";
"LimitedAccessViewController.support-button" = "Contactar Soporte";
"SubscriptionExpiredAlert.header" = "Expired Account";
"SubscriptionExpiredAlert.body" = "Your account has expired.\n\nPlease contact \n[<EMAIL>](mailto:<EMAIL>)\nwith any questions.";
"NetworkUnavailableAlert.header" = "No Internet Connection";
"NetworkUnavailableAlert.body" = "Your Internet connection appears to be offline. Check your connection and try again.";
"StorageLimitAlert.header" = "Attention";
"StorageLimitAlert.body %lld %lld" = "You have reached your overall storage limit of video uploads (%lld of %lld). Delete videos to free up space before uploading more.";
"StorageLimitAlert.button.manage" = "Manage Video Library";
"StorageLimitAlert.button.dismiss" = "Dismiss";
"PHAssetCustomError.notSupported.msg" = "Formato no compatible";
"PHAssetCustomError.notLocal.msg" = "No es un archivo local";
"PHAssetCustomError.noValidSource.msg" = "No es una fuente válida";
"PHAssetCustomError.notResolvedUrl.msg" = "La URL no se puede resolver";
"PHAssetCustomError.noAVAsset.msg" = "No hay AVasset disponible";
"PHAssetCustomError.cancelRequest.msg" = "La solicitud ha sido cancelada por el usuario";
"MyRecordingHeaderView.sort.date" = "Por Fecha";
"MyRecordingHeaderView.sort.name" = "Por Nombre";
"CloudyToolViewController.progress.msg" = "Descargando...";
"CloudyToolViewController.not-available.msg" = "No Disponible";
"Asset.in-photo-library.single" = "Activo de fototeca";
"Asset.in-photo-library.multiple" = "Activos de la fototeca";
"ColorPickerViewControllerV2.title" = "Color";
"ColorPickerViewControllerV2.selector.palette" = "Paleta";
"ColorPickerViewControllerV2.selector.rgb" = "RGB";
"ColorPickerViewControllerV2.selector.predefined-colors" = "Muestras";
"ColorPickerViewControllerV2.css-color-code.alert.title" = "Código de Color CSS";
"ColorPickerViewControllerV2.css-color-code.alert.placeholder" = "Código de Color CSS";
"ColorPickerViewControllerV2.rgb-sliders.red-label" = "R";
"ColorPickerViewControllerV2.rgb-sliders.green-label" = "G";
"ColorPickerViewControllerV2.rgb-sliders.blue-label" = "B";
"DiagInformationsTableViewController.informations.title" = "Diagnóstico";
"DiagInformationsTableViewController.version.title" = "Versión de la Aplicación";
"DiagInformationsTableViewController.build-number.title" = "Número de Compilación de la Aplicación";
"DiagInformationsTableViewController.from.title" = "Origen";
"DiagInformationsTableViewController.tool-bundle-version.title" = "Versión del Paquete de Herramientas";
"DiagInformationsTableViewController.tool-api-version.title" = "Versión de la API de Herramientas";
"DiagInformationsTableViewController.sessions.title" = "Sesiones";
"DiagInformationsTableViewController.log-activation.title" = "Registros de diagnóstico";
"DiagInformationsTableViewController.log-activation.enable.title" = "Registros";
"DiagInformationsTableViewController.log-activation.enable.msg" = "El registro de diagnóstico se habilitará en el próximo lanzamiento de la aplicación durante las próximas %d horas. Esto ayuda a los equipos de I + D y soporte a analizar los problemas que puedan surgir.";
"DiagInformationsTableViewController.log-activation.disable.title" = "Detener registro";
"DiagInformationsTableViewController.log-activation.disable.msg" = "El registro de diagnóstico se desactivará en el próximo lanzamiento de la aplicación.";
"DiagInformationsTableViewController.log-activation.currently-enable.msg" = "El registro de diagnóstico está habilitado actualmente. Esto ayuda a los equipos de I + D y soporte a analizar los problemas que puedan surgir.";
"AutoSwitchTableViewController.title" = "Cambio Automático";
"AutoSwitchTableViewController.sources.header.title" = "Fuentes";
"AutoSwitchTableViewController.actions.header.title" = "Acciones";
"AutoSwitchTableViewController.settings.header.title" = "Configuraciones";
"AutoSwitchTableViewController.sources.no-source.title" = "Sin Fuentes";
"AutoSwitchTableViewController.action-item.start.title" = "Comienza";
"AutoSwitchTableViewController.action-item.stop.title" = "Detener";
"AutoSwitchTableViewController.action.playing" = "En Vivo";
"AutoSwitchTableViewController.settings.transition-timing.title" = "Intervalo";
"AutoSwitchTableViewController.settings.shuffle.title" = "Barajar";
"LoaderInAppViewController.button.ok" = "OK";
"AudioEditorViewController.title" = "Editor";
"AudioEditorViewController.volume.title" = "Volumen";
"AudioEditorViewController.volume.value-db" = "%@ dB";
"AudioEditorViewController.thumbnail.tag.title" = "Etiqueta";
"AudioEditorViewController.thumbnail.tag.placeholder" = "Sin texto";
"AudioEditorViewController.loop.title" = "Loop";
"AudioEditorViewController.loop.description" = "When loop is enabled, the audio asset will play continually until removed from screen.";
"MediaPlayerVideoCore.clips.button.title" = "Clips";
"ClipsTableViewController.title" = "Clips";
"ClipsTableViewController.clip-list.header.title" = "Lista de clips";
"ClipsTableViewController.clip-list.no-clip.title" = "No hay clip";
"MultiviewsProdFlow.group-1.title" = "1 Vista";
"MultiviewsProdFlow.group-2.title" = "2 Vistas";
"MultiviewsProdFlow.group-3.title" = "3 Vistas";
"MultiviewsProdFlow.group-4.title" = "4 Vistas";
"MultiviewsProdFlow.group-5.title" = "5 Vistas";
"MultiviewsProdFlow.group-6.title" = "6 Vistas o Más";
"ElementTransitions.title" = "In & Out Transition";
"ElementTransitions.toggle.title" = "Enable Transition";
"ElementTransitions.toggle.hint" = "When enabled, this will override the main transition settings.";
"ElementTransitions.select.title" = "Transition";
"ElementTransitions.select.hint" = "To adjust transition properties, please go to the Transitions tool.";
"ElementGroupsViewController.elementGroups.title" = "Grupos de Activos";
"ElementGroupsViewController.section.existingGroups" = "Grupos Existentes";
"ElementGroupsViewController.section.addNewGroup.button.title" = "Agregar Nuevo Grupo";
"ElementGroupsViewController.groups.mainGroup" = "Principal";
"ElementGroupsViewController.groups.delete.elements" = "This production group contains assets. What do you want to do?";
"ElementGroupsViewController.groups.delete.all" = "Delete Production Group and Assets";
"ElementGroupsViewController.groups.delete.collection" = "Delete Production Group Only";
"ZoomSliderViewController.editSections.title" = "Editar Groupos";
"Clips.short-description" = "Clips son versiones modificadas de la grabación original.";
"Clips.long-description" = "Clips son versiones modificadas de la grabación original. Pueden recortarse, reducirse y acelerarse.";
"Clips.edit-original-asset.tip" = "Cree un nuevo clip y edítelo según sea necesario.";
"Clips.create-new.title" = "Crear Nuevo Clip";
"Clips.extract.title" = "Crear Nuevo Clip (Recortar)";
"CamoHelpViewController.title" = "Switcher como cámara web";
"CamoHelpViewController.instructions.intro" = "Puede enviar audio y video de Switcher a Zoom, Google Meet, Microsoft Teams o cualquier otro software de videoconferencia en su Mac o PC. Aquí es cómo:";
"CamoHelpViewController.instructions.1.0" = "Descarga y ejecuta Camo Studio en tu Mac o PC.";
"CamoHelpViewController.instructions.1.1" = "Descarga Camo Studio en el sitio web camo.studio.";
"CamoHelpViewController.instructions.1.share-button" = "Compartir enlace de descarga";
"CamoHelpViewController.instructions.2.0" = "Conecte este iPad/iPhone a su Mac o PC.";
"CamoHelpViewController.instructions.2.1" = "Use un cable confiable (como el que vino con su iPhone). Evite el uso de un concentrador.";
"CamoHelpViewController.instructions.3.0" = "Siga las instrucciones en Camo Studio.";
"CamoHelpViewController.instructions.footnote" = "Si desea escuchar a los demás participantes, conecte los auriculares a su computadora. De lo contrario, apague los altavoces de su computadora para evitar el eco y la retroalimentación de audio.";
"news.since-version" = "13.7.0";
"news.title" = "Novedades";
"news.version-prefix" = "Versión";
"news.description" = "# Novedades\
* NUEVO recurso de presentación de diapositivas: una forma integrada de reproducir en bucle un conjunto de logotipos o imágenes\
* Verifique el almacenamiento disponible antes de iniciar una transmisión en vivo con grabación local\
* Corrección de errores y mejoras de estabilidad";
"SourcePropBundleEditor.overlay.tips" = "Se muestra una superposición sobre un activo de pantalla completa o una fuente de video.";
"SourcePropBundleEditor.chromakey.tips" = "Select the color to be removed or replaced with your background image.";
"SourcePropBundleEditor.align.vertical" = "Vertical";
"SourcePropBundleEditor.align.horizontal" = "Horizontal";
"MultiviewBehavior.title" = "Asignación de Ranuras";
"MultiviewBehavior.description" = "Asignación de Ranuras";
"MultiviewBehavior.multiview.title" = "Seleccionar cada vez";
"MultiviewBehavior.multiview.description" = "Cada vez que elijas este Multiview, se te pedirá que selecciones fuentes o activos para llenar cada ranura.";
"MultiviewBehavior.scene.title" = "Preseleccionar";
"MultiviewBehavior.scene.description" = "Primero seleccionarás las fuentes de esta Multivista en la ventana anterior de Propiedades de la Multivista (sólo fuentes de cámara y pantalla). Después, cada vez que elijas esta Multivista, todas las ranuras se llenarán automáticamente con tus selecciones.";
"MultiviewBehavior.overlay.title" = "Reutilizar la fuente actual";
"MultiviewBehavior.overlay.description" = "Primero seleccionarás todas las fuentes de esta Multivista, excepto una, en la ventana anterior de Propiedades de la Multivista. Luego, cada vez que elijas esta Multivista, la última ranura restante se llenará automáticamente con tu fuente actual (la última seleccionada). Pulsa una nueva fuente para cambiar la última ranura, o vuelve a pulsar la Multivista para eliminarla y mostrar sólo tu fuente seleccionada.";
"MultiviewBehavior.special-inputs.unknown.title" = "Desconocido";
"MultiviewBehavior.special-inputs.unassigned.title" = "Ninguno";
"MultiviewBehavior.special-inputs.overlay-background.title" = "Fuente actual";
"MultiviewBehavior.special-inputs.image.title" = "Image";
"MultiviewBehavior.special-inputs.title.title" = "Title";
"MultiviewBehavior.special-inputs.video.title" = "Video";
"IntroGenerator.editor.title" = "Crear introducción";
"IntroGenerator.selection.title" = "Video de introducción";
"IntroGenerator.processing.title" = "Espere mientras exporta el video de introducción";
"IntroGenerator.processing.error" = "No se pudo crear el video de introducción";
"ShoppingListViewController.title" = "Tarjeta";
"ShoppingList.choice.camera-as-background" = "Cámara como fondo";
"ShoppingList.choice.image-as-background" = "Imagen como fondo";
"ShoppingList.choice.multiview" = "2 Vistas";
"ShoppingList.choice.live-selling-samples" = "Muestras de venta en vivo";
"ShoppingList.submenu.title" = "Tarjeta";
"clip.export.title" = "Espera mientras finalizamos tu clip.";
"clip.export.error" = "Error al crear el clip.";
"clip.export.retry" = "Reintentar";
"clip-image-select" = "Escoger";
"clip-trim-start %@" = "Comienzo: %@";
"clip-trim-end %@" = "Fin: %@";
"clip-save" = "Guardar";
"clip-speed" = "Velocidad";
"clip-family-font" = "Fuente";
"clip-format" = "Formato";
"clip-layers" = "Capas";
"clip-image" = "Imagen";
"clip-add-image" = "Agregar Imagen";
"clip-edit-image" = "Editar Imagen";
"clip-progress" = "Barra de Progreso";
"clip-add-progress" = "Agregar Barra de Progreso";
"clip-edit-progress" = "Editar Barra de Progreso";
"clip-title" = "Título";
"clip-add-title" = "Agregar Título";
"clip-edit-title" = "Editar Título";
"clip-final-duration" = "Duración Final";
"clip-reset-speed" = "Restablecer Velocidad";
"clip-layer %lld" = "CAPAS (%lld)";
"clip-add-layer" = "Agregar Capa";
"clip-no-layer" = "No hay capa todavia";
"clip-tap-edit" = "TEXTO (TOCA PARA EDITAR)";
"clip-font" = "LETRA";
"clip-background" = "FONDO";
"clip-position" = "POSICIÓN";
"clip-adjust" = "Ajustar";
"clip-add" = "Agregar";
"clip-apply" = "Aplicar";
"clip-create" = "Crear";
"clip-framing" = "Marco";
"clip-zoom" = "Zoom";
"clip-background-color" = "Color de Fondo";
"clip-layer-edit" = "Editar";
"clip-layer-delete" = "Eliminar";
"clip-image-size" = "Tamaño";
"clip-dimension" = "Dimension";
"clip-height" = "Altura";
"clip-font-family" = "Familia";
"clip-font-color" = "Color";
"clip-font-alignment" = "Alineación";
"clip-font-size" = "Tamaño";
"clip-position-horizontal" = "Horizontal";
"clip-position-vertical" = "Vertical";
"clip-position-spacing" = "Espaciado";
"clip-color" = "Color";
"clip-speed-info %lf" = "%.1lfx";
"clip-caption-title" = "Subtítulos";
"clip-caption-auto-title" = "Generación de subtítulos";
"clip-caption-auto-subtitle" = "Usamos el reconocimiento de voz en su video para generar subtítulos automáticamente. El reconocimiento está fuera de línea y sus datos permanecen en su teléfono.";
"clip-caption-auto-no-language" = "No hay idioma disponible.";
"clip-caption-auto-language-subtitle" = "Elija el idioma que se habla en su video para que podamos transcribirlo correctamente.";
"clip-caption-ask-permission-detail" = "Para iniciar los subtítulos automáticos, primero debe dar su permiso para usar el reconocimiento de voz. Haga clic primero en \"Iniciar reconocimiento\" para dar su permiso.";
"clip-caption-ask-permission" = "Iniciar reconocimiento (pedir permiso)";
"clip-caption-permission-denied" = "Configuración abierta";
"clip-caption-permission-denied-detail" = "El acceso al reconocimiento de voz está deshabilitado. Puede abrir la página Configuración y luego habilitar el Reconocimiento de voz.";
"clip-caption-adjust-warning" = "Tienes subtítulos adjuntos a este video. Si recorta su video, se eliminarán todos los subtítulos.";
"clip-caption-auto-error" = "¡Ups! Se produjo un error en el servicio de transcripción.";
"clip-caption-auto-error-nocaption" = "¡Ups! No se detectaron subtítulos en el audio.";
"clip-caption-auto %lld" = "%lld título(s) generado(s)";
"clip-caption-auto-analyze" = "Proceso de inicio de reconocimiento de voz...";
"clip-caption-auto-success" = "Proceso Exitoso";
"clip-caption-auto-cancel" = "Proceso cancelado";
"clip-caption-auto-start" = "Iniciar reconocimiento";
"transcript-generate" = "Generate";
"clip-caption-auto-waiting" = "Espere, el reconocimiento de voz está en progreso. No cierres la aplicación.";
"clip-caption-auto-manual" = "Añadir subtítulos manualmente";
"clip-caption-use-existing" = "Use Existing Transcript";
"clip-caption-home-info" = "Arrastre la línea de tiempo y presione “+” para agregar un título en esta posición.";
"clip-caption-edit-title" = "Editar subtítulo";
"clip-caption-edit-info" = "Escribe tu texto aquí";
"clip-caption-style-title" = "Estilo";
"clip-caption-fake" = "Sus subtítulos aparecerán en este cuadro";
"clip-caption-srt-mode" = "Editar SRT";
"clip-caption-speech-recognition" = "Ejecutar reconocimiento de voz";
"clip-caption-select-language" = "Por favor elige un idioma";
"clip-caption-srt-import" = "Importar";
"clip-caption-srt-export" = "Exportar";
"AccountInfos.name" = "Name";
"AccountInfos.email" = "Email";
"AccountInfos.expiration-date.days-counting.%d" = "Expira en %d dias";
"AccountInfos.expiration-date.expired.title" = "Expira";
"AccountInfos.subscription-active.title" = "Suscripción activa";
"AccountInfos.subscription-canceled.title" = "Suscripción cancelada";
"AccountInfos.subscription-error.title" = "Acción requerida";
"AccountInfos.subscription-error.message" = "¡Ups! Se ha producido un error con su método de pago. Inicie sesión en el Dashboard para resolver este problema y continúe usando Switcher sin interrupciones.";
"AccountProductActive.expiration-date.title" = "Expira en";
"AccountProductActive.renewable-date.title" = "Renueva en";
"SubscriptionTableViewController.active-plan.title" = "Plan activo";
"SubscriptionTableViewController.available-plans.title" = "Planes Disponibles";
"BrandProfileSubtableController.name" = "Perfil de Marca";
"BrandProfileSubtableController.action.apply" = "Aplicar";
"BrandProfileSubtableController.action.configure" = "Configurar";
"PaletteSubtableController.name" = "Tema";
"PaletteSubtableController.custom" = "Personalizado";
"PaletteSubEditor.title" = "Tema";
"PaletteSubEditor.section.brand-profile" = "Perfil de Marca";
"PaletteSubEditor.section.generic" = "Genérico";
"PaletteSubEditor.action.shuffle" = "Aleatorizar Colores Seleccionados";
"target-url.ack.title" = "Aviso de configuración";
"target-url.ack.dest" = "Switcher Studio está listo para transmitir al siguiente destino:";
"target-url.ack.tip.no-user" = "Cierre este mensaje, inicie sesión en su cuenta de Switcher, luego seleccione \"Usar como Switcher\" y presione el botón 🔴 para comenzar.";
"target-url.ack.tip.user" = "Cierre este mensaje, seleccione \"Usar como conmutador\" y presione el botón 🔴 para comenzar.";
"Settings.Guide.SafeArea" = "Guía de áreas seguras";
"welcome-d3.button.manage-subscription" = "Manage Subscription";
"welcome-d3.button.delete-account" = "Borrar mi cuenta";
"tutorials.getstarted" = "Featured Tutorials";
"tutorials.title" = "View Featured Tutorials";
"tutorials.all.information" = "These videos will help you get started with some of Switcher Studio's most popular features. For more tutorials, visit our Help Center.";
"tutorials.all.title" = "Tutorial Videos";
"filter-list.vfilter-cross-zoom.title" = "Cross Zoom";
"filter-list.vfilter-cover.title" = "Cover";
"filter-list.vfilter-push.title" = "Push";
"filter-list.vfilter-split.title" = "Split";
"filter-list.vfilter-iris.title" = "Iris";
"filter-list.vfilter-star_wipe.title" = "Star Wipe";
"filter-list.vfilter-swipe.title" = "Swipe";
"filter-list.vfilter-fade.title" = "Fade";
"filter-list.vfilter-flicker.title" = "Flicker";
"filter-list.vfilter-shatter.title" = "Shatter";
"filter-list.vfilter-rolling.title" = "Rolling";
"filter-list.vfilter-whip_pan.title" = "Whip Pan";
"filter-list.vfilter-stinger.title" = "Stinger";
"vfilter-prop.fade_out_duration.title" = "Fade Out Duration";
"vfilter-prop.wait_duration.title" = "Wait Duration";
"vfilter-prop.fade_in_duration.title" = "Fade In Duration";
"vfilter-prop.fade_in_title" = "Fade In";
"vfilter-prop.fade_out_title" = "Fade Out";
"vfilter-prop.fade_setting.title" = "Audio Fading";
"vfilter-prop.logo.title" = "Logo";
"vfilter-prop.background_color.title" = "Background Color";
"vfilter-prop.external_border_color.title" = "External Border Color";
"vfilter-prop.internal_border_color.title" = "Internal Border Color";
"vfilter-prop.scale_flap.title" = "Escala de la Solapa";
"vfilter-prop.scale_inset.title" = "Escala Insertada";
"vfilter-prop.margin_flap.title" = "Margen de Solapa";
"vfilter-prop.margin_inset.title" = "Margen de Inserción";
"vfilter-prop.invert_flap.title" = "Invert Flap Position";
"vfilter-prop.zoom_max.title" = "Zoom";
"vfilter-prop.uncover.title" = "Uncover";
"vfilter-prop.reverse.title" = "Reverse";
"PlaybackSettingsSubEditorView.title" = "Playback Settings";
"PlaybackSettingsSubEditorView.play_once" = "Play Once";
"PlaybackSettingsSubEditorView.infinite" = "Infinite Loop";
"PlaybackSettingsSubEditorView.loop_count" = "Loop Count";
"PlaybackSettingsSubEditorView.loop_duration" = "Display Duration";
"PlaybackSettingsSubEditorView.repeat %d" = "Repeat %lldx";
"PlaybackSettingsSubEditorView.play_for %@" = "Play for %@";
"PlaybackSettingsSubEditorView.times" = "Times";
"AssetGridViewController.photoLibrary.manage.title" = "Manage";
"AssetGridViewController.photoLibrary.limitedPhotos.title" = "You've given Switcher access to only a selected number of photos.";
"asset.collection.no.asset" = "No asset";
"asset.collection.reorder" = "Drag & Drop to reorder";
"asset.collection.hidden.sing" = "You have 1 hidden production group.";
"asset.collection.hidden %lld" = "You have %lld hidden production groups.";
"asset.collection.manage" = "Manage";
"asset.collection.disconnect" = "Disconnect";
"asset.collection.delete.confirm.title" = "Are you sure?";
"asset.collection.delete.confirm.message.plur" = "You're about to delete %d assets.";
"asset.collection.delete.confirm.message.sing" = "You're about to delete 1 asset.";
"asset.collection.delete.confirm.button" = "Yes, delete 1 asset";
"asset.collection.delete" = "Delete";
"export.extension.title" = "Use in Switcher";
"export.extension.import.as.assets.title" = "Import as Assets";
"export.extension.import.as.assets.subtitle" = "Add images or videos to use as full-screen or overlaid assets in your productions.";
"export.extension.import.as.assets.button" = "Import";
"export.extension.upload.to.library.title" = "Upload to Video Library";
"export.extension.upload.to.library.subtitle" = "Store, edit video details, and manage your content in the Cloud Video Library.";
"export.extension.upload.to.library.button" = "Upload";
"export.upload.to.library.success" = "Videos added to your Uploads";
"export.extension.send.image.to.switcher %lld" = "Import %lld Images to Switcher";
"export.extension.send.image.to.switcher" = "Import Image to Switcher";
"export.extension.send.video.to.switcher %lld" = "Import %lld Videos to Switcher";
"export.extension.send.video.to.switcher" = "Import Video to Switcher";
"export.extension.orientation" = "Orientation";
"export.extension.thumbnail.selection" = "Thumbnail Selection";
"export.extension.thumbnail.tag" = "Thumbnail Tag";
"export.extension.video.audio-only" = "Audio Only";
"export.image.import.success %lld" = "%lld images imported successfully";
"export.image.import.success %lld %lld" = "%lld / %lld images imported successfully";
"export.image.import.success" = "Image imported successfully";
"export.video.import.success %lld" = "%lld videos imported successfully";
"export.video.import.success %lld %lld" = "%lld / %lld videos imported successfully";
"export.video.import.success" = "Video imported successfully";
"export.mixed-content.warning" = "We have a little problem . . .\n\nIn order to batch import to Switcher Studio, please ensure your selections are all the same media type.";
"export.multiple-videos.warning" = "We have a little problem...\n\nIn order to upload to the Video Library, please ensure you've selected only one video.\nUploading multiple videos simultaneously is unsupported at this time.";
"tag.golive" = "Go live or start recording to add a timestamp.";
"tag.notag" = "No timestamp";
"tag.manual" = "Manage";
"tag.automatic" = "Automatic";
"tag.add.manual.tag" = "Add New Manual Timestamp";
"tag.manage" = "Manage";
"tag.count %lld" = "All Timestamps (%lld)";
"tag.toast.message" = "Timestamp added";
"tag.manual.description" = "Manual Timestamp (%@)";
"tag.jump.to" = "Jump to...";
"summary.prod.title" = "Your video is ready!";
"summary.prod.share.link" = "Share Broadcast Link";
"summary.prod.export.record" = "Export Recorded File";
"summary.prod.create.clip" = "Create Clip";
"summary.prod.director.mode" = "Director Mode Files";
"summary.prod.leave.studio" = "Leave Studio";
"summary.prod.loading" = "One moment — your video is processing.";
"summary.clip.title" = "Your clip is ready!";
"summary.clip.loading" = "One moment — your clip is processing.";
"summary.tooltip.timestamp" = "Use your timestamps to jump to moments you want to clip.";
"summary.tooltip.videolibrary" = "NEW: Upload your recording to your Video Library, and then visit the Dashboard to add it to your Switcher Player.";
"clip.tooltip.timestamp" = "Review 1 timestamp";
"clip.tooltip.timestamps %lld" = "Review %lld timestamps";
"exporter.title" = "Export";
"exporter.more.options" = "More Sharing Options...";
"exporter.poto.library" = "Send to Photo Library";
"exporter.files" = "Save to Files";
"exporter.city.producer" = "Send to CTpro";
"ProdSummaryViewController.state1.cancel.confirmation.title" = "¡Estás tan cerca! ¿Estas seguro que quieres cancelar?\n Su video aún se está transfiriendo al sitio en el que se está reproduciendo.\n Cancelar ahora acortará su transmisión.";
"ProdSummaryViewController.state1.cancel.button.confirmation.title" = "Si, cancelar";
"ProdSummary.videolibrary.info" = "Uploaded to your Video Library";
"ProdSummary.localrecording.info" = "Saved in Local Recordings";
"ProdSummary.practice.title" = "Practice Livestream Complete";
"ProdSummary.practice.text" = "A recording can be privately watched via your online Dashboard at **switcherstudio.com** for the next 14 days.";
"ProdSummary.practice.go.home" = "Go Back Home";
"clip.trim.start" = "Begin Clip";
"clip.trim.end" = "End Clip";
"clip.caption.trim.start" = "Begin Caption";
"clip.caption.trim.end" = "End Caption";
"auto-dismiss.property.enable.title" = "Auto Dismiss";
"auto-dismiss.property.delay.title" = "Display Duration";
"auto-dismiss.property.delay.none" = "Disabled";
"auto-dismiss.property.tip" = "Auto Dismiss removes timers, prerecorded videos, and non-looping animated text from the screen upon completion.";
"auto-dismiss.delay-editor.title" = "Display Duration";
"auto-dismiss.delay-editor.enable.title" = "Enable";
"auto-dismiss.delay-editor.delay.header" = "Duration";
"auto-dismiss.delay-editor.delay.tip" = "Display Duration determines how long images, lower thirds, titles, logos, social and donation overlays, and looping animated text are shown on screen.";
"sharing-choice.button.share-seemo.start" = "Share SeeMo";
"sharing-choice.button.share-seemo.subtitle" = "Utilice la cámara HDMI como fuente en su producción.";
"sharing-choice.button.share-ext-camera.start" = "Share USB Video Source";
"sharing-choice.button.share-ext-camera.subtitle" = "Use the USB Webcam or Capture Device in your production.";
"seemo-settings.title" = "Accsoon SeeMo Settings";
"seemo-settings.latency" = "HDMI Camera Latency";
"seemo.settings.footer" = "This setting only applies to the camera connected to this device through Accsoon SeeMo.";
"seemo.addsource.hdmi.title" = "HDMI Camera/Source via Accsoon SeeMo";
"seemo.addsource.helper.description" = "Use Accsoon SeeMo to connect DSLR cameras and other HDMI sources to Switcher.\r\r\rOnce your source is connected to this device via Accsoon SeeMo, it will appear in the Inputs tab as the built-in camera.\r\r\rAccsoon SeeMo sources can also be used as remote cameras within Switcher. To use an Accsoon SeeMo source as a remote camera, connect your DSLR or other HDMI source to an auxiliary iPhone or iPad running Switcher and control it remotely using this device.";
"MyRecordingTableViewController.edit.title" = "Seleccione";
"MyRecordingTableViewController.edit.rename.title" = "Renombrar";
"MyRecordingTableViewController.edit.trash.confirm.title" = "Eliminar la grabación";
"MyRecordingTableViewController.edit.trash.confirm-multi.title" = "Eliminar %d grabaciones";
"MyRecordingTableViewController.edit.rename.alert.msg" = "Incapaz de renombrar";
"MyRecordingTableViewController.title" = "Grabaciones Locales";
"MyRecordingTableViewController.edit.select-all.title" = "Seleccionar todo";
"MyRecordingTableViewController.edit.deselect-all.title" = "Deselect All";
"MyRecording.Delete.title.sing" = "Delete Video?";
"MyRecording.Delete.title.plur %lld" = "Delete %lld Videos?";
"MyRecording.Delete.body" = "Deleting cannot be undone. Are your sure you want to continue?";
"MyRecordings.no-asset.title" = "No recordings to show";
"MyRecordings.no-asset.subtitle" = "Go live or record a video, then find it here after your production ends. Edit, manage, and share recordings from this panel.";
"MyRecordings.no-asset.hint.guest" = "Empieza ahora";
"MyRecordings.no-asset.hint.loggedIn" = "Crea tu primer vídeo";
"MyRecordings.select.title" = "Select Recordings";
"MyRecordings.select.sing" = "1 recording selected";
"MyRecordings.select.plur %lld" = "%lld recordings selected";
"MyRecordingTableViewCell.delete" = "Eliminar";
"MyRecordings.sort.title" = "Sort By";
"MyRecordingTableViewController.edit.tools.title" = "Herramientas";
"MyRecordings.sort.creation.date" = "Date Created";
"MyRecordings.sort.modification.date" = "Date Modified";
"MyRecordings.sort.name" = "Name";
"MyRecordings.export.live.output" = "Export Live Recording";
"MyRecordings.delete.live.output" = "Delete Live Recording";
"MyRecordings.export.clip" = "Export Clip";
"MyRecordings.export.video" = "Export Video";
"MyRecordings.export.audio" = "Export Audio";
"MyRecordings.clips.creation.info" = "Create video clips from your original recording.";
"MyRecordings.clip.source %@" = "Source: %@";
"MyRecordings.clip.create.menu" = "Create a clip for :";
"MyRecordings.clip.create.menu.others" = "Angles";
"MyRecordings.clip.create" = "Start Clipping";
"MyRecordings.select.clip.title" = "Select Clips";
"MyRecordings.select.clip.sing" = "1 clip selected";
"MyRecordings.select.clip.plur %lld" = "%lld clips selected";
"MyRecordings.banner.text" = "Now you can upload directly to the Dashboard video library.";
"MyRecordings.banner.learn-more" = "Learn More...";
"MyRecordings.banner.webview.title" = "Upload to Library";
"MyRecordings.last-uploaded" = "You uploaded this video %@";
"MyRecordings.transcript" = "Transcript";
"MyRecordings.transcript.edited" = "Edited";
"MyRecordings.transcript.generated" = "Generated automatically";
"MyRecordings.transcript.empty" = "No transcript available for this video.";
"MyRecordings.transcript.menu.copy" = "Copy Text";
"MyRecordings.transcript.menu.edit" = "Edit";
"MyRecordings.transcript.menu.export" = "Export .SRT";
"MyRecordings.transcript.menu.import" = "Import .SRT";
"MyRecordings.transcript.menu.delete" = "Delete Transcript";
"MyRecordings.transcript.menu.delete.confirmation.title" = "Delete Transcript?";
"MyRecordings.transcript.menu.delete.confirmation.subtitle" = "Note: You can generate or import a new transcript at any time.";
"MyRecordings.transcript.menu.delete.cloud.confirmation.subtitle" = "Note: You can import a new transcript at any time.";
"MyRecordings.transcript.menu.delete.progress.title" = "Please wait...";
"MyRecordings.transcript.menu.delete.progress.subtitle" = "Don’t close the app or put it in the background.";
"MyRecordings.transcript.edit.title" = "Edit Transcript";
"MyRecordings.transcript.tooltip" = "On your device, go to **Settings** > **Accessibility** > **Voice Control** > **Language** to add new languages.";
"MyRecordingTableViewCell.mode.live" = "En Vivo";
"MyRecordingTableViewCell.mode.director-mode-recording" = "Grabación MD";
"MyRecordingTableViewCell.mode.director-mode-composition" = "Composición MD";
"MyRecordingTableViewCell.mode.channel" = "Canal";
"MyRecordingTableViewCell.mode.channels" = "Canales";
"MyRecordingTableViewCell.mode.clip" = "Clip";
"MyRecordingTableViewCell.mode.clips" = "Clips";
"MyRecordingTableViewCell.rename" = "Renombrar";
"MyRecordingModeTableViewController.mode.live" = "Grabación en Vivo";
"MyRecordingModeTableViewController.mode.director-mode-composition" = "Composición Modo Director";
"MyRecordingModeTableViewController.mode.director-mode-recording" = "Grabación Modo Director";
"MyRecordingModeTableViewController.mode.clips" = "Clips";
"asset.cloud.error" = "An unexpected error occurred. Please try again.";
"asset.cloud.emptystate.title" = "Nothing to see here yet";
"asset.cloud.emptystate.description" = "To upload assets to the Switcher Cloud: go to the sources panel, tap Select, choose your desired assets, and then tap the cloud icon.";
"asset.upload.conflict.title" = "Resolve Conflict";
"asset.download.conflict.download" = "Download & Replace";
"asset.download.conflict.keep" = "Keep Local Version";
"asset.download.conflict.exist" = "This file already exists locally";
"mediagrid.empty.title" = "No image";
"prod-flow.photo.button.more.short" = "More";
"prod-flow.photo.button.more.long" = "More Options...";
"social-platform-icons-donation" = "DONATION";
"social-platform-icons-social" = "SOCIAL";
"social-platform-icons-title" = "Platform Icons";
"asset.upload.conflict.download" = "Upload & Replace";
"asset.upload.conflict.keep" = "Mantener la versión Cloud";
"asset.upload.conflict.exist" = "Este archivo ya existe en Switcher Cloud";
"asset.upload.progress.title" = "One moment — your assets are uploading.";
"asset.upload.success.title" = "Asset uploaded";
"asset.download.success.title" = "Asset downloaded";
"asset.download.group.success.title" = "Group downloaded";
"asset.upload.cancel" = "Cancel Upload";
"asset.upload.delete.local" = "Delete Local Assets";
"asset.upload.keep.local" = "Keep Local Assets";
"asset.upload.error.title" = "An error occurred";
"asset.conflict.sing.title" = "1 conflicted file";
"asset.conflict.plur.title %lld" = "%lld conflicted files";
"asset.upload.cloud" = "Cloud";
"asset.upload.local" = "Local";
"asset.upload.delete.sing.information" = "Activo eliminado";
"asset.upload.delete.plur.information" = "%d activos eliminados";
"asset.download.delete.cloud.selection" = "Seleccionar activos";
"asset.download.delete.cloud.selected.plur %lld" = "%lld Activos seleccionados";
"asset.download.delete.cloud.selected.sing" = "1 Activo seleccionado";
"asset.download.delete.cloud.confirm" = "Está a punto de eliminar permanentemente unos activos de Switcher Cloud. ¿Seguro que quieres seguir?";
"asset.download.delete.cloud.title" = "Un momento — sus activos de Switcher Cloud se están eliminando.";
"global.form.fieldrequired %@" = "%@ is required";
"global.alertview.retry" = "Retry";
"global.alertview.waiting" = "Please do not lock screen or leave the app.";
"global.alertview.error.title" = "Oops";
"videolibrary.title" = "Upload Video";
"videolibrary.title.plural" = "Upload Videos";
"videolibrary.videotoolong.title" = "Video is too big";
"videolibrary.videotoolong.description" = "Maximum video duration is 30 GB";
"videolibrary.videonotfound.title" = "Video not found";
"videolibrary.videonotfound.description" = "Cannot export the file to the video library.";
"videolibrary.badformat.title" = "Format not supported";
"videolibrary.badformat.description" = "Cannot export the file to the video library. The encoding of the video is not supported (supported encoding: h264 and h265)";
"videolibrary.upload.progress.title" = "One moment — your video is uploading";
"videolibrary.form.file" = "File:";
"videolibrary.form.name" = "Name";
"videolibrary.form.name.placeholder" = "Give your video a name";
"videolibrary.form.description" = "Description";
"videolibrary.form.description.placeholder" = "Let viewers know what your video is about (optional).";
"videolibrary.form.visibility" = "Visibility";
"videolibrary.form.showInCatalog" = "Show In Video Catalog";
"videolibrary.form.collections" = "Collections";
"videolibrary.form.tags" = "Tags";
"videolibrary.form.transcript.label" = "Transcript";
"videolibrary.form.transcript.choose.label" = "Choose an .SRT file on your device.";
"videolibrary.form.transcript.replace.label" = "Replace existing transcript with an .SRT file.";
"videolibrary.form.include.transcript" = "Include existing video transcript.";
"videolibrary.form.transcript.menu.replace.label" = "Replace File";
"videolibrary.form.upload.button" = "Upload";
"videolibrary.form.explaination" = "Want this video on your website? Visit the Switcher Dashboard after your upload completes and add it to a Switcher Player playlist.";
"videolibrary.multiExport.error.description.sing" = "A video has been removed from your selection (unsupported encoding).";
"videolibrary.multiExport.error.description.plur" = "Some videos have been removed from your selection (size or encoding not supported).";
"videolibrary.uploads.toast.completed" = "Upload complete.\nVideo is now available in your library.";
"videolibrary.uploads.toast.failed" = "Upload failed. Please try again.";
"videolibrary.uploads.paused" = "Paused";
"videolibrary.uploads.failed" = "Upload Failed";
"videolibrary.uploads.success" = "Uploaded";
"menu.section.profile" = "PERFIL";
"menu.section.support" = "SOPORTE";
"menu.support.tutorials" = "Tutoriales en Vídeo";
"menu.section.more" = "MÁS";
"menu.logout.confirm" = "¿Estás seguro de que quieres cerrar sesión?";
"menu.manage.beta.features" = "Beta Features";
"menu.manage.beta.features.disclaimer" = "The following experimental features are in beta stage, meaning they could cause unexpected behavior when in use. You may preview these features and provide feedback, but generally we do not provide support for beta features.";
"menu.manage.beta.features.empty.title" = "Welcome to Beta Features";
"menu.manage.beta.features.empty.content" = "There are no beta features currently available. Check back soon for future updates.";
"menu.manage.beta.features.get.features.error.title" = "Unable to load the page";
"menu.manage.beta.features.get.features.error.detail" = "Your internet connection appears to be offline. Check your network settings and try again.";
"menu.manage.beta.features.get.features.error.retry" = "Retry";
"usermain.subscribe.banner.title" = "You don't have an active subscription right now.";
"usermain.subscribe.banner.button" = "Subscribe";
"usermain.storageFull.banner.title %lld %lld" = "**Attention** – You have reached your overall storage limit of video uploads (%lld of %lld).";
"usermain.storageWarning.banner.title %lld %lld" = "**Attention** – You are approaching your overall storage limit of video uploads (%lld of %lld).";
"menu.subscription.multiPlan.title" = "Choose a plan";
"menu.subscription.trial.title" = "Your first 14 days for free";
"menu.subscription.trial.subtitle" = "Pay nothing today and cancel anytime";
"menu.subscription.trial.subscribe" = "Start 14-Day Trial";
"menu.subscription.trial.subscribeTo %@" = "Subscribe To %@";
"menu.subscription.trial.comment" = "Billing begins when your free trial ends. Cancel before trial ends and you won’t be charged. Subscription automatically renews monthly until you cancel. Cancel anytime in your Apple account.";
"menu.subscription.title" = "Start creating with Switcher";
"menu.subscription.subtitle" = "No commitment. Cancel anytime";
"menu.subscription.price %@" = "Renews automatically at %@";
"menu.subscription.priceMonthly %@" = "%@/month";
"menu.subscription.perMonth" = "/month";
"menu.subscription.error" = "Something went wrong.";
"menu.subscription.subscribe" = "Subscribe Now";
"menu.subscription.subscribeTo %@" = "Subscribe To %@";
"menu.subscription.comment" = "Subscription automatically renews monthly until you cancel. Cancel anytime in your Apple account.";
"menu.subscription.feature.multicam" = "Multicamera, multisource video";
"menu.subscription.feature.multistreaming" = "Built-in Multistreaming";
"menu.subscription.feature.graphics" = "Built-in graphics templates";
"menu.subscription.feature.guests" = "Invite up to five remote guests";
"menu.subscription.termsofservice" = "Terms of Service";
"menu.subscription.privacypolicy" = "Privacy Policy";
"menu.subscription.restore" = "Restore Purchase";
"menu.subscription.current.plan" = "My plan";
"menu.subscription.available.plans" = "Available plan";
"menu.subscription.extra.informations" = "Please log in to **switcherstudio.com** to manage your subscription.";
"menu.subscription.start.trial" = "Start Free Trial";
"menu.subscription.manage" = "Manage Subscription";
"menu.subscription.cancel" = "Cancel Subscription";
"menu.subscription.skip.confirmation.title" = "Your account is created, but you won't have full access to all the great features until you subscribe.";
"menu.subscription.skip.confirmation.button" = "Finish Later";
"menu.subscription.skip.confirmation.cancel" = "Resume";
"videolibrary.selector.localrecording" = "Videos created with Switcher Studio on this device";
"videolibrary.selector.cloudvideos" = "Cloud videos that can be added to a Switcher Player";
"videolibrary.processing" = "Your video is processing...";
"videolibrary.login.title" = "Log in to see your videos";
"videolibrary.login.subtitle" = "Once you log in, you’ll find all your videos here.";
"videolibrary.video.error.description" = "Processing error";
"videolibrary.error.title" = "Unable to load your videos";
"rtmp.channel.error.title" = "Unable to load your channels";
"videolibrary.error.subtitle" = "Your Internet connection appears to be offline. Check your network settings and try again.";
"videolibrary.error.button" = "Try again";
"videolibrary.empty.title" = "Take Control of Your Content";
"videolibrary.empty.subtitle" = "Safely store all your videos on the cloud for access to every Switcher feature across all of your devices.";
"videolibrary.empty.hint" = "Upload or create your first video";
"videolibrary.empty.search.title" = "No videos found";
"videolibrary.empty.search.subtitle" = "We couldn’t find any videos that match your criteria.";
"videolibrary.empty.clearfilters" = "Clear Filters";
"videolibrary.download.button" = "Download";
"videolibrary.download.success" = "Video Saved to Camera Roll";
"videolibrary.download.error" = "Cannot Save Video to Camera Roll";
"videolibrary.share.button" = "Share Link";
"videolibrary.select.title" = "Select Videos";
"videolibrary.select.sing" = "1 Video Selected";
"videolibrary.select.plur %d" = "%d Videos Selected";
"videolibrary.sort.nameAZ" = "Title A-Z";
"videolibrary.sort.nameZA" = "Title Z-A";
"videolibrary.sort.newest" = "Newest First";
"videolibrary.sort.oldest" = "Oldest First";
"videolibrary.sort.leastviews" = "Least Views";
"videolibrary.sort.mostviews" = "Most Views";
"videolibrary.default.label" = "(default)";
"videolibrary.delete.cloud.progress" = "Deleting...";
"videolibrary.delete.cloud.error.sing" = "Unable to Delete Video";
"videolibrary.delete.cloud.error.plur" = "Unable to Delete Videos";
"videolibrary.delete.success.sing" = "Video deleted";
"videolibrary.delete.success.plur" = "Videos deleted";
"videolibrary.tooltip.title" = "Change Library";
"videolibrary.tooltip.description" = "Switch between your Video Library and Local Recordings here.";
"videolibrary.details.edit" = "Edit Details";
"videolibrary.details.edit.event" = "Edit Livestream Details";
"videolibrary.details.edit.event.save.button" = "Save Changes";
"videolibrary.details.thumbnail" = "Thumbnail";
"videolibrary.thumbnail.rec.size" = "Recommended size is 1920 x 1080 pixels.";
"videolibrary.details.thumbnail.frame" = "Select Video Frame";
"videolibrary.details.edit.confirm.cancel" = "Are you sure you want to discard your changes?";
"videolibrary.details.edit.confirm.cancel.discard" = "Discard Changes";
"videolibrary.details.edit.confirm.cancel.keepediting" = "Keep Editing";
"videolibrary.details.edit.saving" = "Saving changes";
"videolibrary.details.nodescription" = "No Description";
"videolibrary.details.collectionsandpasses" = "Collections & Passes";
"videolibrary.details.collections.empty" = "Not used in any collections yet";
"videolibrary.details.players.plur %lld" = "%lld Players";
"videolibrary.details.gating" = "Gating Options";
"videolibrary.details.pass.empty" = "No one-time passes yet";
"videolibrary.details.pass.sing" = "1 One-Time Pass";
"videolibrary.details.pass.plur %lld" = "%lld One-Time Passes";
"videolibrary.details.pass.password" = "Password";
"videolibrary.details.password.access" = "Enable Password Access";
"videolibrary.details.password.explain" = "Viewers who enter password will bypass email entry and purchase.";
"videolibrary.details.password.create" = "Create a password";
"videolibrary.details.email.access" = "Require Email Address to View";
"videolibrary.details.email.explain" = "Automatically collected with purchase.";
"videolibrary.details.analytics.tip" = "Data does not include plays from social platforms.";
"videolibrary.details.about" = "Information";
"videolibrary.details.about.size" = "Size";
"videolibrary.details.about.size.unknown" = "Unknown";
"videolibrary.details.about.create" = "Created";
"videolibrary.details.about.dimensions" = "Dimensions";
"videolibrary.details.about.dimensions.processing" = "Processing...";
"videolibrary.details.about.links" = "Interactive Links";
"videolibrary.details.analytics" = "Analytics";
"videolibrary.details.analytics.all_time" = "Lifetime views";
"videolibrary.details.analytics.seven_days" = "Last 7 days";
"videolibrary.details.premiering" = "Premiering";
"videolibrary.details.scheduled" = "Scheduled";
"videolibrary.details.unpublished" = "This video is unpublished";
"videolibrary.details.unpublished.short" = "Unpublished";
"videolibrary.details.published.short" = "Published";
"videolibrary.details.catalog.link.title" = "Want this video on your website?";
"videolibrary.details.catalog.link.description" = "Learn how to make your videos available on your website by using Video Catalog and Collections.";
"videolibrary.setvisibility.title" = "Video Visibility";
"videolibrary.visibility.published.title" = "Publish";
"videolibrary.visibility.published.subtitle" = "Your audience can watch this video.";
"videolibrary.visibility.scheduled.title" = "Schedule Premiere";
"videolibrary.visibility.scheduled.subtitle" = "Publish your video at a specific date and time.";
"videolibrary.visibility.scheduled.when" = "When";
"videolibrary.visibility.unpublished.title" = "Unpublished";
"videolibrary.visibility.unpublished.subtitle" = "Only you can watch this video inside your Switcher account.";
"videolibrary.visibility.upload.published.title" = "Publish Now";
"videolibrary.visibility.upload.scheduled.title" = "Schedule Premiere";
"videolibrary.visibility.upload.unpublished.title" = "Save as Unpublished";
"videolibrary.visibility.unpublished.alert.title" = "Attention";
"videolibrary.visibility.unpublished.alert.message" = "You are unpublishing a video that some of your audience may be paying for access to. If you continue, they will no longer be able to watch this content unless you republish.";
"videolibrary.upload.library" = "Camera Roll";
"videolibrary.upload.complete" = "Upload Complete";
"videolibrary.upload.files" = "Choose From Files";
"videolibrary.upload.title" = "Upload to Video Library";
"videolibrary.gorecordings.title" = "Go to Local Recordings";
"videolibrary.finalizevideo.title" = "Finalize Video";
"videolibrary.schedule.next.event.title" = "Schedule Next Livestream";
"videolibrary.thumbnail.choose" = "Choose Image";
"videolibrary.thumbnail.frame" = "Video Frame";
"videolibrary.thumbnail.photo" = "Take Photo";
"videolibrary.thumbnail.frame.title" = "Select Video Frame";
"videolibrary.thumbnail.reset" = "Reset Default";
"videolibrary.filterby" = "Filters";
"videolibrary.tags" = "Tags";
"videolibrary.filterby.all" = "All";
"videolibrary.filterby.inplayer" = "In Collection";
"videolibrary.filterby.notinplayer" = "Not in Collection";
"videolibrary.filterby.gated" = "Gated";
"videolibrary.filterby.notgated" = "Not Gated";
"videolibrary.filterby.published" = "Published";
"videolibrary.filterby.unpublished" = "Unpublished";
"videolibrary.filterby.scheduled.for.premiere" = "Scheduled for Premiere";
"videolibrary.search.title" = "Search";
"videolibrary.upload.thumbnail.error" = "Failed to upload thumbnail. Please try again.";
"videolibrary.manage" = "Manage Video Library";
"videolibrary.warning.full.title" = "Attention";
"videolibrary.warning.full.description %lld %lld" = "You have reached your overall storage limit of video uploads (%lld of %lld). If you proceed with the current output selection for this livestream, **the oldest video in your Video Library will be automatically deleted.**";
"videolibrary.tags.emptystate.title" = "No tags";
"videolibrary.tags.emptystate.filter.description1" = "Tags allow you to group and filter your videos for easier content management.";
"videolibrary.tags.emptystate.filter.description2" = "You can create your first tag by editing the details of any video you've created or uploaded.";
"videolibrary.categories.emptystate.description" = "Start by creating one, it's a great way to organize and filter your videos.";
"videolibrary.tags.add.title" = "Add Tags";
"videolibrary.tags.add.new" = "New Tag";
"videolibrary.tags.edit.title" = "Edit Tags";
"videolibrary.tags.rename.title" = "Rename Tag";
"videolibrary.tags.delete" = "Delete Tag";
"videolibrary.tags.delete.title" = "Delete Tag?";
"videolibrary.tags.delete.msg %@" = "Are you sure you want to delete %@?";
"videolibrary.tags.title" = "Tag";
"videolibrary.tags.noconnection.title" = "Unable to Load Tags";
"videolibrary.tags.add.error.title" = "Unable to Create Tag";
"videolibrary.tags.add.error.subtitle" = "Something went wrong while creating your new tag.";
"videolibrary.tags.videocount_zero" = "No videos";
"videolibrary.tags.videocount_singular" = "1 video";
"videolibrary.tags.videocount_plural %lld" = "%lld videos";
"videolibrary.categories.add" = "Add";
"videolibrary.cloudvideo.view_singular" = "1 View";
"videolibrary.cloudvideo.view_plural %lld" = "%lld Views";
"videolibrary.loading.fail" = "Loading failed. **Tap to Retry**";
"videolibrary.loading.finished" = "No more videos to show.";
"stream.settings.advanced.title" = "Advanced Settings";
"stream.settings.advanced.subtitle" = "RTMP advanced settings only apply when using Custom RTMP mode. For Livestreaming, stream details will be used.";
"survey.onboarding.title" = "Tell Us About Yourself";
"survey.exit.title" = "Unsure about your Subscription?";
"survey.button.continue" = "Continue";
"survey.button.done" = "Done";
"survey.thankyou.title" = "Thank you!";
"survey.thankyou.text" = "We appreciate your time.\rYour responses will help shape the future of Switcher.";
"survey.thankyou.continue" = "Continue to the app";
"survey.page0.title" = "How would you describe yourself?";
"survey.page0.subtitle" = "Take a moment to help us get to know you.";
"survey.page0.option1" = "Business Owner";
"survey.page0.option2" = "Marketing Professional";
"survey.page0.option3" = "Influencer/Creator";
"survey.page0.option4" = "Hobbyist";
"survey.page0.option5" = "Other (please specify)";
"survey.page1.title" = "What brings you to Switcher?";
"survey.page1.subtitle" = "Select all that apply.";
"survey.page1.option1" = "Recording videos";
"survey.page1.option2" = "Livestreaming on social media";
"survey.page1.option3" = "Charging viewers to watch my content";
"survey.page1.option4" = "Embedding videos on my website";
"survey.page1.website_query" = "www.website.com (optional)";
"survey.page1.website_query_prompt" = "What's your website URL?";
"survey.page2.title" = "Do you have any existing videos to upload?";
"survey.page2.subtitle" = "And if yes, how many?";
"survey.page2.option1" = "No, I don't have any existing videos";
"survey.page2.option2" = "Fewer than 10 videos";
"survey.page2.option3" = "Between 10 and 50 videos";
"survey.page2.option4" = "Between 51 and 100 videos";
"survey.page2.option5" = "More than 100 videos";
"survey.page3.title" = "Where will your viewers watch your videos?";
"survey.page3.subtitle" = "Select all that apply.";
"survey.page3.option1" = "My Website";
"survey.page3.option2" = "Facebook";
"survey.page3.option3" = "YouTube";
"survey.page3.option4" = "Twitch";
"survey.page3.option5" = "TikTok";
"survey.page3.option6" = "Instagram";
"survey.page3.option7" = "Other (please specify)";
"survey.page3.where_query" = "Other (please specify)";
"survey.page4.title" = "How did you find out about Switcher?";
"survey.page4.subtitle" = "This is the last question and you're all set.";
"survey.page4.option1" = "Online Search";
"survey.page4.option2" = "App Store";
"survey.page4.option3" = "Social Media";
"survey.page4.option4" = "Digital Ad";
"survey.page4.option5" = "Personal Recommendation";
"survey.page4.option6" = "Blog Post";
"survey.page4.option7" = "Other (please specify)";
"ElementMainView.loop_count %d" = "Loop %dx";
"Eptz.Help" = "Once zoomed, drag left or right to pan and up or down to tilt.";
"Eptz.Help.Button" = "Tap To Dismiss";
"menu.message.expired_account" = "Your account has expired.";
"menu.message.no_active_subscriptions" = "No active subscription.";
"menu.message.on_a_trial" = "You're on a trial.";
"menu.message.csm-hubspot.title" = "Need help getting started?";
"menu.message.csm-hubspot.subtitle" = "Book time with an expert";
"menu.message.csm-hubspot.webview.title" = "Schedule a Call";
"menu.platforms" = "Platforms";
"menu.platforms.connect.message" = "Connect and manage your external streaming destinations or stream to nearly any platform using a Stream Key and Server URL provided by that platform.";
"social.platforms.error.title" = "Unable to load external destinations";
"social.platform.error.subtitle" = "Your internet connection appears to be offline. Check your network settings and try again.";
"SwitcherNavigationActionSelectorView.streaming" = "Go Live";
"SwitcherNavigationActionSelectorView.practice" = "Practice";
"SwitcherNavigationActionSelectorView.rec" = "Record Video";
"SwitcherNavigationActionSelectorView.upload" = "Upload Video";
"SwitcherNavigationOrientationSelectorView.enter" = "Enter Studio";
"Show.Instructions" = "Show Instructions";
"studio.loading.fail" = "Loading failed. **Tap to Close**";
"studio.share.title" = "Share Link";
"studio.share.practice.title" = "Share Private Link to Watch";
"SwitcherNavigationOrientationSelectorView.no.event.found.title" = "No Livestreams";
"SwitcherNavigationOrientationSelectorView.no.event.found.message" = "When you’re ready to go live, you can create a new livestream from the Outputs tab.";
"SwitcherNavigationOrientationSelectorView.practice.text" = "Test out your livestream and familiarize yourself with all the features of Switcher. Practice streams will only be visible to you, and recordings can be watched in your online dashboard for 14 days.";
"videolibrary.addtoplayers.addtocollections.nav.title" = "Add to Collections";
"videolibrary.addtoplayers.videocount_singular" = "1 Video";
"videolibrary.addtoplayers.videocount_plural %lld" = "%lld Videos";
"videolibrary.addtoplayers.playertitle.placeholder.label" = "Example Player";
"videolibrary.addtoplayers.information.text" = "This livestream will automatically stream to your Video Library. Visit your account at **switcherstudio.com** to manage your Video Catalog and collections.";
"videolibrary.addtoplayers.information.text.replay" = "This replay is automatically available in your Video Library. Visit your account at **switcherstudio.com** to manage your Video Catalog and collections.";
"videolibrary.addtocollections.button.text" = "Add to Collections";
"videolibrary.addtocollections.count_singular" = "1 Collection";
"videolibrary.addtocollections.count_plural %lld" = "%lld Collections";
"videolibrary.addtoplayers.load.error.title" = "Unable to Load Collections";
"videolibrary.addtoplayers.load.error.text" = "Your Internet connection appears to be offline. Check your network settings and try again.";
"videolibrary.addtoplayers.create.error.title" = "Unable to Create Collection";
"videolibrary.addtoplayers.create.error.text" = "Your Internet connection appears to be offline. Check your network settings and try again.";
"videolibrary.addtoplayers.empty.title" = "No collections yet";
"videolibrary.addtoplayers.empty.subtitle" = "Organize livestreams and videos from your library into collections that can be embedded on your website or shared using a dedicated watch page.";
"videolibrary.addtoplayers.create" = "Create Collection";
"videolibrary.addtoplayers.new" = "New Collection";
"videolibrary.upload.background.tooltip.title" = "Track Your Upload Progress";
"videolibrary.upload.background.tooltip.description" = "Keep the app open to ensure smooth uploading. Uploads will pause if you leave the app or create a new video.";
"videolibrary.uploads.title" = "Uploads";
"videolibrary.uploads.storagelimit" = "Storage limit reached";
"videolibrary.uploads.storagelimit.description" = "Uploads will resume when space is available in your Video Library.";
"videolibrary.uploads.noSubscription" = "No Subscription";
"videolibrary.uploads.noSubscription.description" = "Uploads will resume when your account has an active subscription.";
"videolibrary.uploads.noInternet" = "No Internet Connection";
"videolibrary.uploads.noInternet.description" = "Uploads will resume when your device has a stable network connection.";
"videolibrary.uploads.emptystate.title" = "No Uploads in Progress";
"videolibrary.uploads.emptystate.description" = "You can monitor the status of your next video upload on this screen.";
"videolibrary.uploads.tryagain" = "Try Again";
"videolibrary.uploads.cancelall" = "Cancel All";
"videolibrary.uploads.resume" = "Resume";
"videolibrary.uploads.pause" = "Pause";
"videolibrary.uploads.cancel.title" = "Cancel Upload" ;
"videolibrary.uploads.cancel.description" = "Your upload queue will be deleted. This action cannot be undone." ;
"videolibrary.uploads.cancel.notnow" = "Not Now" ;
"videolibrary.uploads.cancel.yes" = "Yes, Cancel";
"videolibrary.uploads.status.finishing" = "Finishing Upload..." ;
"videolibrary.uploads.status.queued" = "Queued" ;
"videolibrary.uploads.status.start" = "Starting Upload...";
"videolibrary.uploads.status.uploading" = "Uploading... %@%%";
"videolibrary.uploads.status.uploaded" = "Uploaded";
"videolibrary.form.links" = "Links";
"videolibrary.links.add.title" = "Add Links";
"videolibrary.links.add.new" = "New Link";
"videolibrary.links.add.edit" = "Edit Link";
"videolibrary.links.emptystate.description" = "These links will appear in the details tab of this video in every player that you add it to.";
"videolibrary.links.emptystate.title" = "No Links Yet";
"videolibrary.links.noconnection.title" = "Unable to Load Links";
"videolibrary.addlinks.button.text" = "Add Links";
"videolibrary.addlinks.count_singular" = "1 Link";
"videolibrary.addlinks.count_plural %lld" = "%lld Links";
"videolibrary.addlinks.url.error" = "Please enter a valid URL";
"videolibrary.addtags.button.text" = "Add Tags";
"videolibrary.addtags.count_singular" = "1 Tag";
"videolibrary.addtags.count_plural %lld" = "%lld Tags";
"videolibrary.uploads.addlinks.status" = "Create Weblinks";
"videolibrary.uploads.captions.status" = "Create Captions";
"videolibrary.uploads.deletelink" = "Delete Link";
"videolibrary.uploads.addlinks.detail" = "These links will appear in the details tab of this video in every player that you add it to.";
"videolibrary.uploads.addlinks.purchase" = "Hide link until video or subscription is purchased if a gated content pass is applied.";
"videolibrary.uploads.addlinks.more %@ %lld" = "%@ and %lld more";
"videolibrary.form.nolinks" = "No links yet";
"videolibrary.seemore" = "See More";
"videolibrary.seeless" = "See Less";
"studio.output.status.title" = "Destinations";
"studio.output.status.action.end" = "End Stream";
"studio.output.destination.title" = "Your Destinations";
"studio.output.destination.vc" = "Video Catalog";
"studio.output.destination.addnew" = "Add Destination";
"studio.output.destination.next" = "Next";
"studio.output.destination.max" = "You've reached the external destination limit for your plan.";
"studio.output.destination.video.catalog" = "Video Catalog";
"studio.output.destination.others" = "others";
"studio.output.destination.footer.connectedas %@" = "Connected as %@.";
"studio.output.destination.footer.signout" = "Disconnect";
"studio.output.destination.footer.alert.title %@" = "Sign Out from %@";
"studio.output.destination.footer.alert.message" = "Any existing destination will be removed. Are you sure?";
"studio.output.destination.footer.alert.progress" = "Signing Out...";
"studio.output.destination.footer.alert.success" = "Sign Out Successful";
"studio.output.destination.footer.alert.error" = "Unable to Sign Out";
"studio.output.destination.footer.alert.error.info" = "Please check your connection and try again.";
"studio.output.destination.footer.tooplip" = "External destinations cannot be changed or edited once your livestream is scheduled.";
"studio.output.event.title" = "Livestream Details";
"studio.output.event.scheduled %@" = "Livestream is scheduled for **%@**.";
"studio.output.event.golive" = "Go Live Now";
"studio.output.event.schedule.start" = "Starts";
"studio.output.event.schedule" = "Schedule for Later";
"studio.output.event.title.placeholder" = "Give your livestream a name";
"studio.output.event.description.placeholder" = "Tell your audience about your livestream";
"studio.output.event.settings" = "Livestream Settings";
"studio.output.event.settings.quality" = "Quality";
"studio.output.event.settings.quality.tooltip" = "For the best streaming experience, we recommend using 720p resolution or lower.";
"studio.output.event.savetovl" = "Save to Video Library";
"studio.output.event.create.go.live.now.button" = "Create Livestream";
"studio.output.event.create.scheduled.button" = "Schedule Livestream";
"studio.output.event.scheduled.cover.image" = "Thumbnail";
"studio.output.event.scheduled.cover.image.message" = "Recommended size is\n1920 x 1080 pixels.";
"studio.output.event.scheduled.upload.library" = "Camera Roll";
"studio.output.event.switcher.website" = "switcherstudio.com";
"studio.output.event.create.selector.creation.title" = "Create New Livestream";
"studio.output.event.create.selector.creation.description" = "Choose destinations and set up new stream.";
"studio.output.event.create.selector.existing.title" = "Use Existing Scheduled Post";
"studio.output.event.create.selector.existing.description" = "Import post details from Facebook or Youtube.";
"studio.output.event.create.selector.error.all" = "Something went wrong when checking existing scheduled posts.";
"studio.output.event.create.selector.error.partial" = "Something went wrong when checking existing scheduled posts for Facebook/Youtube.";
"studio.output.event.existing.title" = "Scheduled Posts";
"studio.output.event.existing.description" = "Choose the existing scheduled post created on Facebook or Youtube and create an new livestream you can use in Switcher.";
"event.creation.stream.status.delete.error.title" = "Something Went Wrong";
"event.creation.stream.status.delete.error.message" = "We were unable to end your stream properly. Please try again.";
"studio.output.stream.status.error.title" = "Unable to load destinations";
"studio.output.stream.status.error.description" = "Your internet connection appears to be offline. Check your network settings and try again.";
"studio.output.stream.status.active" = "Active";
"studio.output.stream.status.ready" = "Ready";
"studio.output.stream.status.ended" = "Ended";
"studio.output.stream.status.ending" = "Ending Stream...";
"studio.output.stream.status.connecting" = "Connecting...";
"studio.output.stream.status.button.end" = "End Stream";
"studio.output.stream.status.end.confirmation.title" = "End stream now?";
"studio.output.stream.status.end.confirmation.message %@" = "You’re about to end stream to %@. This cannot be undone.";
"studio.output.stream.status.end.confirmation.confirm" = "End Stream";
"studio.output.event.stream.directly" = "Stream directly to your website and landing page. To manage your Video Catalog, visit your account dashboard at **switcherstudio.com**.";
"studio.output.event.turn.on.catalog" = "Turn this on to stream directly to your website and landing page. To manage your Video Catalog, visit your account dashboard at **switcherstudio.com**.";
"destination.stream.to.label" = "Stream to";
"destination.facebook" = "Facebook";
"destination.facebook.header" = "Your Facebook account must be at least 60 days old and your page must have at least 100 followers to go live.";
"destination.facebook.timeline" = "Timeline";
"destination.facebook.timeline.my" = "My Timeline";
"destination.facebook.timeline.private" = "Private Test Stream";
"destination.facebook.pages" = "Pages";
"destination.facebook.page" = "Page";
"destination.facebook.content.tags" = "Content Tags";
"destination.facebook.tag" = "Tag";
"destination.facebook.tags" = "Tags";
"destination.facebook.add.tags" = "Add Tags";
"destination.facebook.search.tags" = "Search Tags";
"destination.facebook.no.tags.selected" = "No Tags Selected";
"destination.facebook.tags.no.results" = "No Results";
"destination.facebook.add.pages" = "Add Pages";
"destination.facebook.pages.no.results" = "No Pages";
"destination.facebook.pages.error" = "Unable to Load Pages";
"destination.facebook.no.pages" = "You don’t have any pages you can crosspost with.";
"destination.facebook.pages.offline.error" = "Your internet connection appears to be offline. Check your network settings and try again.";
"destination.facebook.tags.no.results.second.header" = "Try searching again using a different spelling or keyword";
"destination.facebook.tags.title" = "Content Tags";
"destination.facebook.crossposting" = "Crossposting";
"facebook.tags.error.title" = "Something Went Wrong";
"facebook.tags.error.subtitle" = "There was an error loading content tags. Please try again.";
"destination.facebook.event.everyone.title" = "Everyone";
"destination.facebook.event.everyone.description" = "Anyone on or off Facebook.";
"destination.facebook.event.friends.title" = "Friends";
"destination.facebook.event.friends.description" = "Your friends on Facebook.";
"destination.facebook.event.onlyMe.title" = "Only Me";
"destination.facebook.event.onlyMe.description" = "Private test stream.";
"destination.facebook.event.configuration.title %@" = "Facebook - %@";
"destination.facebook.timeline.event.configuration.title" = "Timeline Settings";
"destination.facebook.event.commentModeration.title" = "COMMENT MODERATION";
"destination.facebook.event.default.title" = "Default";
"destination.facebook.event.default.description" = "All viewers can participate in chat.";
"destination.facebook.event.follower.title" = "Follower";
"destination.facebook.event.follower.description" = "Only your followers will be able to leave comment.";
"destination.facebook.event.slow.title" = "Slow";
"destination.facebook.event.slow.description" = "Commenters will only be able to comment every 10 seconds.";
"destination.facebook.event.discussion.title" = "Discussion";
"destination.facebook.event.discussion.description" = "Only comments over 100 characters will be shown.";
"destination.facebook.event.restricted.title" = "Restricted";
"destination.facebook.event.restricted.description" = "Commenters must have accounts that are at least 2 weeks old.";
"destination.already.added" = "Already Added";
"destination.load.error" = "Unable to load your destinations";
"destination.settings.title" = "Destinations Settings";
"destination.catalog.description %@" = "Stream directly to your website and landing page. To manage your Video Catalog, visit your account dashboard at %@";
"destination.facebook.page.event.configuration.title" = "Page Settings";
"destination.youtube" = "YouTube";
"destination.youtube.header" = "Make sure your channel satisfies the **requirements to go live** on YouTube.";
"destination.youtube.channels.label" = "Channels";
"destination.youtube.forkids.title" = "Made for Kids";
"destination.youtube.forkids.description" = "Made for Kids option indicates that this stream is intended for children. This will restrict some features.";
"destination.youtube.embedded.title" = "Allow Embedding";
"destination.youtube.embedded.description" = "Youtube requires minimum 1,000 channel subscribers and 4,000 watch hours, or user verification (Valid ID or Video Verification) to use Embedded Live Feature.";
"destination.youtube.event.title" = "VISIBILITY";
"destination.youtube.event.public.title" = "Public";
"destination.youtube.event.public.description" = "Anyone can search for and view.";
"destination.youtube.event.private.title" = "Private";
"destination.youtube.event.private.description" = "Only people you choose can view.";
"destination.youtube.event.unlisted.title" = "Unlisted";
"destination.youtube.event.unlisted.description" = "Anyone with the link can view.";
"destination.youtube.event.configuration.title %@" = "YouTube - %@";
"destination.youtube.event.configuration.title" = "Channel Settings";
"destination.twitch" = "Twitch";
"destination.twitch.event.configuration.title" = "Account Settings";
"destination.twitch.add.category" = "Add Category";
"destination.twitch.event.category.title" = "Category";
"destination.twitch.event.category.search.title" = "Search categories";
"destination.twitch.event.category.search.empty.title" = "Search Categories";
"destination.twitch.event.category.search.empty.description" = "Twitch uses categories to classify the live streams and group them up with similar content.";
"destination.twitch.event.ingestServer.title" = "Ingest Server";
"destination.twitch.event.ingestServer.placeholder" = "Select Ingest Server";
"desination.twitch.event.ingestServer.description" = "Visit %@ to get information about ingest servers and to find out which ones are recommended for you.";
"destination.twitch.event.ingestServer.error.title" = "Unable to Load Ingest Servers";
"destination.twitch.event.ingestServer.error.text" = "Your Internet connection appears to be offline. Check your connection and try again.";
"destination.twitch.event.categories.error.title" = "Unable to Load Categories";
"destination.twitch.event.categories.error.text" = "Your Internet connection appears to be offline. Check your connection and try again.";
"destination.catalog.event.shopify" = "LIVE SHOPPING";
"destination.catalog.event.shopify.enable" = "Enable Live Shopping";
"destination.catalog.event.no.shopify" = "You need to connect your Shopify account with Cartr before you can sell live.";
"destination.catalog.event.enable.countdown" = "Show countdown over thumbnail";
"destination.catalog.event.one.time.pass" = "Add One-Time Pass";
"destination.catalog.event.one.time.pass.explain" = "Require purchase to view video.";
"destination.catalog.event.one.time.pass.price" = "Price";
"destination.catalog.one.time.pass.price.placeholder" = "2.00";
"destination.catalog.one.time.pass.price.prefix" = "US$";
"destination.catalog.one.time.pass.price.error" = "Amount must be at least $2.00";
"destination.catalog.one.time.pass.rental.error" = "Value must be greater than 0";
"destination.catalog.one.time.pass.name.placeholder" = "Give your pass a name";
"destination.catalog.event.one.time.pass.none" = "None";
"destination.catalog.event.no.one.time.passes" = "No One-Time passes found.";
"one.time.passes.error.title" = "Unable to Load Passes";
"one.time.passes.error.subtitle" = "Your internet connection appears to be offline. Check your network settings and try again.";
"one.time.passes.alert.title" = "Unable to save changes";
"one.time.passes.alert.message" = "Your internet connection appears to be offline. Check your network seetings and try again.";
"destination.catalog.event.one.time.pass.off" = "To start selling one-time access to your livestreams, visit your account dashboard at **switcherstudio.com**.";
"destination.catalog.event.one.time.pass.no.collection.added" = "Gating options are only possible when a collection has been selected as destination.";
"destination.catalog.one.time.pass.description.placeholder" = "Explain to your viewers what they will be able to watch by purchasing this pass.";
"destination.catalog.event.one.time.pass.creation" = "New Pass";
"one.time.passes.timed.access" = "Timed Access Period";
"one.time.passes.duration.hours" = "Hours";
"one.time.passes.duration.days" = "Days";
"one.time.passes.duration.weeks" = "Weeks";
"Livestream.delete.title.sing" = "Delete Livestream?";
"Livestream.delete.body %@" = "Are you sure you want to delete %@?";
"Livestream.delete.success" = "Livestream deleted successfully.";
"Livestream.delete.failed" = "Unable to Delete Livestream";
"Livestream.delete.progress" = "Deleting...";
"Livestream.go.to" = "Go to Livestream Details";
"livestream.how.to" = "How to Run a Test Livestream...";
"livestream.run.test.stream" = "Run a Test Livestream";
"event.creation.success.title" = "Your Livestream is Ready";
"event.creation.success.schedule.title" = "Your Livestream is Scheduled";
"event.creation.success.description.sing" = "Your destination is now configured and you're ready to go live.";
"event.creation.success.schedule.description.sing" = "Your destination is now configured.";
"event.creation.success.description.plur" = "All destinations have been successfully configured and you're ready to go live.";
"event.creation.success.schedule.description.plur" = "All destinations have been successfully configured.";
"event.creation.success.catalog.on.promotion.description" = "You can organize livestreams and videos from your library into collections. Visit your account at **switcherstudio.com** to learn more about it.";
"event.creation.success.catalog.off.promotion.description" = "You can go live directly to your website by using the Video Catalog. Give it a try when scheduling your next livestream or visit your account at **switcherstudio.com** to learn more about it.";
"event.creation.error.generic" = "There was an error with your %@ connection.";
"event.creation.warning.title" = "Please Check Details Below";
"event.creation.warning.description" = "Your livestream is ready but some of your destinations failed. Live streaming is possible on the configured destinations.";
"event.creation.error.youtube.embed.restriction" = "Youtube requires minimum 1,000 channel subscribers and 4,000 watch hours, or user verification (Valid ID or Video Verification) to use Embedded Live Feature";
"event.creation.error.facebook" = "Please reconnect your Facebook account and allow Switcher to make posts on your behalf.";
"event.creation.preparing" = "Preparing Your Livestream...";
"event.creation.error.title" = "Something Went Wrong";
"event.creation.error.subtitle" = "An error occurred when configuring your livestream.";
"event.creation.goback" = "Go Back";
"event.editing.success.title" = "Livestream Updated";
"event.editing.success.schedule.description" = "Your livestream details have been successfully updated.";
"event.editing.preparing" = "Updating Your Livestream...";
"event.editing.error.subtitle" = "An error occurred when updating your livestream.";
"event.editing.warning.title" = "Attention";
"event.editing.warning.description" = "Some destinations could not be properly updated. See details below.";
"event.editing.error.title" = "Something Went Wrong";
"event.editing.error.subtitle" = "An error occurred when updating your livestream.";
"event.upcoming.title" = "Scheduled Livestreams";
"event.upcoming.count.plur %lld" = "%lld livestreams";
"event.upcoming.count.sing" = "1 livestream";
"event.details.share.link" = "Share Livestream Link";
"event.details.scheduled.for.label %@" = "Scheduled for %@";
"event.details.video.player.details.error" = "Failed to load destination, please refresh.";
"permissions.title" = "Before you start";
"permissions.subtitle" = "Switcher needs access to a few permissions in order to work properly.";
"permissions.tooltip" = "You’re always in control. You can change this anytime in your device settings.";
"permissions.camera.title" = "Camera";
"permissions.camera.subtitle" = "Use your camera to record videos.";
"permissions.mic.title" = "Microphone";
"permissions.mic.subtitle" = "Use your microphone to record sound.";
"permissions.network.title" = "Local Network";
"permissions.network.subtitle" = "Use iPhones and iPads as additional cameras.";
"permissions.speech.title" = "Speech Recognition";
"permissions.speech.subtitle" = "Use speech recognition to generate transcript.";
"permissions.button.set" = "Next";
"transcription.copied.to.clipboard" = "Transcript copied to clipboard.";
"transcription.generation.title" = "Generating transcript...";
"transcription.generation.subtitle %lld" = "%lld caption(s) generated.\n\nPlease don't close the app or put it in the background.";
"transcription.generation.error" = "An error occurred when generating the transcript.";
"transcription.generation.error.noaudio" = "No audio was detected in your video, so we couldn't create a transcript.";
"transcription.start.title" = "Generate transcript";
"transcription.start.description" = "Automatically create transcript and captions for your video using speech recognition.";
"transcription.start.button" = "Start Recognition";
"transcription.access.title" = "Switcher Does Not Have Access to Speech Recognition";
"transcription.access.message" = "Speech recognition is used to generate transcripts of your video.";
"transcription.cloud.error" = "Unable to load transcript";
"transcription.cloud.processing" = "Transcript is processing...";
"productiongroups.empty.title" = "No groups";
"productiongroups.empty.subtitle" = "Streamline your productions by organizing your assets into production groups.";
"productiongroups.new" = "New Group";
"productiongroups.asset.sing" = "%d asset";
"productiongroups.asset.plur" = "%d assets";
"productiongroups.alert.title" = "Delete Group?";
"productiongroups.menu.hide" = "Hide Group";
"productiongroups.menu.unhide" = "Unhide Group";
"productiongroups.rename" = "Rename";
"productiongroups.rename.subtitle"= "Enter new group name";
"productiongroups.menu.rename.title" = "Rename Group";
"productiongroups.menu.delete" = "Delete Group";
"productiongroups.hidden.toast" = "Group hidden from Production Panel.";
"productiongroups.deleted.toast" = "Group deleted from Production Panel.";
"productiongroups.delete.alert.error.title" = "Error";
"productiongroups.delete.alert.error.message" = "Unexpected error occurred";
"productiongroups.delete" = "Delete";
"productiongroups.alert.assets.message %@" = "This group contains assets. These assets will be deleted if you continue.\nAre you sure you want to delete %@?";
"productiongroups.alert.noassets.message %@" = "Are you sure you want to delete %@?";
"productiongroups.add.error.duplicate.title" = "Name Already Taken";
"productiongroups.add.error.duplicate.description" = "Please choose a different name.";
"documentpickerview.filenotfound" = "File not found";
"schedule.live.event" = "Schedule Livestream";
"eventcreation.catalog.password" = "Password";
"eventcreation.catalog.email" = "Email";
"eventcreation.catalog.pass" = "One-Time Pass";
"eventcreation.catalog.collection.sing" = "1 collection";
"eventcreation.catalog.collection.plur" = "%d collections";
"eventcreation.catalog.gated" = "Gated";
"stream.schedule.title" = "Schedule";
"stream.schedule.go.live.now.title" = "Go Live Now";
"stream.schedule.go.live.now.message" = "Create an instant livestream to go live soon. Your audience will not be notified until you begin streaming.";
"stream.schedule.scheduled.for.later.title" = "Schedule for Later";
"stream.schedule.scheduled.for.later.message" = "Create a livestream for a specific date and time to share with your audience.";
"scene.builder" = "Scene Builder";
"scene.builder.fullscreen.title" = "Full-Screen Scene";
"scene.builder.overlay.title" = "Overlay Scene";
"scene.builder.fullscreen.subtitle" = "Combine sources, texts and images.";
"scene.builder.overlay.subtitle" = "Combine texts and images.";
"scene.builder.layer.new.title" = "New Layer";
"scene.builder.layer.text.title" = "Text";
"scene.builder.layer.text.edit.title %@" = "Edit %@";
"scene.builder.layer.image.title" = "Image";
"scene.builder.layer.image.replace" = "Replace Image";
"scene.builder.layer.source.title" = "Source";
"scene.builder.layer.shape.title" = "Shape";
"scene.builder.layer.rectangle.title" = "Rectangle";
"scene.builder.layer.circle.title" = "Circle";
"scene.builder.layer.background.title" = "Background";
"scene.builder.layer.delete.title" = "Delete Layer";
"scene.builder.layer.delete.confirmation.title %@" = "%@ will be deleted. Are you sure?";
"scene.builder.layer.delete.confirmation.confirm.title" = "Delete Layer";
"scene.builder.layer.text.select.font.title" = "Choose Font";
"scene.builder.layer.text.recent.font.title" = "Recents";
"scene.builder.layer.text.line.spacing.title" = "Line Spacing";
"scene.builder.layer.text.align.title" = "Text Alignment";
"scene.builder.layer.content.align.left.title" = "Left Align";
"scene.builder.layer.content.align.center.title" = "Center";
"scene.builder.layer.content.align.right.title" = "Right Align";
"scene.builder.add.sources.title" = "Number of Sources";
"scene.builder.add.sources.hint" = "A maximum of 9 sources can be added to a scene.";
"scene.builder.add.sources.button" = "Add Sources";
"scene.builder.helper" = "Start with an empty scene on which you can add sources (only for full-screen), images and texts to create custom assets.";
"videocatalog.update.error" = "Unable to update catalog settings";
"recommend.quality.title" = "Attention";
"recommend.quality.description" = "Your current connection is not fast enough to support the stream quality you selected, which could create problems when you go live.";
"storage.warning.description %@" = "Your storage has only %@ GB available and it won't be enough to store an hour of recording. We recommend you clean some space before starting the recording, or disable local recording.";
"storage.warning.continue" = "Continue Anyway";
"storage.warning.disable" = "Disable Local Recording";
"recommend.quality.recommend.keep.current.quality %@" = "Keep Current (%@p)";
"recommend.quality.recommend.use.recommended.quality %@" = "Use Recommended (%@p)";
"recommend.bitrate.title" = "Attention";
"recommend.bitrate.description %@" = "Your current connection is not fast enough to support the RTMP video bitrate settings you selected, which could create problems when you go live.\nWe recommend you reduce your bitrate to a maximum of %@ kbps.";
"recommend.bitrate.recommend.keep.current %@" = "Keep Current (%@ kbps)";
"recommend.bitrate.recommend.update.settings" = "Update RTMP settings";
"event.home.empty.title" = "Schedule Your Next Livestream";
"event.home.empty.subtitle" = "Attract a larger audience and promote your stream ahead of time by scheduling your next livestream.";
"event.home.empty.button" = "Schedule Now";
"event.home.see.all" = "See All Livestreams";
"event.home.error.title" = "Something Went Wrong";
"event.home.error.subtitle" = "Your Internet connection appears to be offline. Check your connection and try again.";
"event.time.away.weeks.plural %lld" = "In %lld Weeks";
"event.time.away.weeks.singular" = "In 1 Week";
"event.time.away.days.plural %lld" = "In %lld Days";
"event.time.away.days.singular" = "In 1 Day";
"event.time.away.hours.plural %lld" = "In %lld Hours";
"event.time.away.hours.singular" = "In 1 Hour";
"event.time.away.minutes.plural %lld" = "In %lld Minutes";
"event.time.away.minutes.singular" = "In 1 Minute";
"event.time.away.now" = "Now";
"mixer.error.reactnative.restart" = "Restart";
"mixer.error.reactnative.ignore" = "Ignore";
"mixer.error.reactnative.title" = "A cloud plug-in has raised an unexpected error";
"mixer.error.reactnative.message" = "This can have an impact on Remote Guests, Live Comments and Scoreboard.";
"auto.reconnect.message" = "Poor connection. Reconnecting...";
"camera.menu.exit" = "Disconnect Camera";
"switcher.api.error.no.internet.connection.title" = "Something Went Wrong";
"switcher.api.error.no.internet.connection.subtitle" = "Your Internet connection appears to be offline. Check your connection and try again.";
"switcher.api.error.no.server.connection.title" = "Something Went Wrong";
"switcher.api.error.no.server.connection.subtitle" = "Your connection to the servers appear to be offline. Check your connection and try again.";
"switcher.api.error.user.invalid.title" = "User is Invalid";
"switcher.api.error.user.invalid.subtitle" = "Please login and logout or check your user's active subscription";
"aspect-ratio-free-form" = "Custom";
"vprop.picker.cut" = "Cut";
"vprop.picker.push" = "Push";
"vprop.picker.cross-dissolve" = "Cross Dissolve";
"slideshow.select.firstimage" = "Select First Image";
