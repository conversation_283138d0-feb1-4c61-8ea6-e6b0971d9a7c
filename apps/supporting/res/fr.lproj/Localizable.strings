"global-action.cancel.title" = "Annuler";
"global-action.skip.title" = "Skip";
"global-action.ok.title" = "OK";
"global-action.dismiss.title" = "Dismiss";
"global-action.done.title" = "OK";
"global-action.continue.title" = "Continuer";
"global-action.close.title" = "Fermer";
"global-action.remove.title" = "Remove";
"global-text.error.title" = "Erreur";
"global-text.warning.title" = "Attention";
"global-text.slash-separator" = " / ";
"global-text.yes" = "Yes";
"global-text.no" = "No";
"global-text.none" = "None";
"global-action.reset.title" = "Reset";
"brand.kit" = "Brand Kit";
"media-lib.text.Journal" = "Journal";
"media-lib.text.Angle" = "Angle";
"media-lib.text.Audio" = "Audio";
"media-lib.text.Part" = "Partie";
"filter-list.title" = "Effets";
"filter-list.transition.title" = "Transitions";
"filter-list.transition.reorder" = "Reorder and Customize";
"filter-list.transition.text" = "Reorder or hide transitions you don't use to personalize your view.";
"filter-list.multi-view.title" = "Compositions";
"filter-list.vfilter-none.title" = "Aucun";
"filter-list.vfilter-cut.title" = "Coupure";
"filter-list.vfilter-crossfade.title" = "Fondu enchaîné";
"filter-list.vfilter-cube.title" = "Cube";
"filter-list.vfilter-twist.title" = "Torsion";
"filter-list.vfilter-wipe.title" = "Balayage";
"filter-list.vfilter-cockpit.title" = "Tableau de bord";
"filter-list.vfilter-flap.title" = "Volet";
"filter-list.vfilter-slot.title" = "Bandes";
"filter-list.vfilter-pinp.title" = "Image dans l'image";
"filter-list.vfilter-split-view.title" = "Écran divisé";
"filter-list.vfilter-grid.title" = "Grille";
"filter-list.vfilter-gen.title" = "Format d'image";
"filter-list.vfilter-input.title" = "Format d'image";
"vfilter-prop.duration.title" = "Durée de transition";
"vfilter-prop.bg_color.title" = "Couleur de fond";
"vfilter-prop.bg_image.title" = "Image de fond";
"vfilter-prop.bg_alpha.title" = "Opacité de l'image de fond";
"vfilter-prop.direction.title" = "Direction";
"vfilter-prop.test_pattern.title" = "Dessin de test";
"vfilter-prop.border_color.title" = "Couleur du bord";
"vfilter-prop.border_thickness.title" = "Épaisseur du bord";
"vfilter-prop.corner_radius.title" = "Rayon d’arrondi";
"vfilter-prop.pos_x.title" = "Position X";
"vfilter-prop.pos_y.title" = "Position Y";
"vfilter-prop.pos_z.title" = "Position Z";
"vfilter-prop.stopwatch.title" = "Stopwatch";
"vfilter-prop.timer.title" = "Timer";
"vfilter-prop.yaw.title" = "Lacet";
"vfilter-prop.pitch.title" = "Tangage";
"vfilter-prop.roll.title" = "Roulis";
"vfilter-prop.center.title" = "Centrer";
"vfilter-prop.frame_crop_h.title" = "Rognage horizontal";
"vfilter-prop.frame_crop_v.title" = "Rognage vertical";
"vfilter-prop.gap_h.title" = "Écartement horizontal";
"vfilter-prop.gap_v.title" = "Écartement vertical";
"vfilter-prop.reflection.title" = "Reflet";
"vfilter-prop.reflection_gap.title" = "Écartement du reflet";
"vfilter-prop.reframing_mode.title" = "Cadrage";
"vfilter-prop.bg_mode.title" = "Calque";
"vfilter-prop.bg_mode.enum.source.title" = "Plein écran";
"vfilter-prop.bg_mode.enum.overlay.title" = "Surimpression";
"vfilter-prop.angle.title" = "Angle";
"vfilter-prop.angle.flap.title" = "Angle du volet";
"vfilter-prop.frame_format.title" = "Format";
"vfilter-prop.frame_format.flap.title" = "Format du volet";
"vfilter-prop.separator_direction.title" = "Direction de la séparation";
"vfilter-prop.separator_position.title" = "Position de la séparation";
"vfilter-prop.separator_thickness.title" = "Épaisseur de la séparation";
"vfilter-prop.separator_color.title" = "Couleur de la séparation";
"vfilter-prop.spacing.title" = "Espacement";
"vfilter-prop.uniform_gap.title" = "Écart uniforme";
"vfilter-prop.bottom_margin.title" = "Marge du bas";
"vfilter-prop.view_angle.cockpit.title" = "Perspective";
"vfilter-prop.dark_color_transp.title" = "Noir vers transparent";
"vfilter-prop.chroma_key.header" = "Chroma Key";
"vfilter-prop.chroma_key.title" = "Enable Chroma Key";
"vfilter-prop.chroma_key_color.title" = "Key Color";
"vfilter-prop.chroma_key_range.title" = "Range";
"vfilter-prop.chroma_key_softness.title" = "Softness";
"vfilter-prop.chroma_key_edge_desat.title" = "Edge Desaturation";
"vfilter-prop.chroma_key_alpha_crop.title" = "Alpha Crop";
"vfilter-prop.chroma_key_bg_image.title" = "Background Image";
"UIFrameFormatPropViewController.title" = "Format";
"PropBundleEditor.disabled-because-no-reframing.msg" = "Ces propriétés n'ont pas d'effet car le contenu rempli parfaitement l'image de sortie.";
"PropBundleEditorViewController.restore-defaults-button.title" = "Rétablir les paramètres par défaut";
"source-list.title" = "Caméras";
"source-list.inputs.title" = "Entrées";
"source-list.angle.title" = "Angle";
"source-list.audio.title" = "Audio";
"source-list.other-item.title" = "Ajouter une source";
"source-list.built-in-source.title" = "Sources intégrées";
"source-list.network-source.title" = "Sources sur le réseau Wi-Fi";
"source-list.alert.busy.msg.text" = "%@ est occupé";
"source-list.alert.bad-version.msg.text" = "%@: version incompatible";
"source-list.alert.bad-format.msg.text" = "%@: format non supporté";
"source-list.alert.bad-prod.msg.text" = "%@ n'est pas pris en charge";
"source-list.alert.ok-button.text" = "OK";
"source-list.multi-src-name.fmt" = "%@ sur %@";
"source-list.not-available-item-local.title" = "Non disponible";
"source-list.not-available-item-wifi.title" = "Aucune";
"SourceListIcons.menu.title" = "Sources";
"SourceListIcons.source.camera" = "Caméra";
"SourceListIcons.source.mobile-screen" = "Écran de téléphone";
"SourceListIcons.source.desktop-screen" = "Écran d'ordinateur";
"SourceListIcons.source.connect-url" = "Connecter par url";
"SourceListIcons.source.videochat" = "Videochat";
"SourceInformationsTableViewController.instructions.header" = "Instructions";
"SourceInformationsTableViewController.title.camera" = "Caméra";
"SourceInformationsTableViewController.instructions.camera-1" = "Ouvrir l'application Switcher Studio sur le dispositif iOS additionnel.";
"SourceInformationsTableViewController.instructions.camera-2" = "Appuyer sur \"Partager ce dispositif\".";
"SourceInformationsTableViewController.instructions.camera-3" = "Appuyer sur \"Partager cette caméra\".";
"SourceInformationsTableViewController.instructions.camera-4" = "Sur votre dispositif principal, sélectionner le nom du dispositif additionnel dans la liste des Entrées.";
"SourceInformationsTableViewController.camera-extra" = "If you're connected on your additional iOS device, the \"Share Camera or Screen\" option is available in the Menu page";
"SourceInformationsTableViewController.title.mobile-screen" = "Écran de téléphone";
"SourceInformationsTableViewController.instructions.mobile-screen-1" = "Ouvrir l'application Switcher Studio sur le dispositif iOS additionnel.";
"SourceInformationsTableViewController.instructions.mobile-screen-2" = "Appuyer sur \"Partager ce dispositif\".";
"SourceInformationsTableViewController.instructions.mobile-screen-3" = "Appuyer sur \"Partager cet écran\".";
"SourceInformationsTableViewController.instructions.mobile-screen-4" = "Sur votre dispositif principal, sélectionner le nom du dispositif additionnel dans la liste des Entrées.";
"SourceInformationsTableViewController.title.desktop-screen" = "Écran d'ordinateur";
"SourceInformationsTableViewController.instructions.desktop-screen-1" = "Installer le logiciel Switcher Cast sur votre ordinateur.";
"SourceInformationsTableViewController.instructions.desktop-screen-2" = "Connecter cet ordinateur au même réseau Wi-Fi que les autres dispositifs iOS.";
"SourceInformationsTableViewController.instructions.desktop-screen-3" = "Démarrer Switcher Cast pour permettre le partage d'écran de votre ordinateur.";
"SourceInformationsTableViewController.instructions.desktop-screen-4" = "Sur votre dispositif principal, sélectionner l'ordinateur dans la liste des Entrées.";
"SourceInformationsTableViewController.connect-url.header" = "Votre dispositif n'apparaît pas dans la liste ?";
"SourceInformationsTableViewController.connect-url.title" = "Connecter par URL";
"SourceInformationsTableViewController.download-link.title" = "Partager le lien pour télécharger Switcher Cast";
"SourceInformationsTableViewController.download-link.desktop-win" = "Switcher Cast pour Windows";
"SourceInformationsTableViewController.download-link.desktop-mac" = "Switcher Cast pour MacOS";
"CameraUrlInformationsTableViewController.instructions.header" = "Instructions";
"CameraUrlInformationsTableViewController.instructions.camera-1" = "Ouvrir l'application Switcher Studio sur le dispositif principal.";
"CameraUrlInformationsTableViewController.instructions.camera-2" = "S'identifier et appuyer sur \"Mode régie\".";
"CameraUrlInformationsTableViewController.instructions.camera-3" = "Appuyer sur l'onglet \"Entrées\" (icône caméra vidéo).";
"CameraUrlInformationsTableViewController.instructions.camera-4" = "Si ce dispositif n'est pas listé, appuyer sur \"Ajouter une source\".";
"CameraUrlInformationsTableViewController.instructions.camera-5" = "Appuyer sur \"Connecter par URL\".";
"CameraUrlInformationsTableViewController.instructions.camera-6" = "Entrer l'URL affichée sur le dispositif iOS additionnel.";
"RecordingInterruptedAlertController.msg" = "Déconnecter une caméra va interrompre son enregistrement.";
"RecordingInterruptedAlertController.ok" = "OK";
"RecordingInterruptedAlertController.dont-show-again" = "Ne plus afficher ce message";
"URLConnectionController.title.text" = "Connexion par URL";
"URLConnectionController.url.title.text" = "Entrer l'URL de la caméra:";
"URLConnectionController.history.title.text" = "Utilisés récemment:";
"URLConnectionController.history.empty.text" = "Aucun";
"URLConnectionController.action-connect.text" = "Connecter";
"URLConnectionController.alert.no-wifi.text" = "Pas de réseau Wi-Fi";
"URLConnectionController.alert.bad-url.text" = "Mauvais URL";
"URLConnectionController.alert.itself.text" = "Vous ne pouvez pas vous connecter à vous-même";
"IOSAVProvider.camera.title" = "Caméra intégrée";
"IOSAVProvider.back-camera.title" = "Caméra arrière";
"IOSAVProvider.front-camera.title" = "Caméra avant";
"LocalAudio.source.title" = "Micro intégré / Entrée audio";
"MixerViewController.program.text" = "Live";
"MixerViewController.preview.text" = "Aperçu";
"MixerViewController.sources.text" = "Sources";
"MixerViewController.prev-to-prog-button.left.text" = "Aperçu";
"MixerViewController.prev-to-prog-button.right.text" = "Live";
"MixerViewController.stream-validation.msg" = "Flux en cours de validation...";
"CheckDevice.generic-alert.msg" = "%@ permet de filmer sous plusieurs angles, à partir de multiples périphériques connectés à votre réseau local en Wi-Fi. Pour cela, vous devez autoriser l’accès au microphone, à la caméra et au réseau local. Vous pouvez autoriser ces accès depuis le menu Réglages > Switcher.";
"CheckDevice.settings.title" = "Réglages";
"CheckDevice.no-local-network.title" = "Accès au réseau local refusé";
"CheckDevice.no-microphone.title" = "Accès au microphone refusé";
"CheckDevice.no-camera.title" = "Accès à la caméra refusé";
"MediaViewController.byte-unit" = "o";
"MediaViewController.media-state.searching.title" = "Recherche en cours...";
"MediaViewController.media-state.missing.title" = "Manquant";
"MediaViewController.media-state.missing-hostname.title" = "Manquant (enregistré sur %@)";
"MediaViewController.media-state.missing-connection.title" = "Manquant (reconnectez %@)";
"MediaViewController.media-state.cancelled.title" = "Annulé";
"MediaViewController.media-state.cancelling.title" = "Annulation en cours...";
"MediaViewController.media-state.error.title" = "Erreur";
"MediaViewController.media-state.out-of-mem.title" = "Mémoire vive insuffisante";
"MediaViewController.media-state.out-of-storage.title" = "Espace de stockage insuffisant sur votre appareil";
"MemoryViewController.title" = "Mémoire";
"MemoryViewController.available-label.text" = "Mémoire disponible";
"MemoryViewController.enlarge-button.text" = "Dilater";
"MemoryViewController.build-button.text" = "Créer la composition";
"MemoryViewController.ignore-switch.text" = "Ignorer les limites de mémoire";
"MemoryViewController.help" = "Si la mémoire vive est insuffisante pour créer la composition finale, veuillez svp fermer toutes les applications, éteindre votre appareil et le rallumer. Pour plus d’informations, visitez svp notre site web.";
"MemoryViewController.byte-unit" = "o";
"CameraControl.zoom" = "Zoom";
"CameraControl.focus" = "Focus";
"CameraControl.expo" = "Exposition";
"CameraControl.wbal" = "Balance des blancs";
"CameraControl.light" = "Éclairage LED";
"CameraControl.stab" = "Stabilisation";
"CameraControl.noFocusWhenLive" = "Pas d'autofocus en live";
"CameraControl.resetToDefaults" = "Rétablir les réglages par défaut";
"CameraControl.saveSettings" = "Sauver les réglages";
"CameraControl.vptz.restore-default-presets" = "Restore Default Presets";
"CameraControl.vptz.enable" = "Enable ePTZ";
"CameraControl.vptz.unsupported-iphone-camera-tip" = "This feature is not compatible with iPhone's build-in camera. Please select a different camera.";
"CameraControl.tab.vptz" = "ePTZ";
"CameraControl.tab.controls" = "Controls";
"AudioViewController.title" = "Audio";
"AudioViewController.main-grp.title" = "Canal principal";
"AudioViewController.main-grp.switcher.title" = "Audio pour enregistrement et diffusion";
"AudioViewController.aux-grp.title" = "Canaux auxiliaires";
"AudioViewController.aux-grp.footer" = "Les canaux auxiliaires sont enregistrés mais ne sont pas utilisés dans la composition finale.";
"AudioViewController.global-settings-grp.title" = "Options";
"AudioViewController.global-settings-grp.footer" = "Le retour audio vous permet d'écouter l'audio de l'enregistrement ou de la diffusion grace à votre casque ou autre sortie active. Avec certaines interfaces audio, cela peut provoquer de l'echo.\nLe mode voix ajoute des traitements du son tels que l’annulation d'écho. Il est nécessaire pour les AirPops et autres casques Bluetooth avec micro. Désactivez le mode voix lorsque vous utilisez des micros filaires, des interfaces audio externes ou si vous enregistrez de la musique.";
"AudioViewController.monitoring.title" = "Retour audio";
"AudioViewController.voice-mode.title" = "Mode voix";
"CameraControlTableViewController.title" = "Contrôle caméra";
"ResyncDelayLevel.0.long" = "Mode faible latence";
"ResyncDelayLevel.1.long" = "Mode Wi-Fi standard";
"ResyncDelayLevel.2.long" = "Mode Wi-Fi renforcé";
"ResyncDelayLevel.0.short" = "Faible latence";
"ResyncDelayLevel.1.short" = "Mode Wi-Fi standard";
"ResyncDelayLevel.2.short" = "Wi-Fi renforcé";
"ResyncDelayLevel.0.description" = "En mode faible latence, la vidéo est affichée peu de temps après avoir été capturée, ce qui ne permet pas toujours d’atteindre une qualité vidéo optimale. Dans ce mode, la stabilisation et l'enregistrement 4K ne sont pas possibles.";
"ResyncDelayLevel.1.description" = "Le mode standard est le meilleur compromis entre latence et qualité vidéo. Cela requiert un réseau Wi-Fi robuste.";
"ResyncDelayLevel.2.description" = "Ce mode n'affecte pas la qualité de la vidéo mais introduit une seconde de délai sur l'affichage de la vidéo afin de contourner les limitations des réseaux Wi-Fi faibles, surchargés ou sujets à des interférences.";
"ResyncDelayLevel.2.description-temporary-addon" = "(temporaire)";
"OutputViewController.title" = "Sorties";
"OutputViewController.outputs.title" = "Sorties";
"OutputViewController.outputs.camo.title" = "Vers Zoom, Meet, Teams, Skype, ...";
"OutputViewController.external-display.source.title" = "Vers HDMI / AirPlay";
"OutputViewController.external-display.not-allowed.title" = "HDMI / AirPlay";
"OutputViewController.external-display.not-allowed.msg" = "Pour utiliser HDMI ou AirPlay, veuillez svp utiliser Switcher Studio";
"OutputViewController.external-display.not-allowed.more-info" = "Plus d'infos";
"OutputViewController.streaming.title" = "Diffusion";
"OutputViewController.streamingOptions.title" = "Options de diffusion";
"OutputViewController.streaming.recordonly.title" = "Enregistrement Uniquement";
"OutputViewController.streaming.recordonly-autosave.title" = "Recording";
"OutputViewController.streaming.simulcast.title" = "Livestreaming";
"OutputViewController.practice.mode.title" = "Practice";
"OutputViewController.streaming.selectedLivestream" = "Active Livestream";
"OutputViewController.streaming.selectAnotherLivestream" = "Select Another Livestream";
"OutputViewController.event.noevents.header" = "No Livestreams";
"OutputViewController.event.noevents.body" = "Create your livestreams in advance and they will appear here.";
"OutputViewController.streaming.generic.title" = "RTMP";
"OutputViewController.event.noevent" = "No Livestreams";
"OutputViewController.event.noevent.subtitle" = "Create one to get started";
"OutputViewController.event.nochannel" = "No Channel";
"OutputViewController.event.nochannel.subtitle" = "Create new or select existing";
"OutputViewController.event.golive" = "Go Live Now";
"OutputViewController.event.button.create" = "Create";
"OutputViewController.event.button.change" = "Change";
"OutputViewController.event.destinations %d" = "%d destinations";
"OutputViewController.event.destinations.sing" = "1 destination";
"OutputViewController.event.share" = "Share Link";
"OutputViewController.rec.title" = "Enregistrement";
"OutputViewController.rec.director-mode.title" = "Mode “Director”";
"OutputViewController.rec.rtmp.reconnect.title" = "Reconnexion RTMP";
"OutputViewController.rec.options.title" = "Réglages avancés";
"OutputViewController.rec.tip.msg" = "Activez le mode “Director” pour un enregistrement en haute qualité.";
"OutputViewController.isocam-warning.msg" = "Une fois le mode “Director” activé, la caméra intégrée à cet appareil ne sera plus en service. Quand vous démarrez la production, chaque caméra va alors enregistrer sa vidéo de façon indépendante. L’ensemble de ces vidéos permet de générer un rendu Full HD (1080p) ou 4K. Vérifiez d’avoir assez de mémoire sur chaque appareil avant de continuer. Pour plus d'info, consultez notre base de connaissances.";
"OutputViewController.wi-fi.title" = "Communication Wi-Fi avec les caméras";
"OutputViewController.wi-fi.resync-delay.title" = "Optimisation du Wi-Fi";
"OutputViewController.refresh-control.title" = "Synchronisation avec le Dashboard...";
"OutputViewController.display-selection.program.short" = "Live";
"OutputViewController.display-selection.mirroring.short" = "Tout";
"OutputViewController.resolution.recording" = "Video Quality";
"OutputViewController.resolution.streaming" = "Stream Quality";
"OutputSourcesTableViewController.display-selection.mirroring.long" = "Afficher tout l'écran";
"OutputSourcesTableViewController.display-selection.program.long" = "Afficher la sortie Live";
"OutputSourcesTableViewController.airplay-tip.msg" = "Pour activer ou désactiver AirPlay, utilisez svp le bouton AirPlay du Centre de contrôle. La recopie vidéo doit également être enclenchée.";
"OutputSourcesTableViewController.underscan.title" = "Sous-balayage";
"OutputSourcesTableViewController.underscan.auto.title" = "Auto";
"OutputSourcesTableViewController.rotation.title" = "Rotation";
"LimitedAccessOutputViewController.header" = "Compte expiré";
"LimitedAccessOutputViewController.body" = "Votre compte a expiré.\n\<NAME_EMAIL> pour toute question.";
"LimitedAccessOutputViewController.iap.header" = "No subscription";
"LimitedAccessOutputViewController.iap.body" = "You need an active subscription to access Outputs settings.";
"BCProfileListViewController.title" = "Canaux RTMP";
"BCProfileListViewController.new-profile.name" = "Mon canal";
"BCProfileListViewController.new-profile.button" = "New Channel";
"BCProfileListViewController.no-profile.msg" = "Touchez [+] pour ajouter un canal.";
"BCProfileListViewController.new.msg" = "Swipe down to refresh";
"BCProfileListViewController.refresh.button" = "Refresh";
"BCProfileListViewController.delete" = "Delete";
"RTMPDefViewController.title" = "Paramètres RTMP";
"RTMPDefViewController.name.title" = "Nom du canal";
"RTMPDefViewController.name.placeholder" = "Enter channel name";
"RTMPDefViewController.url.title" = "URL du serveur";
"RTMPDefViewController.url.placeholder" = "Enter your stream URL";
"RTMPDefViewController.stream.title" = "Nom du flux (clé ou ID)";
"RTMPDefViewController.stream.placeholder" = "Enter your stream key or ID";
"RTMPDefViewController.options.title" = "Options de diffusion";
"RTMPDefViewController.vformat.title" = "Résolution vidéo";
"RTMPDefViewController.vbitrate.title" = "Débit vidéo";
"RTMPDefViewController.aformat.title" = "Format audio";
"RTMPDefViewController.abitrate.title" = "Débit audio";
"RTMPDefViewController.compat.title" = "Options de compatibilité";
"RTMPDefViewController.compat-fmle.title" = "Émuler Flash Media Live Encoder";
"RTMPDefViewController.speed-test" = "Speed Test";
"RTMPDefViewController.speed-test.title" = "SPEED TEST";
"RTMPDefViewController.speed-test.msg" = "Touchez SPEED TEST pour configurer les OPTIONS DE DIFFUSION d'après la vitesse actuelle de votre connexion Internet.";
"RTMPDefViewController.default.short-form.title" = "Par défaut";
"RTMPDefViewController.default.long-form.title" = "Valeur par défaut";
"RTMPDefViewController.error.title" = "Unable to Save Changes";
"RTMPDefViewController.error.msg" = "Please check your connection and try again.";
"menu.title" = "Profil";
"library.title" = "Vidéothèque";
"home.title" = "Home";
"create.new.title.loggedIn" = "Nouvelle Vidéo";
"menu.knowledge-base.title" = "Centre d'assistance";
"menu.settings.title" = "Ouvrir les Réglages";
"menu.media.title" = "Vidéothèque";
"menu.diag.title" = "Données de diagnostic";
"menu.home.title" = "Retourner à l'écran d'accueil";
"menu.home.confirm.title" = "Êtes-vous sûr de vouloir quitter ?";
"menu.home.confirm.comment" = "Vous allez ainsi déconnecter toutes les caméras et retourner à l'écran d'accueil.";
"menu.home.confirm.yes" = "Déconnecter et quitter";
"menu.home.confirm.no" = "Annuler";
"menu.account.title" = "Compte";
"menu.brand-profile.title" = "Brand Profile";
"menu.iphone.studio-mixer.info.title" = "À propos des dispositifs";
"GalileoConnectionViewController.auto-detect.help.text" = "L'auto-détection vous permet de connecter Galileo dès que l'app est lancée.";
"GalileoConnectionViewController.auto-detect.switch.text" = "Auto-détection";
"GalileoConnectionViewController.status.connected" = "Galileo connecté";
"GalileoConnectionViewController.status.unconnected" = "Faites pivoter Galileo";
"GalileoConnectionViewController.title" = "Motrr Galileo";
"ArtworkProdFlow.error.asset-on-icloud.mgs" = "Ce média n'est pas stocké sur votre appareil.";
"ArtworkProdFlow.error.unsupported-video.mgs" = "Format vidéo non supporté. Veuillez svp convertir ce média en AVC (H.264) ou HEVC (H.265)";
"ArtworkProdFlow.error.noavasset.mgs" = "Le média n'est pas disponible.";
"ArtworkProdFlow.error.notvalid.mgs" = "Le format du média n'est pas valide";
"ArtworkProdFlow.download-switchercloud.msg" = "Téléchargement depuis Switcher Cloud";
"ArtworkProdFlow.download-icloud.msg" = "Téléchargement depuis iCloud";
"ArtworkProdFlow.download-switchercloud.error.msg" = "Erreur de téléchargement depuis Switcher Cloud";
"ArtworkProdFlow.error.no-phasset.mgs" = "Ce média n'est pas valide";
"ElementCollectionViewController.title %@" = "Slot %@ Source";
"ElementCollectionViewController.menu2.edit.title" = "Éditer";
"ElementCollectionViewController.menu2.duplicate.title" = "Dupliquer";
"ElementCollectionViewController.menu2.move.title" = "Déplacer";
"ElementCollectionViewController.menu2.remove.title" = "Supprimer";
"ElementCollectionViewController.menu2.quick-multiview.title" = "Composer";
"ElementCollectionViewController.multiview-menu.alone.title" = "Solo";
"ElementCollectionViewController.plus-button.tip" = "Touchez [+] pour ajouter des caméras, des photos,\ndes vidéos, des titres, des logos et des compositions.";
"TitleListViewController.title" = "Titres";
"TitleListViewController.generic.title" = "Titres génériques";
"TitleListViewController.generic.free-title.title" = "Texte plein écran";
"TitleListViewController.generic.free-label.title" = "Texte en surimplession";
"TitleListViewController.specific-layout.title" = "Cadres";
"TitleListViewController.specific-layout.postcard.title" = "Carte postale";
"TitleListViewController.specific-content.title" = "Modèles";
"LowerThirdListViewController.title" = "Bannières";
"PlatformOverlayListViewController.title" = "Bannières de plateformes";
"PlatformOverlayBannerContent.title" = "Adaptez ces bannières à la plateforme de votre choix";
"BroadcastNotificationListViewController.title" = "Avis de diffusion";
"VMakerEditorViewController.title.blank" = "Blank";
"VMakerEditorViewController.title.live" = "Live";
"VMakerEditorViewController.picker.blank" = "Blank Canvas";
"VMakerEditorViewController.picker.live" = "Live Output";
"VMakerEditorViewController.tips" = "Pour redimensionner l'élément, le pincer et l'agrandir. Pour le repositionner, appuyer dessus et le faire glisser";
"VMakerEditorViewController.apply-on-preview" = "Appliquer à l'aperçu";
"VMakerEditorViewController.stay-on-top" = "Rester au premier plan";
"VMakerEditorViewController.restore-defaults" = "Rétablir les paramètres par défaut";
"VMakerEditorViewController.transition-picker" = "In & Out Transition";
"ElementGroupsViewController.transitions.disabled" = "Disabled";
"VMakerEditorViewController.group-picker" = "Groupe";
"ArtworkEditorViewController.title" = "Propriétés";
"CameraEditorViewController.title" = "Caméra";
"CameraEditorViewController.screen-content.title" = "Écran";
"PlayedVideoEditorViewController.title" = "Propriétés de la vidéo";
"PlayedVideoEditorViewController.loop.title" = "Loop";
"PlayedVideoEditorViewController.loop.description" = "When loop is enabled, the video asset will play continually until removed from screen.";
"PlayedVideoEditorViewController.thumbnail-selector.title" = "Sélection de la vignette";
"PlayedVideoEditorViewController.end-on-last-frame.title" = "S'arrêter sur la dernière image";
"PlayedVideoEditorViewController.audio-enabled.title" = "Activer l'audio";
"PlayedVideoEditorViewController.trimmer.title" = "Trimmer";
"PlayedVideoEditorViewController.trimmer.from" = "De";
"PlayedVideoEditorViewController.trimmer.to" = "à";
"PlayedVideoEditorViewController.trimmer.end" = "la fin";
"PlayedVideoEditorViewController.volume.title" = "Volume";
"PlayedVideoEditorViewController.volume.value-db" = "%@ dB";
"PlayedVideoThumbnailSelectorViewController.title" = "Sélection de la vignette";
"PlayedVideoTrimmerViewController.feature-trimmer-only.title" = "Trimmer";
"PlayedVideoTrimmerViewController.infos.from" = "De :";
"PlayedVideoTrimmerViewController.infos.to" = "À :";
"PlayedVideoTrimmerViewController.infos.duration" = "Durée :";
"MultiviewEditorViewController.title" = "Propriétés de la composition";
"MultiviewInputSubtableController.num-of-inputs.title" = "Nombre de sources";
"UserAccount.subscription-expired.msg" = "Abonnement expiré - compte inactif";
"UserAccount.access-refused.msg" = "Accès refusé";
"AccountViewController.buy.no-product.title" = "Aucun abonnement disponible";
"AccountViewController.buy.success.title" = "Félicitations !";
"AccountViewController.buy.success.message" = "Abonnement réussi";
"AccountViewController.buy.failed.title" = "Achat annulé";
"AccountViewController.restore.no-product.title" = "Terminé";
"AccountViewController.restore.no-product.message" = "Aucun abonnement à restaurer";
"AccountViewController.restore.success.title" = "Félicitations !";
"AccountViewController.restore.success.message" = "La restauration s'est effectuée avec succès";
"AccountViewController.restore.failed.title" = "Problème de restauration";
"AccountViewController.processing.title" = "Traitement en cours";
"AccountViewController.processing.message" = "Merci de patienter, cette action peut durer un certain moment.";
"AccountViewController.restore.button.title" = "Restaurer l'achat";
"AccountViewController.restore.receipt-warning.title" = "Un problème a été rencontré lors de votre dernière transaction. Merci de restaurer vos abonnements.";
"AccountViewController.restore.logout-warning.title" = "Un problème a été rencontré lors de votre dernière transaction. Merci de retourner dans l'écran d'acceuil et de vous connecter à nouveau.";
"AppleInAppManagerError.noAppleReceiptFound" = "Reçu Apple non trouvé dans votre téléphone";
"AppleInAppManagerError.noPurchaseToRestore" = "Aucun achat a restaurer";
"AppleInAppManagerError.paymentWasCancelled" = "L'achat In-App a été annulé";
"AppleInAppManagerError.productRequestFailed" = "Impossible d'obtenir les produits In-App disponibles pour le moment";
"AppleInAppManagerError.noProductsFound" = "Aucun produit In-App n'a été trouvé";
"AppleInAppManagerError.noProductIDsFound" = "Aucun identifiants pour les produits In-App n'a été trouvé";
"CloudInAppManagerError.uploadReceiptError" = "Problème lors de l'upload du receipt";
"CloudInAppManagerError.productListError" = "Problème des récupération de la liste des produits in-app";
"CloudInAppManagerError.decodingDatas" = "Problème de décodage des données";
"MediaManViewController.title" = "Envoyer vers l'ordi";
"MediaPlayerViewController.trash-menu.remove.title" = "Supprimer";
"MediaPlayerViewController.asset-type.title" = "type";
"MediaPlayerViewController.creation-date.title" = "date de création";
"MediaPlayerViewController.frame-size.title" = "taille d'image";
"MediaPlayerViewController.file-size.title" = "taille du fichier";
"MediaPlayerViewController.duration.title" = "durée";
"MediaPlayerViewController.encoding.title" = "encodage";
"AssetExporter.send-menu.share-to-fb-reels.title" = "Partager sur Facebook Reels";
"AssetExporter.send-menu.share-to-fb-stories.title" = "Partager sur Facebook Stories";
"AssetExporter.send-menu.share-to-ig-stories.title" = "Partager sur Instagram Stories";
"AssetExporter.send-menu.share-to-ig-feed.title" = "Partager sur Instagram Feed";
"AssetExporter.send-menu.share-to-tiktok.title" = "Partager sur TikTok";
"AssetExporter.send-menu.share-to-meta-loo-long.msg" = "Les vidéos plus longues que %d minutes ne peuvent être partagées sur %@.";
"AssetExporter.send-menu.share-to-app-not-installed.msg" = "L'application %@ est nécessaire. Veuillez l'installer svp.";
"AssetExporter.send-menu.share-video-could-not-be-read.msg" = "Le fichier vidéo ne peut pas être lu.";
"AssetExporter.send-menu.share-video-all-photos-perm-req.msg" = "Cette opération nécessite l'accès à toutes les photos de votre Photothèque. Vous pouvez modifier les droits d'accès dans Réglages.";
"AssetExporter.open-in.no-app.msg" = "Il n'y a aucune app capable d'ouvrir ce média";
"AssetExporter.progress.title" = "Transfert en cours";
"AssetExporter.send-menu.webdav.title" = "Envoyer vers Media Manager";
"RecProfileViewController.director-mode.title" = "Mode “Director”";
"RecProfileViewController.director-mode.disabled" = "These options are only available when Director Mode is enabled.";
"RecProfileViewController.director-mode.frame-size.title" = "Format";
"RecProfileViewController.director-mode.frame-size.auto.short" = "Auto";
"RecProfileViewController.director-mode.frame-size.auto.long" = "Auto";
"RecProfileViewController.director-mode.frame-size.tip" = "Seulement les dispositifs compatibles vont pouvoir enregistrer dans la résolution sélectionnée. Les autres vont enregistrer dans leur plus haute résolution. REMARQUE: L'enregistrement en 4K n'est pas possible en mode Wi-Fi faible latence, n'est pas supporté par les dispositifs qui tournent sous iOS14 et est limité à 30 fps";
"RecProfileViewController.director-mode.frame-rate.title" = "Images par seconde";
"RecProfileViewController.director-mode.frame-rate.variable.title" = "Camera en mode VFR";
"RecProfileViewController.director-mode.frame-rate.variable.tip" = "Utilisez le mode VFR (Variable Frame Rate) pour augmenter la qualité en basse lumière";
"RecProfileViewController.director-mode.bit-rate.title" = "Débit d'enregistrement";
"RecProfileViewController.remote-camera.title" = "Caméras distantes";
"RecProfileViewController.remote-camera.rec.title" = "Enregistrer les caméras distantes";
"RecProfileViewController.remote-camera.rec.disabled" = "This option is only available when Director Mode is disabled.";
"RecProfileViewController.wifi.title" = "Wi-Fi Camera Communication";
"RecProfileViewController.outputs.title" = "Outputs";
"RecProfileViewController.live-output.title" = "Sortie Live";
"RecProfileViewController.live-output.rec.title" = "Enregistrer la sortie Live";
"RecProfileViewController.live-output.frame-size.title" = "Format de la sortie Live";
"RecProfileViewController.live-output.frame-size.tip" = "Ces paramètres sont appliqués uniquement lorsqu'aucun canal de diffusion n'est sélectionné.";
"prod-action.alert.stop-rec-and-broadcast-button.title" = "Arrêter enregistrement et diffusion";
"prod-action.alert.stop-broadcast-button.title" = "Poursuivre l'enregistrement";
"prod-action.alert.recover-broadcast-button.title" = "Rétablir la connexion";
"prod-action.alert.stop-button.title" = "Arrêter la diffusion";
"ProdError.error.BadUrlScheme.msg" = "Protocole non supporté";
"ProdError.error.NoHost.msg" = "Serveur de diffusion non atteignable";
"ProdError.error.NoSocket.msg" = "Canal de communication inexistant";
"ProdError.error.NoConnection.msg" = "Connexion au serveur de diffusion échouée";
"ProdError.error.NoESocket.msg" = "Couche de communication inexistante";
"ProdError.error.NoESocketHandshake.msg" = "Erreur de connexion: La validation de la sécurité n'est pas fonctionnelle";
"ProdError.error.NoRTMPHandshake.msg" = "Le serveur de diffusion ne répond pas";
"ProdError.error.BadRTMPHandshake.msg" = "Réponse erronée du serveur de diffusion";
"ProdError.error.UnknownRTMPError.msg" = "Erreur de protocole RTMP";
"ProdError.error.SocketClosedByPeer.msg" = "Erreur de diffusion: connection fermée par le serveur";
"ProdError.error.SocketError.msg" = "Erreur de diffusion: problème de communication";
"ProdError.error.SocketTxError.msg" = "Erreur de diffusion: transmission échouée";
"ProdError.error.SocketRxError.msg" = "Erreur de diffusion: réception échouée";
"ProdError.error.PracticeModeFailed.msg" = "Practice livestream failed";
"ProdError.error.SocketSecurityError.msg" = "Erreur de sécurité";
"ProdError.error.InvalidAccount.msg" = "Manque un compte valide pour le service de diffusion choisi";
"ProdError.error.InvalidIngestionSettings.msg" = "La résolution de diffusion n'est pas correcte";
"ProdError.error.StreamingProviderGenericError.msg" = "Erreur de la plateforme de diffusion";
"ProdError.error.InconsistentProfile.msg" = "Les paramètres de diffusion ne sont pas cohérents avec ceux du serveur. Veuillez retourner dans les réglages de l'évènement pour mettre à jours les paramètres";
"ProdError.error.BadHTTPResponseCode.msg" = "Le serveur a rejeté la requête";
"ProdError.error.BadHTTPResponseFormat.msg" = "La réponse du serveur est erronée";
"ProdError.error.HTTPRequestFailed.msg" = "Le serveur n'est pas disponible";
"ProdError.error.DualStreamingNotSupported.msg" = "Mode de diffusion non supporté";
"ProdError.error.NothingToStream.msg" = "Le flux de diffusion est vide";
"ProdError.error.HLSFailed.msg" = "Erreur HLS";
"ProdError.error.sec.TLS_INVALID_CERT.msg" = "Erreur de connexion: Le certificat de sécurité n'est pas valide";
"ProdError.error.sec.TLS_NO_CERT.msg" = "Erreur de connexion: Le certificat de sécurité n'est pas disponible";
"ProdError.error.sec.TLS_EXPIRED_CERT.msg" = "Erreur de connexion: Le certificat de sécurité est périmé";
"ProdError.error.sec.TLS_HANDSHAKE.msg" = "Sécurisation du canal de communication échouée";
"ProdError.error.sec.NET_CLOSED.msg" = "Connexion sécurisée interrompue par le serveur de diffusion";
"ProdError.error.RecordingError.msg" = "Erreur d'enregistrement";
"ProdError.error.network.connectivity.title" = "Poor Network Connection";
"ProdError.error.network.connectivity.msg" = "Make sure you are connected to a stable network and recover your broadcast.";
"ProdError.error.DiskFull.msg" = "Il n'y a plus assez d'espace de stockage pour continuer l'enregistrement";
"VideoBitRate.title" = "Débit Video";
"VideoBitRate.value.title" = "Débit";
"VideoBitRate.mode.title" = "Mode";
"VideoBitRate.mode.default.title.long" = "Débit standard";
"VideoBitRate.mode.default.title.short" = "Standard";
"VideoBitRate.mode.uniform.title" = "Débit Uniforme";
"VideoBitRate.mode.uniform.tip" = "Le même débit est appliqué quelque soit le format vidéo.";
"VideoBitRate.mode.adaptative.title" = "Adapter à la taille et à la cadence des images";
"VideoBitRate.mode.adaptative.tip" = "Pour les formats vidéo qui ne sont pas affichés ci-dessus, le débit est adapté proportionnellement à la taille et à la cadence des images.";
"VideoBitRate.mode.avc720p.title" = "Vidéo 1280 x 720, %d fps, H.264/AVC:";
"VideoBitRate.mode.avc1080p.title" = "Vidéo 1920 x 1080, %d fps, H.264/AVC:";
"VideoBitRate.mode.hevc2160p.title" = "Vidéo 3840 x 2160 (4K), %d fps, HEVC:";
"RLBatchViewController.collect-remote-media.title" = "Collecter les médias distants";
"RLBatchViewController.remove-remote-media.title" = "Effacer les médias distants";
"RLBatchViewController.render.title" = "Créer les compositions finales";
"RLBatchViewController.execute.title" = "Exécuter";
"RLBatchViewController.done.title" = "Opération terminée";
"RLInfoViewController.title" = "Dispositifs";
"RLInfoViewController.audio-input.title" = "Entrée audio";
"RLInfoViewController.audio-output.title" = "Sortie audio";
"RLInfoViewController.audio-output.disabled-speaker.title" = "Aucune";
"RLInfoViewController.battery.title" = "Batterie";
"RLInfoViewController.storage.title" = "Stockage utilisé";
"RLInfoViewController.cpu-load.title" = "Charge CPU";
"RLInfoViewController.capture-size.title" = "Source vidéo";
"RLInfoViewController.stream-size.title" = "Flux vidéo";
"RLInfoViewController.stream-type.title" = "Type de flux";
"RLInfoViewController.this-device.title" = "Ce dispositif";
"RLInfoViewController.audio-source-drift.title" = "Audio In Drift";
"RLInfoViewController.audio-dest-drift.title" = "Audio Out Drift";
"RLInfoViewController.pkt-loss.title" = "Perte de paquets";
"RLInfoViewController.latency.title" = "Latence réseau";
"RLInfoViewController.speedtest.title" = "Upload Speed Test";
"RLInfoViewController.speedtest.status" = "Status";
"RLInfoViewController.speedtest.status.idle" = "Idle";
"RLInfoViewController.speedtest.status.inProgress %lld" = "In progress (%lld%%)";
"RLInfoViewController.speedtest.status.success" = "Completed";
"RLInfoViewController.speedtest.status.error" = "Error";
"RLInfoViewController.speedtest.status.skipped" = "Skipped";
"RLInfoViewController.speedtest.status.cancelled" = "Cancelled";
"RLInfoViewController.speedtest.result" = "Result";
"RLInfoViewController.speedtest.result.na" = "N/A";
"RLInfoWarningView.warning.msg" = "Votre réseau Wi-Fi montre quelques signes de faiblesse au niveau latence et fiabilité. Nous vous suggérons d'activer le mode Wi-Fi renforcé.";
"RLInfoWarningView.warning.btn.title" = "Activer";
"CameraValidationViewController.device.text" = "Appareil";
"CameraValidationViewController.perm-msg.text" = "prend le contrôle";
"CameraValidationViewController.perm-allow-button.text" = "Autoriser";
"CameraValidationViewController.perm-reject-button.text" = "Rejeter";
"CameraValidationViewController.perm-auto-mode-switch.text" = "Autoriser systématiquement";
"MixerModel.create-new-event.title" = "Créer un nouvel évènement";
"MixerModel.create-new-event.subtitle" = "Please create a livestream and try again.";
"MixerModel.create-new-event.button" = "Create New Livestream";
"MixerModel.create-new-event.button.short" = "Schedule";
"MixerModel.no-rtmp-channel.title" = "No RTMP Channel Available";
"MixerModel.no-rtmp-channel.subtitle" = "Please set up an RTMP channel and try again.";
"MixerModel.no-rtmp-channel.button" = " Set Up RTMP ";
"MixerModel.create-edit-event.title" = "Modifier l'évènement";
"MixerModel.audio-warning.title" = "Attention";
"MixerModel.audio-warning.msg" = "L'audio est actuellement désactivé ou utilisé par une autre application.";
"MixerModel.audio-warning.restart" = "Activer l'audio";
"OnePaneMixerViewController.stream-validation.msg" = "Flux en cours de validation...";
"OnePaneCameraViewController.remote-xfer-progress.text" = "Transfert de médias";
"OnePaneCameraViewController.menu.title" = "Menu";
"OnePaneCameraViewController.disconnect.title" = "Se déconnecter de la régie";
"OnePaneCameraViewController.disconnect.named.title" = "Se déconnecter de la régie \"%@\"";
"OnePaneCameraViewController.disconnect.confirm.msg" = "Voulez-vous vraiment vous déconnecter de la régie ?";
"OnePaneCameraViewController.disconnect.confirm.named.msg" = "Voulez-vous vraiment vous déconnecter de la régie \"%@\" ?";
"OnePaneCameraViewController.disconnect.confirm.yes" = "Déconnecter";
"OnePaneCameraViewController.disconnect.confirm.no" = "Rester connecté";
"OsmoConnectionViewController.title" = "DJI Osmo Mobile";
"OsmoConnectionViewController.searching.title" = "Recherche en cours...";
"OsmoConnectionViewController.error.connection.msg" = "Impossible de se connecter à %@";
"MediaCollectionViewController.location.photo-lib.title" = "Photothèque";
"MediaCollectionViewController.media-type.journal.title" = "Journal";
"MediaCollectionViewController.media-type.live.title" = "Live";
"MediaCollectionViewController.media-type.composition.title" = "Composition";
"MediaCollectionViewController.media-type.aux.title" = "Média auxiliaire";
"MediaCollectionViewController.media-type.unknown.title" = "Média inconnu";
"MediaCollectionViewController.media-type.special.title" = "Média spécial";
"LocalMediaTableViewController.media-type.unknown.title" = "Média inconnu";
"LocalMediaTableViewController.media-type.special.title" = "Média spécial";
"LocalMediaTableViewController.type.recorded-audio.title" = "Source audio";
"LocalMediaTableViewController.type.recorded-video.title" = "Source video";
"LocalMediaTableViewController.type.recorded-compo.title" = "Composition";
"LocalMediaTableViewController.type.recorded-live.title" = "Live";
"LocalMediaTableViewController.type.recorded-clip.title" = "Clip";
"LocalMediaTableViewController.type.imported-audio.title" = "Audio importé";
"LocalMediaTableViewController.type.imported-video.title" = "Video importée";
"LocalMediaTableViewController.type.imported-image.title" = "Image importée";
"LocalMediaTableViewController.photo-imported.title" = "Photos importées";
"LocalMediaTableViewController.video-imported.title" = "Vidéos importées";
"LocalMediaTableViewController.video-recordings.title" = "Mes enregistrements";
"JournalViewController.remove.nothing.title" = "Rien ne peut être supprimé.";
"JournalViewController.remove.single.title" = "Êtes-vous sûr de vouloir supprimer 1 média ?";
"JournalViewController.remove.multiple.title" = "Êtes-vous sûr de vouloir supprimer %d médias ?";
"JournalViewController.remove.msg" = "Les médias auxiliaires et les médias stockés dans la Photothèque ne seront pas supprimés.";
"JournalViewController.remove.single.action" = "Supprimer 1 média";
"JournalViewController.remove.multiple.action" = "Supprimer %d médias";
"JournalViewController.send-to.compo.action" = "Composition";
"JournalViewController.send-to.all.action" = "Tous les médias";
"MediaProcessing.photo-lib.error.no-access" = "Cette app n'a pas accès à votre Photothèque.";
"MediaProcessing.photo-lib.error.failed" = "Le transfert vers la Photothèque a échoué.";
"MediaProcessing.photo-lib.success" = "Le transfert s'est effectué avec succès.";
"CityProducerViewController.title" = "Transférer vers CTpro";
"CityProducerViewController.local.title" = "CTpro sur ce dispositif";
"CityProducerViewController.local-dest.title" = "Apps";
"CityProducerViewController.remote-dest.title" = "Destinations sur le réseau";
"CityProducerViewController.missing.msg" = "CTpro n'est pas disponible sur ce dispositif.";
"welcome-d3.button.close" = "Fermer";
"welcome-d3.button.back" = "Retour";
"welcome-d3.button.login" = "Connexion";
"welcome-d3.button.logout" = "Déconnexion";
"welcome-d3.button.login.info" = "Connectez-vous pour accéder à votre compte";
"welcome-d3.button.create-account" = "Créer un compte";
"welcome-d3.button.share-device" = "Partager ce dispositif";
"welcome-d3.button.switcher-mode" = "Mode régie";
"welcome-d3.separator.or" = "OU";
"welcome-d3.button.troubleshoot" = "Problèmes de connexion ?";
"welcome-d3.field.user" = "Adresse e-mail";
"welcome-d3.field.password" = "Mot de passe";
"welcome-d3.field.forgot.password" = "Mot de Passe Oublié?";
"welcome-d3.field.create-account.user.placeholder" = "Entrez votre adresse e-mail";
"welcome-d3.field.create-account.password.placeholder" = "Créez un mot de passe";
"welcome-d3.field.create-account.confirmation.placeholder" = "Confirmez votre mot de passe";
"welcome-d3.alert.authentication-error" = "Erreur d'authentification";
"welcome-d3.alert.authentication-connection-error.title" = "Pas de connexion Internet";
"welcome-d3.alert.authentication-connection-error.message" = "Votre connexion Internet semble être hors ligne. Vérifiez la connexion et réessayez.";
"welcome-d3.alert.authentication-input-error.title" = "Problème de connexion";
"welcome-d3.alert.authentication-input-error.message" = "Adresse e-mail ou mot de passe invalide. Vérifiez et réessayez";
"welcome-d3.message.go-live-on" = "Fait un live sur";
"welcome-d3.warning.refresh-denied" = "Votre session a expiré ou vous avez ouvert une session sur un autre dispositif.";
"welcome-login.text.value-prop" = "**Toutes vos vidéos, dans Switcher**";
"welcome-login.button.login-signup" = "Connexion / Créer un compte";
"welcome-login.button.share" = "Partager Caméra ou Écran";
"account-creation.generic.error" = "";
"account-creation.web-error.error" = "La création du compte a échoué lors de l'authentification";
"account-creation.auth-error.error" = "Erreur d'authentification";
"account-creation.no-connection.error" = "Impossible de se connecter au serveur";
"account-creation.invalid-user.error" = "Adresse e-mail non valide";
"account-creation.unmatched-passwords.error" = "Confirmation du mot de passe erronée";
"email-confirmation.title" = "Confirmation de l'e-mail";
"email-confirmation.text1" = "Suivez les instructions reçues par e-mail.";
"email-confirmation.text2" = "<html>Nous avons envoyé un e-mail à <a action=\"email\">%@</a>. Pour confirmer votre adresse, cliquez sur le lien qui se trouve dans l'e-mail.</html>";
"email-confirmation.resend.title" = "E-mail non reçu ?";
"email-confirmation.resend.ack" = "Une nouvelle copie de l'e-mail a été envoyée à %@.";
"sharing-choice.button.share-screen.start" = "Partager cet écran";
"sharing-choice.button.share-screen.subtitle" = "Utiliser l’ecran de cet appareil comme source dans votre production.";
"sharing-choice.button.share-screen.stop" = "Arrêter le partage";
"sharing-choice.button.share-camera.start" = "Partager cette Caméra";
"sharing-choice.button.share-camera.subtitle" = "Utiliser la caméra de cet appareil comme source dans votre production.";
"waiting-connection.title.share-screen" = "Partage de cet écran en cours";
"waiting-connection.message.waiting-for-connection" = "Attente de connexion depuis la régie";
"waiting-connection.more-informations.title" = "Plus d'informations";
"waiting-connection.share-camera.message %@" = "Tap this iOS device’s name in the Inputs tab (%@) on your main iOS device.";
"waiting-connection.infos-line1.message" = "La caméra n'apparaît pas dans la liste ?";
"waiting-connection.infos-line2.message" = "Connecter la caméra avec cette URL :";
"waiting-connection.infos-line3.message" = "Wi-Fi non disponible";
"AssetExportToPHLibViewController.phlib.title.label" = "Envoyez vers votre Photothèque";
"AssetExportToPHLibViewController.ctpro.title.label" = "Envoyez vers CTpro";
"AssetExportToPHLibViewController.countmedias.label" = "%d média(s) sélectionné(s)";
"AssetExportToPHLibViewController.move.button" = "Déplacer";
"AssetExportToPHLibViewController.move.description" = "Supprime les média(s) de Switcher";
"AssetExportToPHLibViewController.copy.button" = "Copier";
"AssetExportToPHLibViewController.copy.description" = "Garde une copie des média(s) dans Switcher";
"AssetExportToPHLibViewController.loading.label" = "Veuillez svp attendre que le transfert soit terminé.";
"AssetExportToPHLibViewController.ctpro.opening" = "Ouverture de l'application CTpro en cours";
"ToolLibrary.reorder.title" = "Personnaliser";
"ToolLibrary.reorder.titleForDeleteConfirmationButton" = "Supprimer";
"ToolLibrary.section.visibleTools" = "Outils visibles";
"ToolLibrary.section.hiddenTools" = "Plus d'outils";
"ToolLibrary.section.restoreTools" = "";
"ToolLibrary.section.restoreTools.restore-defaults-button.title" = "Rétablir l'ordre par défaut";
"ToolLibrary.section.restoreTools.confirmation.msg" = "Êtes vous sûr de vouloir rétablir l'ordre par défaut ?";
"ToolLibrary.section.restoreTools.confirmation.button.title" = "Rétablir";
"ToolLibrary.tool.input" = "Entrées";
"ToolLibrary.tool.output" = "Sorties";
"ToolLibrary.tool.effect" = "Effets";
"ToolLibrary.tool.camera-control" = "Contrôle caméra";
"ToolLibrary.tool.audio" = "Audio";
"ToolLibrary.tool.comment" = "Live Comments";
"ToolLibrary.tool.more" = "Plus";
"ToolLibrary.tool.info" = "Info";
"ToolLibrary.tool.encoding" = "Encodage";
"ToolLibrary.tool.star" = "Assets";
"ToolLibrary.tool.custom" = "Personnalisation";
"ToolLibrary.tool.react-native" = "React Native";
"ToolLibrary.tool.auto-switch" = "Auto-switch";
"ToolLibrary.tool.development" = "Development Tool";
"ToolLibrary.tool.markers" = "Marque-temps";
"RLAudioEngine.SrcLineIn.title" = "Entrée jack analogique";
"RLAudioEngine.SrcBuiltInMic.title" = "Micro intégré";
"RLAudioEngine.SrcWiredInput.title" = "Entrée micro/ligne câblée";
"RLAudioEngine.SrcBluetoothHFP.title" = "Micro Bluetooth";
"RLAudioEngine.SrcUSBAudio.title" = "Entrée audio USB/Lightning";
"RLAudioEngine.SrcUSBCAudio.title" = "Entrée audio USB";
"RLAudioEngine.SrcCarAudio.title" = "Entrée CarPlay";
"RLAudioEngine.SrcVirtual.title" = "Entrée audio virtuelle";
"RLAudioEngine.SrcPCI.title" = "Entrée audio PCI";
"RLAudioEngine.SrcFireWire.title" = "Entrée audio FireWire";
"RLAudioEngine.SrcDisplayPort.title" = "Entrée audio Display Port";
"RLAudioEngine.SrcAVB.title" = "Entrée Audio Video Bridging";
"RLAudioEngine.SrcThunderbolt.title" = "Entrée audio Thunderbolt";
"RLAudioEngine.DestLineOut.title" = "Sortie jack analogique";
"RLAudioEngine.DestHeadphones.title" = "Écouteurs / Casque";
"RLAudioEngine.DestBluetoothA2DP.title" = "Sortie Bluetooth";
"RLAudioEngine.DestBuiltInReceiver.title" = "Écouteur intégrés";
"RLAudioEngine.DestBuiltInSpeaker.title" = "Hautparleurs intégrés";
"RLAudioEngine.DestHDMI.title" = "Sortie HDMI";
"RLAudioEngine.DestAirPlay.title" = "Sortie Airplay";
"RLAudioEngine.DestBluetoothLE.title" = "Sortie Bluetooth LE";
"RLAudioEngine.DestBluetoothHFP.title" = "Sortie Bluetooth HFP";
"RLAudioEngine.DestUSBAudio.title" = "Sortie audio USB";
"RLAudioEngine.DestCarAudio.title" = "Sortie CarPlay";
"RLAudioEngine.DestVirtual.title" = "Sortie audio virtuelle";
"RLAudioEngine.DestPCI.title" = "Sortie audio PCI";
"RLAudioEngine.DestFireWire.title" = "Sortie audio FireWire";
"RLAudioEngine.DestDisplayPort.title" = "Sortie audio Display Port";
"RLAudioEngine.DestAVB.title" = "Sortie Audio Video Bridging";
"RLAudioEngine.DestThunderbolt.title" = "Sortie audio Thunderbolt";
"RLAudioEngine.unknown.title" = "Inconnu";
"CloudAssetsListViewController.title" = "Switcher Cloud";
"CloudAssetsListViewController.edit.title" = "Sélectionner";
"CloudAssetsListViewController.error.title" = "Erreur de chargement des assets depuis le Cloud : %@";
"CloudAssetsListViewController.switcher-filetype.mmsrc.title" = "Audio/Vidéo";
"CloudAssetsListViewController.switcher-filetype.mmvideo.title" = "Vidéo";
"CloudAssetsListViewController.switcher-filetype.mmaudio.title" = "Audio";
"CloudAssetsListViewController.switcher-filetype.mmfx.title" = "Effet";
"CloudAssetsListViewController.switcher-filetype.mmart.title" = "Élément graphique";
"CloudAssetsListViewController.switcher-filetype.mmjrnl.title" = "Journal";
"CloudAssetsListViewController.switcher-filetype.undefined.title" = "Fichier Switcher inconnu";
"CloudAssetsListViewController.switcher-filetype.image.title" = "Image";
"CloudAssetsListViewController.switcher-filetype.movie.title" = "Vidéo";
"CloudAssetsListViewController.switcher-filetype.audio.title" = "Audio";
"CloudAssetsListViewController.no-asset.title" = "Aucun média disponible.";
"CloudAsseUploadViewController.title.label" = "Upload des médias sur Switcher Cloud";
"CloudAssetUploadViewController.countmedias.label" = "%d média(s) sélectionné(s)";
"CloudAssetUploadViewController.success.label" = "Upload %d média(s) avec succès";
"CloudAssetUploadViewController.failed.label" = "Erreur d'upload sur le serveur \nSwitcher Cloud";
"CloudAssetUploadViewController.failed.max-assets.label" = "Limite de nombre de médias dépassée sur Switcher Cloud";
"CloudVideoStorageView.text.primary.mid" = "Storage limit approaching";
"CloudVideoStorageView.text.primary.high" = "Storage limit reached";
"CloudVideoStorageView.text.studio.attributed" = "You are approaching your overall storage limit of video uploads. Upgrade Your Plan";
"CloudVideoStorageView.text.studio.limit.attributed" = "You have reached your overall storage limit of video uploads. Upgrade Your Plan";
"CloudVideoStorageView.text.attributed.substring" = "Upgrade Your Plan";
"CloudVideoStorageView.text.business.attributed" = "You are approaching your overall storage limit of video uploads. Delete videos or contact us for more storage";
"CloudVideoStorageView.text.business.limit.attributed" = "You have reached your overall storage limit of video uploads. Delete videos or contact us for more storage";
"CloudVideoStorageView.text.business.attributed.substring" = "contact us for more storage";
"CloudVideoStorageView.text.studio.no.sub.attributed" = "You are approaching your overall storage limit of video uploads. Visit your account at switcherstudio.com to manage your subscription.";
"CloudVideoStorageView.text.studio.no.sub.limit.attributed" = "You have reached your overall storage limit of video uploads. Visit your account at switcherstudio.com to manage your subscription.";
"CloudVideoStorageView.text.studio.no.sub.attributed.substring" = "switcherstudio.com";
"CloudVideoStorageView.progress.text %lld %lld" = "%lld of %lld";
"SelectMenuManager.edit.title" = "Select";
"D3UserMainViewController.badge.dev" = "Dev";
"D3UserMainViewController.badge.trial" = "Période d'essai";
"D3UserMainViewController.badge.limited-access" = "Compte expiré";
"D3UserMainViewController.badge.dev.alert" = "Yeahh, tu es sur le serveur de dev !!!";
"D3UserMainViewController.badge.trial.alert" = "Nous espérons que vous ayez du plaisir à utiliser Switcher Studio pendant cette période d’essai gratuite qui vous donne momentanément accès à toutes les fonctionnalités de l’application. Pendant cette période d’essai, l’application ajoute le logo Switcher en filigrane sur vos vidéos. Pour ne plus avoir ce logo, abonnez-vous à Switcher Studio.";
"UserMainView.message" = "Bonjour!\n Que voulez-vous créer aujourd’hui ?";
"UserMainView.Creation.title" = "Faire un Live ou enregistrer une vidéo";
"UserMainView.Creation.text" = "Utilisez notre suite complète d’outils de montage en direct et multi-caméras.";
"UserMainView.horizontal" = "Horizontale";
"UserMainView.vertical" = "Verticale";
"UserMainView.ratio" = "VIDEO RATIO";
"AssetProdCollectionViewController.title" = "Médias";
"AssetProdCollectionViewController.new" = "New";
"AssetProdCollectionViewController.choice.photo" = "Photo";
"AssetProdCollectionViewController.choice.video" = "Vidéo";
"AssetProdCollectionViewController.choice.audio" = "Audio";
"AssetProdCollectionViewController.choice.shopping" = "Fiche";
"AssetProdCollectionViewController.choice.text-graphics" = "Texte ou élément graphique";
"AssetProdCollectionViewController.choice.multiviews" = "Composition";
"AssetProdCollectionViewController.choice.cloud" = "Switcher Cloud";
"AssetProdCollectionViewController.choice.camera" = "Entrée";
"AssetProdCollectionViewController.choice.logo" = "Logo";
"AssetProdCollectionViewController.choice.imagesoverlay" = "Image Overlays";
"AssetProdCollectionViewController.choice.lowerthird" = "Bannière";
"AssetProdCollectionViewController.choice.image-overlay" = "Image en superposition";
"MediaListTableViewController.photo.title" = "Photo";
"MediaListTableViewController.video.title" = "Vidéo";
"MediaListTableViewController.audio.title" = "Audio";
"MediaListTableViewController.choice.audio-from-video" = "Audio d'un fichier vidéo";
"MediaListTableViewController.choice.audio-imported" = "Audios importés";
"MediaListTableViewController.choice.photo-library" = "Phototèque";
"MediaListTableViewController.choice.photo-imported" = "Images importées";
"MediaListTableViewController.choice.video-imported" = "Vidéos importées";
"MediaListTableViewController.choice.my-recordings" = "Mes enregistrements";
"MediaListTableViewController.choice.samples" = "Exemples";
"MediaListTableViewController.choice.switcher-cloud" = "Switcher Cloud";
"MediaListTableViewController.choice.ios-picker" = "Dossiers / Clé USB";
"MediaListTableViewController.choice.no-image" = "Aucune image";
"MediaListTableViewController.choice.backgrounds" = "Images de fond";
"MediaListTableViewController.choice.gradient" = "Dégradés";
"MediaListTableViewController.choice.platform-icons" = "Icônes de plateformes";
"MediaListTableViewController.choice.patterns" = "Motifs";
"MediaListTableViewController.choice.frames" = "Cadres";
"GradientGeneratorViewController.title" = "Dégradé";
"GradientGeneratorViewController.params.main-color" = "Couleur principale";
"GradientGeneratorViewController.params.nuance" = "Nuance";
"GradientGeneratorViewController.params.brightness" = "Luminosité";
"GradientGeneratorViewController.params.orientation" = "Orientation";
"MediaListTableViewController.social-icons.title" = "Réseaux sociaux";
"MediaListTableViewController.choice.social.facebook" = "Facebook";
"MediaListTableViewController.choice.social.instagram" = "Instagram";
"MediaListTableViewController.choice.social.youtube" = "YouTube";
"MediaListTableViewController.choice.social.twitter" = "Twitter";
"MediaListTableViewController.choice.social.linkedin" = "LinkedIn";
"MediaListTableViewController.donation-icons.title" = "Donations";
"MediaListTableViewController.choice.donation.cashapp" = "Cashapp";
"MediaListTableViewController.choice.donation.givelify" = "Givelify";
"MediaListTableViewController.choice.donation.patreon" = "Patreon";
"MediaListTableViewController.choice.donation.paypal" = "Paypal";
"MediaListTableViewController.choice.donation.tithely" = "Tithely";
"MediaListTableViewController.choice.donation.venmo" = "Venmo";
"AssetGridViewController.photo.title" = "Toutes les photos";
"AssetGridViewController.video.title" = "Toutes les vidéos";
"AlbumListTableViewController.title" = "Albums";
"AlbumListTableViewController.section.smart-albums" = "Albums intelligents";
"AlbumListTableViewController.section.user-albums" = "Albums personnels";
"AlbumListTableViewController.section.icloud-albums" = "Photos iCloud";
"AlbumListTableViewController.section.empty.title" = "Aucun album disponible";
"SamplesCollectionViewController.title" = "Exemples";
"SamplesCollectionViewController.choice.pattern" = "Test Pattern";
"SamplesCollectionViewController.choice.cornerbug" = "Logo";
"SamplesCollectionViewController.choice.slide" = "Slide";
"SamplesCollectionViewController.choice.image-fullscreen" = "Photo";
"SamplesCollectionViewController.choice.video-cereals" = "Céréales";
"SamplesCollectionViewController.choice.video-ocean" = "Océan";
"BackgroundsCollectionViewController.title" = "Images de fond";
"OpacityViewManager.loading.title" = "Chargement en cours";
"TextAndGraphicsListTableViewController.title" = "Textes & Graphiques";
"TextAndGraphicsListTableViewController.choice.title" = "Titres";
"TextAndGraphicsListTableViewController.choice.lower-third" = "Bannières";
"TextAndGraphicsListTableViewController.choice.animated-text" = "Textes animés";
"TextAndGraphicsListTableViewController.choice.timers" = "Minuteurs";
"TimersCollectionViewController.timers.stopwatches.section" = "Timers and Stopwatches";
"TimersCollectionViewController.timers.section" = "Timers Only";
"TextAndGraphicsListTableViewController.choice.platform-overlay" = "Bannières de plateformes";
"TextAndGraphicsListTableViewController.choice.broadcast-notification" = "Avis de diffusion";
"TextAndGraphicsListTableViewController.choice.corner-bug" = "Logos";
"TextAndGraphicsListTableViewController.choice.image-overlay" = "Image en superposition";
"TextAndGraphicsListTableViewController.choice.slideshow" = "Image Slideshow";
"TextAndGraphicsListTableViewController.choice.image-lower-third" = "Image en tant que bannière";
"MultiviewsCollectionViewController.title" = "Compositions";
"MultiviewsCollectionViewController.choice.dashboard" = "Tableau de bord";
"MultiviewsCollectionViewController.choice.split-screen" = "Écran divisé";
"MultiviewsCollectionViewController.choice.flap" = "Volet";
"MultiviewsCollectionViewController.choice.slots" = "Bandes";
"MultiviewsCollectionViewController.choice.picture-in-picture" = "Image dans l'image";
"MultiviewsCollectionViewController.choice.grid" = "Grille";
"BaseProdFlow.alert.cloud.available-soon" = "Bientôt disponible";
"AudioSettingTableViewController.title" = "Paramètres audio";
"AudioSettingsTableViewController.boost-section.title" = "Boost";
"AudioSettingsTableViewController.boost-section.value-template" = "%d dB";
"AudioSettingsTableViewController.routing-section.title" = "Routing";
"AudioSettingsTableViewController.routing-section.none" = "Aucun";
"AudioSettingsTableViewController.routing-section.leftToMono" = "Gauche vers mono";
"AudioSettingsTableViewController.routing-section.rightToMono" = "Droite vers mono";
"AudioSettingsTableViewController.routing-section.sumToMono" = "Somme vers mono";
"LimitedAccessViewController.header" = "Compte expiré";
"LimitedAccessViewController.body" = "Votre compte a expiré.\n\nVeuillez contacter\<EMAIL>\npour toute question.";
"LimitedAccessViewController.support-button" = "Contacter le support";
"SubscriptionExpiredAlert.header" = "Expired Account";
"SubscriptionExpiredAlert.body" = "Your account has expired.\n\nPlease contact \n[<EMAIL>](mailto:<EMAIL>)\nwith any questions.";
"NetworkUnavailableAlert.header" = "No Internet Connection";
"NetworkUnavailableAlert.body" = "Your Internet connection appears to be offline. Check your connection and try again.";
"StorageLimitAlert.header" = "Attention";
"StorageLimitAlert.body %lld %lld" = "You have reached your overall storage limit of video uploads (%lld of %lld). Delete videos to free up space before uploading more.";
"StorageLimitAlert.button.manage" = "Manage Video Library";
"StorageLimitAlert.button.dismiss" = "Dismiss";
"PHAssetCustomError.notSupported.msg" = "Format non supporté";
"PHAssetCustomError.notLocal.msg" = "Le fichier n'existe pas localement";
"PHAssetCustomError.noValidSource.msg" = "Source non valide";
"PHAssetCustomError.notResolvedUrl.msg" = "L'URL n'a pas pu être résolue";
"PHAssetCustomError.noAVAsset.msg" = "AVAsset non valide";
"PHAssetCustomError.cancelRequest.msg" = "La requête a été annulée par l'utilisateur";
"MyRecordingHeaderView.sort.date" = "Par Date";
"MyRecordingHeaderView.sort.name" = "Par Nom";
"CloudyToolViewController.progress.msg" = "Téléchargement...";
"CloudyToolViewController.not-available.msg" = "Non disponible";
"Asset.in-photo-library.single" = "Photo Library Média";
"Asset.in-photo-library.multiple" = "Photo Library Médias";
"ColorPickerViewControllerV2.title" = "Couleur";
"ColorPickerViewControllerV2.selector.palette" = "Palette";
"ColorPickerViewControllerV2.selector.rgb" = "RVB";
"ColorPickerViewControllerV2.selector.predefined-colors" = "Échantillons";
"ColorPickerViewControllerV2.css-color-code.alert.title" = "Code couleur CSS";
"ColorPickerViewControllerV2.css-color-code.alert.placeholder" = "Code couleur CSS";
"ColorPickerViewControllerV2.rgb-sliders.red-label" = "R";
"ColorPickerViewControllerV2.rgb-sliders.green-label" = "V";
"ColorPickerViewControllerV2.rgb-sliders.blue-label" = "B";
"DiagInformationsTableViewController.informations.title" = "Diagnostic";
"DiagInformationsTableViewController.version.title" = "Version de l'application";
"DiagInformationsTableViewController.build-number.title" = "Numéro de Build";
"DiagInformationsTableViewController.from.title" = "Origine";
"DiagInformationsTableViewController.tool-bundle-version.title" = "Version du Tool Bundle";
"DiagInformationsTableViewController.tool-api-version.title" = "Version du Tool Api";
"DiagInformationsTableViewController.sessions.title" = "Sessions";
"DiagInformationsTableViewController.log-activation.title" = "Journal de diagnostic";
"DiagInformationsTableViewController.log-activation.enable.title" = "Logs";
"DiagInformationsTableViewController.log-activation.enable.msg" = "Les logs seront activés au prochain démarrage de l'application pour une durée de %d heures. Ceci aide les équipes de R&D et du support à mieux analyser les erreurs qui peuvent se produirent.";
"DiagInformationsTableViewController.log-activation.disable.title" = "Arrêt des logs";
"DiagInformationsTableViewController.log-activation.disable.msg" = "Les logs seront désactivés au prochain démarrage de l'application.";
"DiagInformationsTableViewController.log-activation.currently-enable.msg" = "Les logs sont actuellement activés. Ceci aide les équipes de R&D et du support à mieux analyser les erreurs qui peuvent se produirent.";
"AutoSwitchTableViewController.title" = "Auto-switch";
"AutoSwitchTableViewController.sources.header.title" = "Sources";
"AutoSwitchTableViewController.actions.header.title" = "Actions";
"AutoSwitchTableViewController.settings.header.title" = "Paramètres";
"AutoSwitchTableViewController.sources.no-source.title" = "Pas de Source";
"AutoSwitchTableViewController.action-item.start.title" = "Commencer";
"AutoSwitchTableViewController.action-item.stop.title" = "Arrêter";
"AutoSwitchTableViewController.action.playing" = "Live";
"AutoSwitchTableViewController.settings.transition-timing.title" = "Intervalle";
"AutoSwitchTableViewController.settings.shuffle.title" = "Aléatoire";
"LoaderInAppViewController.button.ok" = "OK";
"AudioEditorViewController.title" = "Editeur";
"AudioEditorViewController.volume.title" = "Volume";
"AudioEditorViewController.volume.value-db" = "%@ dB";
"AudioEditorViewController.thumbnail.tag.title" = "Tag";
"AudioEditorViewController.thumbnail.tag.placeholder" = "Aucun texte";
"AudioEditorViewController.loop.title" = "Loop";
"AudioEditorViewController.loop.description" = "When loop is enabled, the audio asset will play continually until removed from screen.";
"MediaPlayerVideoCore.clips.button.title" = "Clips";
"ClipsTableViewController.title" = "Clips";
"ClipsTableViewController.clip-list.header.title" = "Liste des clips";
"ClipsTableViewController.clip-list.no-clip.title" = "Aucun clip";
"MultiviewsProdFlow.group-1.title" = "1 source";
"MultiviewsProdFlow.group-2.title" = "2 sources";
"MultiviewsProdFlow.group-3.title" = "3 sources";
"MultiviewsProdFlow.group-4.title" = "4 sources";
"MultiviewsProdFlow.group-5.title" = "5 sources";
"MultiviewsProdFlow.group-6.title" = "6 sources ou plus";
"ElementTransitions.title" = "In & Out Transition";
"ElementTransitions.toggle.title" = "Enable Transition";
"ElementTransitions.toggle.hint" = "When enabled, this will override the main transition settings.";
"ElementTransitions.select.title" = "Transition";
"ElementTransitions.select.hint" = "To adjust transition properties, please go to the Transitions tool.";
"ElementGroupsViewController.elementGroups.title" = "Groupes";
"ElementGroupsViewController.section.existingGroups" = "Groupes existants";
"ElementGroupsViewController.section.addNewGroup.button.title" = "Ajouter un groupe";
"ElementGroupsViewController.groups.mainGroup" = "Principal";
"ElementGroupsViewController.groups.delete.elements" = "Ce groupe n'est pas vide. Que voulez-vous faire ?";
"ElementGroupsViewController.groups.delete.all" = "Supprimer le groupe et son contenu";
"ElementGroupsViewController.groups.delete.collection" = "Supprimer uniquement le groupe";
"ZoomSliderViewController.editSections.title" = "Gérer les groupes";
"Clips.short-description" = "Les clips sont des versions modifiés de vos enregistrements.";
"Clips.long-description" = "Les clips sont des versions modifiés de vos enregistrements. Vous pouvez découper la portion de vidéo que vous souhaitez et changer la vitesse de lecture.";
"Clips.edit-original-asset.tip" = "Créez un clip et éditez-le selon vos besoins.";
"Clips.create-new.title" = "Créer un nouveau clip";
"Clips.extract.title" = "Créer un clip (éditer)";
"CamoHelpViewController.title" = "Switcher en tant que webcam";
"CamoHelpViewController.instructions.intro" = "Vous pouvez envoyer la sortie audio et vidéo de Switcher dans Zoom, Google Meet, Microsoft Teams ou n’importe quel autre outil de vidéoconférence qui tourne sur votre Mac ou PC. Voici comment :";
"CamoHelpViewController.instructions.1.0" = "Téléchargez et exécutez Camo Studio sur votre Mac ou PC.";
"CamoHelpViewController.instructions.1.1" = "Téléchargez Camo Studio sur le site camo.studio.";
"CamoHelpViewController.instructions.1.share-button" = "Partager le lien du site";
"CamoHelpViewController.instructions.2.0" = "Connectez cet iPad ou iPhone à votre Mac ou PC.";
"CamoHelpViewController.instructions.2.1" = "Utilisez un câble fiable, tel que celui fourni avec votre iPhone. Évitez d’utiliser un Hub USB.";
"CamoHelpViewController.instructions.3.0" = "Suivez les instructions dans Camo Studio.";
"CamoHelpViewController.instructions.footnote" = "Si vous voulez entendre les autres participants, utilisez un casque connecté à votre ordinateur. Sinon, coupez les haut-parleurs de votre ordinateur de façon à éviter écho et larsen.";
"news.since-version" = "13.7.0";
"news.title" = "Nouveautés";
"news.version-prefix" = "Version";
"news.description" = "# Nouveautés\
* NOUVEAU diaporama: une fonctionnalité intégrée pour afficher en boucle un ensemble de logos ou d'images\
* Vérifiez l'espace de stockage disponible avant de démarrer un live avec un enregistrement local\
* Correction de bugs et amélioration de la stabilité";
"SourcePropBundleEditor.overlay.tips" = "En surimpression signifie affiché par dessus un élément graphique ou une source en plein écran.";
"SourcePropBundleEditor.chromakey.tips" = "Select the color to be removed or replaced with your background image.";
"SourcePropBundleEditor.align.vertical" = "Vertical";
"SourcePropBundleEditor.align.horizontal" = "Horizontal";
"MultiviewBehavior.title" = "Attribution des emplacements";
"MultiviewBehavior.description" = "Mode d'attribution des emplacements";
"MultiviewBehavior.multiview.title" = "Sélectionner à chaque fois";
"MultiviewBehavior.multiview.description" = "Chaque fois que vous choisissez cette composition, vous serez invité à sélectionner des sources ou éléments graphiques pour remplir chaque emplacement vide.";
"MultiviewBehavior.scene.title" = "Présélectionner";
"MultiviewBehavior.scene.description" = "Vous allez d'abord sélectionner les sources de cette composition (caméras et écrans uniquement) dans “Propriétés de la composition” (fenêtre précédente). Ensuite, chaque fois que vous choisirez cette composition, tous les emplacements seront automatiquement remplis avec les sources sélectionnées.";
"MultiviewBehavior.overlay.title" = "Réutiliser la source actuelle";
"MultiviewBehavior.overlay.description" = "Vous allez d'abord sélectionner toutes les sources de cette composition sauf une dans “Propriétés de la composition” (fenêtre précédente). Ensuite, chaque fois que vous choisirez cette composition, l’emplacement restant sera automatiquement rempli avec votre source actuelle. Quand vous choisirez une autre source, c’est dans ce même emplacement qu’elle s’affichera. Pour quitter la composition et n’afficher plus que la source actuelle, il faudra désélectionner la composition.";
"MultiviewBehavior.special-inputs.unknown.title" = "Source inconnue";
"MultiviewBehavior.special-inputs.unassigned.title" = "Aucune source";
"MultiviewBehavior.special-inputs.overlay-background.title" = "Source actuelle";
"MultiviewBehavior.special-inputs.image.title" = "Image";
"MultiviewBehavior.special-inputs.title.title" = "Title";
"MultiviewBehavior.special-inputs.video.title" = "Video";
"IntroGenerator.editor.title" = "Créer une vidéo d'introduction";
"IntroGenerator.selection.title" = "Vidéo d'introduction";
"IntroGenerator.processing.title" = "Merci de patienter pendant pendant la création de la vidéo";
"IntroGenerator.processing.error" = "Impossible de créer la vidéo d'introduction";
"ShoppingListViewController.title" = "Fiche";
"ShoppingList.choice.camera-as-background" = "Caméra en arrière-plan";
"ShoppingList.choice.image-as-background" = "Image en arrière-plan";
"ShoppingList.choice.multiview" = "2 sources";
"ShoppingList.choice.live-selling-samples" = "Echantillons pour la vente en ligne";
"ShoppingList.submenu.title" = "Fiche";
"clip.export.title" = "Merci de patienter, le clip est en cours de création...";
"clip.export.error" = "Erreur lors de la création du clip";
"clip.export.retry" = "Réessayer";
"clip-image-select" = "Sélectionner...";
"clip-trim-start %@" = "Début: %@";
"clip-trim-end %@" = "Fin: %@";
"clip-save" = "Sauvegarder";
"clip-speed" = "Vitesse";
"clip-family-font" = "Police";
"clip-format" = "Format";
"clip-layers" = "Éléments";
"clip-image" = "Image";
"clip-add-image" = "Ajouter une image";
"clip-edit-image" = "Modification de l'image";
"clip-progress" = "Barre de progression";
"clip-add-progress" = "Ajouter une barre de progression";
"clip-edit-progress" = "Édition de la barre de progression";
"clip-title" = "Titre";
"clip-add-title" = "Ajouter un titre";
"clip-edit-title" = "Édition du titre";
"clip-final-duration" = "Durée finale";
"clip-reset-speed" = "Vitesse normale";
"clip-layer %lld" = "ÉLÉMENTS (%lld)";
"clip-add-layer" = "Ajouter un élément";
"clip-no-layer" = "Aucun élément";
"clip-tap-edit" = "TEXTE (TOUCHER POUR ÉDITER)";
"clip-font" = "POLICE";
"clip-background" = "FOND";
"clip-position" = "POSITION";
"clip-adjust" = "Ajuster";
"clip-add" = "Ajouter";
"clip-apply" = "Appliquer";
"clip-create" = "Créer le clip";
"clip-framing" = "Cadrage";
"clip-zoom" = "Zoom";
"clip-background-color" = "Couleur de fond";
"clip-layer-edit" = "Éditer";
"clip-layer-delete" = "Supprimer";
"clip-image-size" = "Taille";
"clip-dimension" = "Dimension";
"clip-height" = "Hauteur";
"clip-font-family" = "Famille";
"clip-font-color" = "Couleur";
"clip-font-alignment" = "Alignement";
"clip-font-size" = "Taille";
"clip-position-horizontal" = "Horizontal";
"clip-position-vertical" = "Vertical";
"clip-position-spacing" = "Espacement";
"clip-color" = "Couleur";
"clip-speed-info %lf" = "%.1lfx";
"clip-caption-title" = "Sous-titres";
"clip-caption-auto-title" = "Sous-titrage automatique";
"clip-caption-auto-subtitle" = "Nous utilisons la reconnaissance vocale pour générer les sous-titres automatiquement. La reconnaissance vocale est hors ligne, et vos données restent sur votre téléphone.";
"clip-caption-auto-no-language" = "Aucune langue disponible.";
"clip-caption-auto-language-subtitle" = "Merci de sélectionner la langue de la vidéo pour pouvoir générer les sous-titres correctement.";
"clip-caption-ask-permission-detail" = "Pour démarrer le sous-titrage automatique, vous avez besoin d'autoriser l'accès à la reconnaissance vocale. Merci de cliquer sur \"Démarrer\" puis de donner votre permission.";
"clip-caption-ask-permission" = "Démarrer (Permissions)";
"clip-caption-permission-denied" = "Ouvrir Réglages";
"clip-caption-permission-denied-detail" = "L'accès à la reconnaissance vocale a été refusé. Vous pouvez ouvrir les Réglages et autoriser la reconnaissance vocale.";
"clip-caption-adjust-warning" = "Il y a des sous-titres associés à cette vidéo. Si vous coupez des parties de la vidéo, les sous-titres seront effacés.";
"clip-caption-auto-error" = "Oops! Il y a eu une erreur lors de la reconnaissance vocale.";
"clip-caption-auto-error-nocaption" = "Oops! Aucun sous-titre n'a été detecté.";
"clip-caption-auto %lld" = "%lld sous-titre(s) générés";
"clip-caption-auto-analyze" = "Début de la reconnaissance vocale...";
"clip-caption-auto-success" = "L'analyse s'est effectuée avec succès.";
"clip-caption-auto-cancel" = "Annulation en cours...";
"clip-caption-auto-start" = "Démarrer la reconnaisance vocale";
"transcript-generate" = "Generate";
"clip-caption-auto-waiting" = "Merci de patienter, la reconnaissance vocale est en cours. Ne pas fermer l'application.";
"clip-caption-auto-manual" = "Ajouter un sous-titre manuellement";
"clip-caption-use-existing" = "Use Existing Transcript";
"clip-caption-home-info" = "Faire glisser la timeline et appuyer sur \"+\" pour ajouter un sous-titre à cette position.";
"clip-caption-edit-title" = "Édition";
"clip-caption-edit-info" = "Écrivez votre texte ici";
"clip-caption-style-title" = "Style";
"clip-caption-fake" = "Vos sous-titres apparaîtront ici";
"clip-caption-srt-mode" = "Édition SRT";
"clip-caption-speech-recognition" = "Démarrer la reconnaissance vocale";
"clip-caption-select-language" = "Merci de chosir une langue";
"clip-caption-srt-import" = "Importer";
"clip-caption-srt-export" = "Exporter";
"AccountInfos.name" = "Name";
"AccountInfos.email" = "Email";
"AccountInfos.expiration-date.days-counting.%d" = "Expire dans %d jours";
"AccountInfos.expiration-date.expired.title" = "Expiré";
"AccountInfos.subscription-active.title" = "Abonnement actif";
"AccountInfos.subscription-canceled.title" = "Abonnement annulé";
"AccountInfos.subscription-error.title" = "Action requise";
"AccountInfos.subscription-error.message" = "Oops! Une erreur s'est produite concernant le paiement. Merci de vous connecter au Dashboard pour résoudre le problème et utiliser Switcher sans interruption.";
"AccountProductActive.expiration-date.title" = "Expire le";
"AccountProductActive.renewable-date.title" = "Renouvelé le";
"SubscriptionTableViewController.active-plan.title" = "Abonnement actif";
"SubscriptionTableViewController.available-plans.title" = "Abonnements disponibles";
"BrandProfileSubtableController.name" = "Brand Profile";
"BrandProfileSubtableController.action.apply" = "Appliquer";
"BrandProfileSubtableController.action.configure" = "Configurer";
"PaletteSubtableController.name" = "Thème";
"PaletteSubtableController.custom" = "Personnalisé";
"PaletteSubEditor.title" = "Thème";
"PaletteSubEditor.section.brand-profile" = "Brand Profile";
"PaletteSubEditor.section.generic" = "Générique";
"PaletteSubEditor.action.shuffle" = "Mélanger les couleurs sélectionnées";
"target-url.ack.title" = "Avis de configuration";
"target-url.ack.dest" = "Switcher Studio est prêt pour la diffusion vers la destination suivante:";
"target-url.ack.tip.no-user" = "Après avoir fermé cet avis, identifiez-vous, puis sélectionnez \"Mode régie\" et appuyez sur le bouton 🔴 pour démarrer le live.";
"target-url.ack.tip.user" = "Après avoir fermé cet avis, sélectionnez \"Mode régie\" et appuyez sur le bouton 🔴 pour démarrer le live.";
"Settings.Guide.SafeArea" = "Marges de sécurité";
"welcome-d3.button.manage-subscription" = "Manage Subscription";
"welcome-d3.button.delete-account" = "Supprimer mon compte";
"tutorials.getstarted" = "Tutoriels principaux";
"tutorials.title" = "Afficher les tutoriels principaux";
"tutorials.all.information" = "Ces vidéos vous aideront à démarrer avec certaines des fonctionnalités les plus populaires de Switcher Studio. Pour plus de tutoriels, visitez notre centre d'aide.";
"tutorials.all.title" = "Tutoriels Vidéo";
"filter-list.vfilter-cross-zoom.title" = "Zoom enchaîné";
"filter-list.vfilter-cover.title" = "Couvrir";
"filter-list.vfilter-push.title" = "Pousser";
"filter-list.vfilter-split.title" = "Fractionnement";
"filter-list.vfilter-iris.title" = "Iris";
"filter-list.vfilter-star_wipe.title" = "Balayage étoile";
"filter-list.vfilter-swipe.title" = "Swipe";
"filter-list.vfilter-fade.title" = "Fondu";
"filter-list.vfilter-flicker.title" = "Clignotement";
"filter-list.vfilter-shatter.title" = "Explosion";
"filter-list.vfilter-rolling.title" = "Roulement";
"filter-list.vfilter-whip_pan.title" = "Panoramique horizontal";
"filter-list.vfilter-stinger.title" = "Stinger";
"vfilter-prop.fade_out_duration.title" = "Durée de la disparition";
"vfilter-prop.wait_duration.title" = "Durée de l'attente";
"vfilter-prop.fade_in_duration.title" = "Durée de l'apparition";
"vfilter-prop.fade_in_title" = "Fade In";
"vfilter-prop.fade_out_title" = "Fade Out";
"vfilter-prop.fade_setting.title" = "Audio Fading";
"vfilter-prop.logo.title" = "Logo";
"vfilter-prop.background_color.title" = "Couleur de fond";
"vfilter-prop.external_border_color.title" = "Couleur du bord extérieur";
"vfilter-prop.internal_border_color.title" = "Couleur du bord intérieur";
"vfilter-prop.scale_flap.title" = "Échelle du volet";
"vfilter-prop.scale_inset.title" = "Échelle de l'insert";
"vfilter-prop.margin_flap.title" = "Marge du volet";
"vfilter-prop.margin_inset.title" = "Marge de l'insert";
"vfilter-prop.invert_flap.title" = "Invert Flap Position";
"vfilter-prop.zoom_max.title" = "Zoom";
"vfilter-prop.uncover.title" = "Découvrir";
"vfilter-prop.reverse.title" = "Inverser";
"PlaybackSettingsSubEditorView.title" = "Playback Settings";
"PlaybackSettingsSubEditorView.play_once" = "Play Once";
"PlaybackSettingsSubEditorView.infinite" = "Infinite Loop";
"PlaybackSettingsSubEditorView.loop_count" = "Loop Count";
"PlaybackSettingsSubEditorView.loop_duration" = "Display Duration";
"PlaybackSettingsSubEditorView.repeat %d" = "Repeat %lldx";
"PlaybackSettingsSubEditorView.play_for %@" = "Play for %@";
"PlaybackSettingsSubEditorView.times" = "Times";
"AssetGridViewController.photoLibrary.manage.title" = "Gérer";
"AssetGridViewController.photoLibrary.limitedPhotos.title" = "Vous n'avez donné à Switcher l'accès qu'à un nombre limité de photos";
"asset.collection.no.asset" = "Aucun élément";
"asset.collection.reorder" = "Glisser pour réorganiser";
"asset.collection.hidden.sing" = "Vous avez 1 groupe masqué.";
"asset.collection.hidden %lld" = "Vous avez %lld groupes masqués.";
"asset.collection.manage" = "Gérer";
"asset.collection.disconnect" = "Déconnecter";
"asset.collection.delete.confirm.title" = "Êtes-vous sûr ?";
"asset.collection.delete.confirm.message.plur" = "Vous êtes sur le point de supprimer %d éléments.";
"asset.collection.delete.confirm.message.sing" = "Vous êtes sur le point de supprimer un élément";
"asset.collection.delete.confirm.button" = "Oui, supprimer l'élément";
"asset.collection.delete" = "Supprimer";
"export.extension.title" = "Use in Switcher";
"export.extension.import.as.assets.title" = "Import as Assets";
"export.extension.import.as.assets.subtitle" = "Add images or videos to use as full-screen or overlaid assets in your productions.";
"export.extension.import.as.assets.button" = "Import";
"export.extension.upload.to.library.title" = "Upload to Video Library";
"export.extension.upload.to.library.subtitle" = "Store, edit video details, and manage your content in the Cloud Video Library.";
"export.extension.upload.to.library.button" = "Upload";
"export.upload.to.library.success" = "Videos added to your Uploads";
"export.extension.send.image.to.switcher %lld" = "Importer %lld images dans Switcher";
"export.extension.send.image.to.switcher" = "Importer l'image dans Switcher";
"export.extension.send.video.to.switcher %lld" = "Importer %lld vidéos dans Switcher";
"export.extension.send.video.to.switcher" = "Importer la vidéo dans Switcher";
"export.extension.orientation" = "Orientation";
"export.extension.thumbnail.selection" = "Sélection de la vignette";
"export.extension.thumbnail.tag" = "Tag sur vignette";
"export.extension.video.audio-only" = "Audio uniquement";
"export.image.import.success %lld" = "%lld images importées avec succès";
"export.image.import.success %lld %lld" = "%lld / %lld images importées avec succès";
"export.image.import.success" = "Image importée avec succès";
"export.video.import.success %lld" = "%lld vidéos importées avec succès";
"export.video.import.success %lld %lld" = "%lld / %lld vidéos importées avec succès";
"export.video.import.success" = "Importation réussie";
"export.mixed-content.warning" = "Petit problème...\n\nPour importer un lot de médias dans Switcher, assurez-vous qu'ils soient tous du même type.";
"export.multiple-videos.warning" = "We have a little problem...\n\nIn order to upload to the Video Library, please ensure you've selected only one video.\nUploading multiple videos simultaneously is unsupported at this time.";
"tag.golive" = "Démarrez la production pour ajouter un marque-temps.";
"tag.notag" = "Aucun marque-temps";
"tag.manual" = "Gestion";
"tag.automatic" = "Automatique";
"tag.add.manual.tag" = "Ajouter un marque-temps manuel";
"tag.manage" = "Gérer";
"tag.count %lld" = "Tous les maque-temps (%lld)";
"tag.toast.message" = "Marque-temps ajouté";
"tag.manual.description" = "Marque-temps manuel (%@)";
"tag.jump.to" = "Aller à...";
"summary.prod.title" = "Votre vidéo est prête !";
"summary.prod.share.link" = "Partager le lien de la diffusion";
"summary.prod.export.record" = "Exporter l'enregistrement";
"summary.prod.create.clip" = "Créer un clip";
"summary.prod.director.mode" = "Fichiers du Director Mode";
"summary.prod.leave.studio" = "Quitter la régie";
"summary.prod.loading" = "Patientez, traitement de votre vidéo en cours.";
"summary.clip.title" = "Votre clip est prêt !";
"summary.clip.loading" = "Patientez, traitement du clip en cours.";
"summary.tooltip.timestamp" = "Utilisez vos marque-temps pour retrouver rapidement l'endroit où extraire votre clip.";
"summary.tooltip.videolibrary" = "NEW: Upload your recording to your Video Library, and then visit the Dashboard to add it to your Switcher Player.";
"clip.tooltip.timestamp" = "Trouvez ici vos marque-temps";
"clip.tooltip.timestamps %lld" = "Trouvez ici vos %lld marque-temps";
"exporter.title" = "Partage";
"exporter.more.options" = "Autres options de partage...";
"exporter.poto.library" = "Enregistrer dans Photos";
"exporter.files" = "Enregistrer dans Fichiers";
"exporter.city.producer" = "Envoyer vers CTpro";
"ProdSummaryViewController.state1.cancel.confirmation.title" = "Vous avez presque terminé! Êtes vous sûr de vouloir annuler?\n Votre vidéo est en cours d'upload.\n Annuler maintenant va couper votre flux vidéo.";
"ProdSummaryViewController.state1.cancel.button.confirmation.title" = "Oui, annuler";
"ProdSummary.videolibrary.info" = "Uploaded to your Video Library";
"ProdSummary.localrecording.info" = "Saved in Local Recordings";
"ProdSummary.practice.title" = "Practice Livestream Complete";
"ProdSummary.practice.text" = "A recording can be privately watched via your online Dashboard at **switcherstudio.com** for the next 14 days.";
"ProdSummary.practice.go.home" = "Go Back Home";
"clip.trim.start" = "Début du clip";
"clip.trim.end" = "Fin du clip";
"clip.caption.trim.start" = "Début du sous-titre";
"clip.caption.trim.end" = "Fin du sous-titre";
"auto-dismiss.property.enable.title" = "Enlever automatiquement";
"auto-dismiss.property.delay.title" = "Durée d'affichage";
"auto-dismiss.property.delay.none" = "Désactivée";
"auto-dismiss.property.tip" = "La fonction \"Enlever automatiquement\" permet de supprimer de l’écran une fois terminés les minuteurs, les vidéos préenregistrées et les textes animés qui ne tournent pas en boucle.";
"auto-dismiss.delay-editor.title" = "Durée d'affichage";
"auto-dismiss.delay-editor.enable.title" = "Activer";
"auto-dismiss.delay-editor.delay.header" = "Durée";
"auto-dismiss.delay-editor.delay.tip" = "La durée d'affichage détermine combien de temps restent à l'écran les images, les bannières, les titres, les logos, les références aux réseaux sociaux et donations et les textes animés en boucle.";
"sharing-choice.button.share-seemo.start" = "Partager SeeMo";
"sharing-choice.button.share-seemo.subtitle" = "Utiliser la caméra HDMI comme source dans votre production.";
"sharing-choice.button.share-ext-camera.start" = "Share USB Video Source";
"sharing-choice.button.share-ext-camera.subtitle" = "Use the USB Webcam or Capture Device in your production.";
"seemo-settings.title" = "Accsoon SeeMo Settings";
"seemo-settings.latency" = "Latence de la caméra HDMI";
"seemo.settings.footer" = "Ce réglage ne s'applique qu'à la caméra connectée à ce dispositif via Accsoon SeeMo.";
"seemo.addsource.hdmi.title" = "Caméra / Source HDMI via Accsoon SeeMo";
"seemo.addsource.helper.description" = "Utilisez Accsoon SeeMo pour connecter des appareils photo reflex numériques (DSLR) et d'autres sources HDMI à Switcher.\r\r\rUne fois votre source connectée à cet appareil via Accsoon SeeMo, elle apparaîtra dans l'onglet Entrées de la même façon que la caméra intégrée.\r\r\rLes sources Accsoon SeeMo peuvent également être utilisées et contrôlées à distance. Pour faire cela, connectez votre DSLR ou autre source HDMI à un iPhone ou iPad auxiliaire sur lequel tourne Switcher, et prenez-en le contrôle à distance depuis la régie via Wi-Fi.";
"MyRecordingTableViewController.edit.title" = "Sélectionner";
"MyRecordingTableViewController.edit.rename.title" = "Renommer";
"MyRecordingTableViewController.edit.trash.confirm.title" = "Supprimer l'enregistrement";
"MyRecordingTableViewController.edit.trash.confirm-multi.title" = "Supprimer %d enregistrements";
"MyRecordingTableViewController.edit.rename.alert.msg" = "Impossible de renommer";
"MyRecordingTableViewController.title" = "Mes enregistrements";
"MyRecordingTableViewController.edit.select-all.title" = "Tout sélectionner";
"MyRecordingTableViewController.edit.deselect-all.title" = "Tout désélectionner";
"MyRecording.Delete.title.sing" = "Delete Video?";
"MyRecording.Delete.title.plur %lld" = "Delete %lld Videos?";
"MyRecording.Delete.body" = "Deleting cannot be undone. Are your sure you want to continue?";
"MyRecordings.no-asset.title" = "Pas d'enregistrement à afficher";
"MyRecordings.no-asset.subtitle" = "Faites un live ou enregistrez une vidéo. Une fois terminé, vous retrouverez votre enregistrement ici. Vous pouvez ensuite gérer, éditer et partager vos enregistrements depuis cet écran.";
"MyRecordings.no-asset.hint.guest" = "Démarrer Maintenant";
"MyRecordings.no-asset.hint.loggedIn" = "Créez votre première video";
"MyRecordings.select.title" = "Sélectionner les enregistrements";
"MyRecordings.select.sing" = "1 enregistrement sélectionné";
"MyRecordings.select.plur %lld" = "%lld enregistrements sélectionnés";
"MyRecordingTableViewCell.delete" = "Supprimer";
"MyRecordings.sort.title" = "Trier par";
"MyRecordingTableViewController.edit.tools.title" = "Outils";
"MyRecordings.sort.creation.date" = "Date de création";
"MyRecordings.sort.modification.date" = "Date de modification";
"MyRecordings.sort.name" = "Nom";
"MyRecordings.export.live.output" = "Exporter l'enregistrement live";
"MyRecordings.delete.live.output" = "Supprimer l'enregistrement live";
"MyRecordings.export.clip" = "Exporter le clip";
"MyRecordings.export.video" = "Exporter la vidéo";
"MyRecordings.export.audio" = "Exporter l'audio";
"MyRecordings.clips.creation.info" = "Créez des clips vidéo à partir de votre propre enregistrement.";
"MyRecordings.clip.source %@" = "Source : %@";
"MyRecordings.clip.create.menu" = "Créer un clip à partir de :";
"MyRecordings.clip.create.menu.others" = "Angles";
"MyRecordings.clip.create" = "Créer un clip";
"MyRecordings.select.clip.title" = "Sélectionnez les clips";
"MyRecordings.select.clip.sing" = "1 clip sélectionné";
"MyRecordings.select.clip.plur %lld" = "%lld clips sélectionnés";
"MyRecordings.banner.text" = "Now you can upload directly to the Dashboard video library.";
"MyRecordings.banner.learn-more" = "Learn More...";
"MyRecordings.banner.webview.title" = "Upload to Library";
"MyRecordings.last-uploaded" = "You uploaded this video %@";
"MyRecordings.transcript" = "Transcript";
"MyRecordings.transcript.edited" = "Edited";
"MyRecordings.transcript.generated" = "Generated automatically";
"MyRecordings.transcript.empty" = "No transcript available for this video.";
"MyRecordings.transcript.menu.copy" = "Copy Text";
"MyRecordings.transcript.menu.edit" = "Edit";
"MyRecordings.transcript.menu.export" = "Export .SRT";
"MyRecordings.transcript.menu.import" = "Import .SRT";
"MyRecordings.transcript.menu.delete" = "Delete Transcript";
"MyRecordings.transcript.menu.delete.confirmation.title" = "Delete Transcript?";
"MyRecordings.transcript.menu.delete.confirmation.subtitle" = "Note: You can generate or import a new transcript at any time.";
"MyRecordings.transcript.menu.delete.cloud.confirmation.subtitle" = "Note: You can import a new transcript at any time.";
"MyRecordings.transcript.menu.delete.progress.title" = "Please wait...";
"MyRecordings.transcript.menu.delete.progress.subtitle" = "Don’t close the app or put it in the background.";
"MyRecordings.transcript.edit.title" = "Edit Transcript";
"MyRecordings.transcript.tooltip" = "On your device, go to **Settings** > **Accessibility** > **Voice Control** > **Language** to add new languages.";
"MyRecordingTableViewCell.mode.live" = "Live";
"MyRecordingTableViewCell.mode.director-mode-recording" = "DM Recording";
"MyRecordingTableViewCell.mode.director-mode-composition" = "DM Composition";
"MyRecordingTableViewCell.mode.channel" = "Channel";
"MyRecordingTableViewCell.mode.channels" = "Channels";
"MyRecordingTableViewCell.mode.clip" = "Clip";
"MyRecordingTableViewCell.mode.clips" = "Clips";
"MyRecordingTableViewCell.rename" = "Renommer";
"MyRecordingModeTableViewController.mode.live" = "Live Recording";
"MyRecordingModeTableViewController.mode.director-mode-composition" = "Director Mode Composition";
"MyRecordingModeTableViewController.mode.director-mode-recording" = "Director Mode Recording";
"MyRecordingModeTableViewController.mode.clips" = "Clips";
"asset.cloud.error" = "An unexpected error occurred. Please try again.";
"asset.cloud.emptystate.title" = "Nothing to see here yet";
"asset.cloud.emptystate.description" = "To upload assets to the Switcher Cloud: go to the sources panel, tap Select, choose your desired assets, and then tap the cloud icon.";
"asset.upload.conflict.title" = "Resolve Conflict";
"asset.download.conflict.download" = "Download & Replace";
"asset.download.conflict.keep" = "Keep Local Version";
"asset.download.conflict.exist" = "This file already exists locally";
"mediagrid.empty.title" = "Aucune image";
"prod-flow.photo.button.more.short" = "Autres";
"prod-flow.photo.button.more.long" = "Autres options...";
"social-platform-icons-donation" = "Plateformes de financement";
"social-platform-icons-social" = "Réseaux sociaux";
"social-platform-icons-title" = "Icônes de plateformes";
"asset.upload.conflict.download" = "Upload & Replace";
"asset.upload.conflict.keep" = "Keep Cloud Version";
"asset.upload.conflict.exist" = "This file already exists on the Switcher Cloud";
"asset.upload.progress.title" = "One moment — your assets are uploading.";
"asset.upload.success.title" = "Asset uploaded";
"asset.download.success.title" = "Asset downloaded";
"asset.download.group.success.title" = "Group downloaded";
"asset.upload.cancel" = "Cancel Upload";
"asset.upload.delete.local" = "Delete Local Assets";
"asset.upload.keep.local" = "Keep Local Assets";
"asset.upload.error.title" = "An error occurred";
"asset.conflict.sing.title" = "1 conflicted file";
"asset.conflict.plur.title %lld" = "%lld conflicted files";
"asset.upload.cloud" = "Cloud";
"asset.upload.local" = "Local";
"asset.upload.delete.sing.information" = "Asset deleted";
"asset.upload.delete.plur.information" = "%d assets deleted";
"asset.download.delete.cloud.selection" = "Select Assets";
"asset.download.delete.cloud.selected.plur %lld" = "%lld Assets Selected";
"asset.download.delete.cloud.selected.sing" = "1 Asset Selected";
"asset.download.delete.cloud.confirm" = "You’re about to delete permanently asset(s) from the Switcher Cloud. Are you sure you want to continue?";
"asset.download.delete.cloud.title" = "One moment — your Switcher Cloud assets are being deleted.";
"global.form.fieldrequired %@" = "%@ is required";
"global.alertview.retry" = "Retry";
"global.alertview.waiting" = "Please do not lock screen or leave the app.";
"global.alertview.error.title" = "Oops";
"videolibrary.title" = "Upload Video";
"videolibrary.title.plural" = "Upload Videos";
"videolibrary.videotoolong.title" = "Video is too big";
"videolibrary.videotoolong.description" = "Maximum video duration is 30 GB";
"videolibrary.videonotfound.title" = "Video not found";
"videolibrary.videonotfound.description" = "Cannot export the file to the video library.";
"videolibrary.badformat.title" = "Format not supported";
"videolibrary.badformat.description" = "Cannot export the file to the video library. The encoding of the video is not supported (supported encoding: h264 and h265)";
"videolibrary.upload.progress.title" = "One moment — your video is uploading";
"videolibrary.form.file" = "File:";
"videolibrary.form.name" = "Name";
"videolibrary.form.name.placeholder" = "Give your video a name";
"videolibrary.form.description" = "Description";
"videolibrary.form.description.placeholder" = "Let viewers know what your video is about (optional).";
"videolibrary.form.visibility" = "Visibility";
"videolibrary.form.showInCatalog" = "Show In Video Catalog";
"videolibrary.form.collections" = "Collections";
"videolibrary.form.tags" = "Tags";
"videolibrary.form.transcript.label" = "Transcript";
"videolibrary.form.transcript.choose.label" = "Choose an .SRT file on your device.";
"videolibrary.form.transcript.replace.label" = "Replace existing transcript with an .SRT file.";
"videolibrary.form.include.transcript" = "Include existing video transcript.";
"videolibrary.form.transcript.menu.replace.label" = "Replace File";
"videolibrary.form.upload.button" = "Upload";
"videolibrary.form.explaination" = "Want this video on your website? Visit the Switcher Dashboard after your upload completes and add it to a Switcher Player playlist.";
"videolibrary.multiExport.error.description.sing" = "A video has been removed from your selection (unsupported encoding).";
"videolibrary.multiExport.error.description.plur" = "Some videos have been removed from your selection (size or encoding not supported).";
"videolibrary.uploads.toast.completed" = "Upload complete.\nVideo is now available in your library.";
"videolibrary.uploads.toast.failed" = "Upload failed. Please try again.";
"videolibrary.uploads.paused" = "Paused";
"videolibrary.uploads.failed" = "Upload Failed";
"videolibrary.uploads.success" = "Uploaded";
"menu.section.profile" = "PROFIL";
"menu.section.support" = "AIDE";
"menu.support.tutorials" = "Video Tutorials";
"menu.section.more" = "ENCORE";
"menu.logout.confirm" = "Vous voulez vous déconnecter?";
"menu.manage.beta.features" = "Beta Features";
"menu.manage.beta.features.disclaimer" = "The following experimental features are in beta stage, meaning they could cause unexpected behavior when in use. You may preview these features and provide feedback, but generally we do not provide support for beta features.";
"menu.manage.beta.features.empty.title" = "Welcome to Beta Features";
"menu.manage.beta.features.empty.content" = "There are no beta features currently available. Check back soon for future updates.";
"menu.manage.beta.features.get.features.error.title" = "Unable to load the page";
"menu.manage.beta.features.get.features.error.detail" = "Your internet connection appears to be offline. Check your network settings and try again.";
"menu.manage.beta.features.get.features.error.retry" = "Retry";
"usermain.subscribe.banner.title" = "You don't have an active subscription right now.";
"usermain.subscribe.banner.button" = "Subscribe";
"usermain.storageFull.banner.title %lld %lld" = "**Attention** – You have reached your overall storage limit of video uploads (%lld of %lld).";
"usermain.storageWarning.banner.title %lld %lld" = "**Attention** – You are approaching your overall storage limit of video uploads (%lld of %lld).";
"menu.subscription.multiPlan.title" = "Choose a plan";
"menu.subscription.trial.title" = "Your first 14 days for free";
"menu.subscription.trial.subtitle" = "Pay nothing today and cancel anytime";
"menu.subscription.trial.subscribe" = "Start 14-Day Trial";
"menu.subscription.trial.subscribeTo %@" = "Subscribe To %@";
"menu.subscription.trial.comment" = "Billing begins when your free trial ends. Cancel before trial ends and you won’t be charged. Subscription automatically renews monthly until you cancel. Cancel anytime in your Apple account.";
"menu.subscription.title" = "Start creating with Switcher";
"menu.subscription.subtitle" = "No commitment. Cancel anytime";
"menu.subscription.price %@" = "Renews automatically at %@";
"menu.subscription.priceMonthly %@" = "%@/month";
"menu.subscription.perMonth" = "/month";
"menu.subscription.error" = "Something went wrong.";
"menu.subscription.subscribe" = "Subscribe Now";
"menu.subscription.subscribeTo %@" = "Subscribe To %@";
"menu.subscription.comment" = "Subscription automatically renews monthly until you cancel. Cancel anytime in your Apple account.";
"menu.subscription.feature.multicam" = "Multicamera, multisource video";
"menu.subscription.feature.multistreaming" = "Built-in Multistreaming";
"menu.subscription.feature.graphics" = "Built-in graphics templates";
"menu.subscription.feature.guests" = "Invite up to five remote guests";
"menu.subscription.termsofservice" = "Terms of Service";
"menu.subscription.privacypolicy" = "Privacy Policy";
"menu.subscription.restore" = "Restore Purchase";
"menu.subscription.current.plan" = "My plan";
"menu.subscription.available.plans" = "Available plan";
"menu.subscription.extra.informations" = "Please log in to **switcherstudio.com** to manage your subscription.";
"menu.subscription.start.trial" = "Start Free Trial";
"menu.subscription.manage" = "Manage Subscription";
"menu.subscription.cancel" = "Cancel Subscription";
"menu.subscription.skip.confirmation.title" = "Your account is created, but you won't have full access to all the great features until you subscribe.";
"menu.subscription.skip.confirmation.button" = "Finish Later";
"menu.subscription.skip.confirmation.cancel" = "Resume";
"videolibrary.selector.localrecording" = "Videos created with Switcher Studio on this device";
"videolibrary.selector.cloudvideos" = "Cloud videos that can be added to a Switcher Player";
"videolibrary.processing" = "Your video is processing...";
"videolibrary.login.title" = "Log in to see your videos";
"videolibrary.login.subtitle" = "Once you log in, you’ll find all your videos here.";
"videolibrary.video.error.description" = "Processing error";
"videolibrary.error.title" = "Unable to load your videos";
"rtmp.channel.error.title" = "Unable to load your channels";
"videolibrary.error.subtitle" = "Your Internet connection appears to be offline. Check your network settings and try again.";
"videolibrary.error.button" = "Try again";
"videolibrary.empty.title" = "Take Control of Your Content";
"videolibrary.empty.subtitle" = "Safely store all your videos on the cloud for access to every Switcher feature across all of your devices.";
"videolibrary.empty.hint" = "Upload or create your first video";
"videolibrary.empty.search.title" = "No videos found";
"videolibrary.empty.search.subtitle" = "We couldn’t find any videos that match your criteria.";
"videolibrary.empty.clearfilters" = "Clear Filters";
"videolibrary.download.button" = "Download";
"videolibrary.download.success" = "Video Saved to Camera Roll";
"videolibrary.download.error" = "Cannot Save Video to Camera Roll";
"videolibrary.share.button" = "Share Link";
"videolibrary.select.title" = "Select Videos";
"videolibrary.select.sing" = "1 Video Selected";
"videolibrary.select.plur %d" = "%d Videos Selected";
"videolibrary.sort.nameAZ" = "Title A-Z";
"videolibrary.sort.nameZA" = "Title Z-A";
"videolibrary.sort.newest" = "Newest First";
"videolibrary.sort.oldest" = "Oldest First";
"videolibrary.sort.leastviews" = "Least Views";
"videolibrary.sort.mostviews" = "Most Views";
"videolibrary.default.label" = "(default)";
"videolibrary.delete.cloud.progress" = "Deleting...";
"videolibrary.delete.cloud.error.sing" = "Unable to Delete Video";
"videolibrary.delete.cloud.error.plur" = "Unable to Delete Videos";
"videolibrary.delete.success.sing" = "Video deleted";
"videolibrary.delete.success.plur" = "Videos deleted";
"videolibrary.tooltip.title" = "Change Library";
"videolibrary.tooltip.description" = "Switch between your Video Library and Local Recordings here.";
"videolibrary.details.edit" = "Edit Details";
"videolibrary.details.edit.event" = "Edit Livestream Details";
"videolibrary.details.edit.event.save.button" = "Save Changes";
"videolibrary.details.thumbnail" = "Thumbnail";
"videolibrary.thumbnail.rec.size" = "Recommended size is 1920 x 1080 pixels.";
"videolibrary.details.thumbnail.frame" = "Select Video Frame";
"videolibrary.details.edit.confirm.cancel" = "Are you sure you want to discard your changes?";
"videolibrary.details.edit.confirm.cancel.discard" = "Discard Changes";
"videolibrary.details.edit.confirm.cancel.keepediting" = "Keep Editing";
"videolibrary.details.edit.saving" = "Saving changes";
"videolibrary.details.nodescription" = "No Description";
"videolibrary.details.collectionsandpasses" = "Collections & Passes";
"videolibrary.details.collections.empty" = "Not used in any collections yet";
"videolibrary.details.players.plur %lld" = "%lld Players";
"videolibrary.details.gating" = "Gating Options";
"videolibrary.details.pass.empty" = "No one-time passes yet";
"videolibrary.details.pass.sing" = "1 One-Time Pass";
"videolibrary.details.pass.plur %lld" = "%lld One-Time Passes";
"videolibrary.details.pass.password" = "Password";
"videolibrary.details.password.access" = "Enable Password Access";
"videolibrary.details.password.explain" = "Viewers who enter password will bypass email entry and purchase.";
"videolibrary.details.password.create" = "Create a password";
"videolibrary.details.email.access" = "Require Email Address to View";
"videolibrary.details.email.explain" = "Automatically collected with purchase.";
"videolibrary.details.analytics.tip" = "Data does not include plays from social platforms.";
"videolibrary.details.about" = "Information";
"videolibrary.details.about.size" = "Size";
"videolibrary.details.about.size.unknown" = "Unknown";
"videolibrary.details.about.create" = "Created";
"videolibrary.details.about.dimensions" = "Dimensions";
"videolibrary.details.about.dimensions.processing" = "Processing...";
"videolibrary.details.about.links" = "Interactive Links";
"videolibrary.details.analytics" = "Analytics";
"videolibrary.details.analytics.all_time" = "Lifetime views";
"videolibrary.details.analytics.seven_days" = "Last 7 days";
"videolibrary.details.premiering" = "Premiering";
"videolibrary.details.scheduled" = "Scheduled";
"videolibrary.details.unpublished" = "This video is unpublished";
"videolibrary.details.unpublished.short" = "Unpublished";
"videolibrary.details.published.short" = "Published";
"videolibrary.details.catalog.link.title" = "Want this video on your website?";
"videolibrary.details.catalog.link.description" = "Learn how to make your videos available on your website by using Video Catalog and Collections.";
"videolibrary.setvisibility.title" = "Video Visibility";
"videolibrary.visibility.published.title" = "Publish";
"videolibrary.visibility.published.subtitle" = "Your audience can watch this video.";
"videolibrary.visibility.scheduled.title" = "Schedule Premiere";
"videolibrary.visibility.scheduled.subtitle" = "Publish your video at a specific date and time.";
"videolibrary.visibility.scheduled.when" = "When";
"videolibrary.visibility.unpublished.title" = "Unpublished";
"videolibrary.visibility.unpublished.subtitle" = "Only you can watch this video inside your Switcher account.";
"videolibrary.visibility.upload.published.title" = "Publish Now";
"videolibrary.visibility.upload.scheduled.title" = "Schedule Premiere";
"videolibrary.visibility.upload.unpublished.title" = "Save as Unpublished";
"videolibrary.visibility.unpublished.alert.title" = "Attention";
"videolibrary.visibility.unpublished.alert.message" = "You are unpublishing a video that some of your audience may be paying for access to. If you continue, they will no longer be able to watch this content unless you republish.";
"videolibrary.upload.library" = "Camera Roll";
"videolibrary.upload.complete" = "Upload Complete";
"videolibrary.upload.files" = "Choose From Files";
"videolibrary.upload.title" = "Upload to Video Library";
"videolibrary.gorecordings.title" = "Go to Local Recordings";
"videolibrary.finalizevideo.title" = "Finalize Video";
"videolibrary.schedule.next.event.title" = "Schedule Next Livestream";
"videolibrary.thumbnail.choose" = "Choose Image";
"videolibrary.thumbnail.frame" = "Video Frame";
"videolibrary.thumbnail.photo" = "Take Photo";
"videolibrary.thumbnail.frame.title" = "Select Video Frame";
"videolibrary.thumbnail.reset" = "Reset Default";
"videolibrary.filterby" = "Filters";
"videolibrary.tags" = "Tags";
"videolibrary.filterby.all" = "All";
"videolibrary.filterby.inplayer" = "In Collection";
"videolibrary.filterby.notinplayer" = "Not in Collection";
"videolibrary.filterby.gated" = "Gated";
"videolibrary.filterby.notgated" = "Not Gated";
"videolibrary.filterby.published" = "Published";
"videolibrary.filterby.unpublished" = "Unpublished";
"videolibrary.filterby.scheduled.for.premiere" = "Scheduled for Premiere";
"videolibrary.search.title" = "Search";
"videolibrary.upload.thumbnail.error" = "Failed to upload thumbnail. Please try again.";
"videolibrary.manage" = "Manage Video Library";
"videolibrary.warning.full.title" = "Attention";
"videolibrary.warning.full.description %lld %lld" = "You have reached your overall storage limit of video uploads (%lld of %lld). If you proceed with the current output selection for this livestream, **the oldest video in your Video Library will be automatically deleted.**";
"videolibrary.tags.emptystate.title" = "No tags";
"videolibrary.tags.emptystate.filter.description1" = "Tags allow you to group and filter your videos for easier content management.";
"videolibrary.tags.emptystate.filter.description2" = "You can create your first tag by editing the details of any video you've created or uploaded.";
"videolibrary.categories.emptystate.description" = "Start by creating one, it's a great way to organize and filter your videos.";
"videolibrary.tags.add.title" = "Add Tags";
"videolibrary.tags.add.new" = "New Tag";
"videolibrary.tags.edit.title" = "Edit Tags";
"videolibrary.tags.rename.title" = "Rename Tag";
"videolibrary.tags.delete" = "Delete Tag";
"videolibrary.tags.delete.title" = "Delete Tag?";
"videolibrary.tags.delete.msg %@" = "Are you sure you want to delete %@?";
"videolibrary.tags.title" = "Tag";
"videolibrary.tags.noconnection.title" = "Unable to Load Tags";
"videolibrary.tags.add.error.title" = "Unable to Create Tag";
"videolibrary.tags.add.error.subtitle" = "Something went wrong while creating your new tag.";
"videolibrary.tags.videocount_zero" = "No videos";
"videolibrary.tags.videocount_singular" = "1 video";
"videolibrary.tags.videocount_plural %lld" = "%lld videos";
"videolibrary.categories.add" = "Add";
"videolibrary.cloudvideo.view_singular" = "1 View";
"videolibrary.cloudvideo.view_plural %lld" = "%lld Views";
"videolibrary.loading.fail" = "Loading failed. **Tap to Retry**";
"videolibrary.loading.finished" = "No more videos to show.";
"stream.settings.advanced.title" = "Advanced Settings";
"stream.settings.advanced.subtitle" = "RTMP advanced settings only apply when using Custom RTMP mode. For Livestreaming, stream details will be used.";
"survey.onboarding.title" = "Tell Us About Yourself";
"survey.exit.title" = "Unsure about your Subscription?";
"survey.button.continue" = "Continue";
"survey.button.done" = "Done";
"survey.thankyou.title" = "Thank you!";
"survey.thankyou.text" = "We appreciate your time.\rYour responses will help shape the future of Switcher.";
"survey.thankyou.continue" = "Continue to the app";
"survey.page0.title" = "How would you describe yourself?";
"survey.page0.subtitle" = "Take a moment to help us get to know you.";
"survey.page0.option1" = "Business Owner";
"survey.page0.option2" = "Marketing Professional";
"survey.page0.option3" = "Influencer/Creator";
"survey.page0.option4" = "Hobbyist";
"survey.page0.option5" = "Other (please specify)";
"survey.page1.title" = "What brings you to Switcher?";
"survey.page1.subtitle" = "Select all that apply.";
"survey.page1.option1" = "Recording videos";
"survey.page1.option2" = "Livestreaming on social media";
"survey.page1.option3" = "Charging viewers to watch my content";
"survey.page1.option4" = "Embedding videos on my website";
"survey.page1.website_query" = "www.website.com (optional)";
"survey.page1.website_query_prompt" = "What's your website URL?";
"survey.page2.title" = "Do you have any existing videos to upload?";
"survey.page2.subtitle" = "And if yes, how many?";
"survey.page2.option1" = "No, I don't have any existing videos";
"survey.page2.option2" = "Fewer than 10 videos";
"survey.page2.option3" = "Between 10 and 50 videos";
"survey.page2.option4" = "Between 51 and 100 videos";
"survey.page2.option5" = "More than 100 videos";
"survey.page3.title" = "Where will your viewers watch your videos?";
"survey.page3.subtitle" = "Select all that apply.";
"survey.page3.option1" = "My Website";
"survey.page3.option2" = "Facebook";
"survey.page3.option3" = "YouTube";
"survey.page3.option4" = "Twitch";
"survey.page3.option5" = "TikTok";
"survey.page3.option6" = "Instagram";
"survey.page3.option7" = "Other (please specify)";
"survey.page3.where_query" = "Other (please specify)";
"survey.page4.title" = "How did you find out about Switcher?";
"survey.page4.subtitle" = "This is the last question and you're all set.";
"survey.page4.option1" = "Online Search";
"survey.page4.option2" = "App Store";
"survey.page4.option3" = "Social Media";
"survey.page4.option4" = "Digital Ad";
"survey.page4.option5" = "Personal Recommendation";
"survey.page4.option6" = "Blog Post";
"survey.page4.option7" = "Other (please specify)";
"ElementMainView.loop_count %d" = "Loop %dx";
"Eptz.Help" = "Once zoomed, drag left or right to pan and up or down to tilt.";
"Eptz.Help.Button" = "Tap To Dismiss";
"menu.message.expired_account" = "Your account has expired.";
"menu.message.no_active_subscriptions" = "No active subscription.";
"menu.message.on_a_trial" = "You're on a trial.";
"menu.message.csm-hubspot.title" = "Need help getting started?";
"menu.message.csm-hubspot.subtitle" = "Book time with an expert";
"menu.message.csm-hubspot.webview.title" = "Schedule a Call";
"menu.platforms" = "Platforms";
"menu.platforms.connect.message" = "Connect and manage your external streaming destinations or stream to nearly any platform using a Stream Key and Server URL provided by that platform.";
"social.platforms.error.title" = "Unable to load external destinations";
"social.platform.error.subtitle" = "Your internet connection appears to be offline. Check your network settings and try again.";
"SwitcherNavigationActionSelectorView.streaming" = "Go Live";
"SwitcherNavigationActionSelectorView.practice" = "Practice";
"SwitcherNavigationActionSelectorView.rec" = "Record Video";
"SwitcherNavigationActionSelectorView.upload" = "Upload Video";
"SwitcherNavigationOrientationSelectorView.enter" = "Enter Studio";
"Show.Instructions" = "Show Instructions";
"studio.loading.fail" = "Loading failed. **Tap to Close**";
"studio.share.title" = "Share Link";
"studio.share.practice.title" = "Share Private Link to Watch";
"SwitcherNavigationOrientationSelectorView.no.event.found.title" = "No Livestreams";
"SwitcherNavigationOrientationSelectorView.no.event.found.message" = "When you’re ready to go live, you can create a new livestream from the Outputs tab.";
"SwitcherNavigationOrientationSelectorView.practice.text" = "Test out your livestream and familiarize yourself with all the features of Switcher. Practice streams will only be visible to you, and recordings can be watched in your online dashboard for 14 days.";
"videolibrary.addtoplayers.addtocollections.nav.title" = "Add to Collections";
"videolibrary.addtoplayers.videocount_singular" = "1 Video";
"videolibrary.addtoplayers.videocount_plural %lld" = "%lld Videos";
"videolibrary.addtoplayers.playertitle.placeholder.label" = "Example Player";
"videolibrary.addtoplayers.information.text" = "This livestream will automatically stream to your Video Library. Visit your account at **switcherstudio.com** to manage your Video Catalog and collections.";
"videolibrary.addtoplayers.information.text.replay" = "This replay is automatically available in your Video Library. Visit your account at **switcherstudio.com** to manage your Video Catalog and collections.";
"videolibrary.addtocollections.button.text" = "Add to Collections";
"videolibrary.addtocollections.count_singular" = "1 Collection";
"videolibrary.addtocollections.count_plural %lld" = "%lld Collections";
"videolibrary.addtoplayers.load.error.title" = "Unable to Load Collections";
"videolibrary.addtoplayers.load.error.text" = "Your Internet connection appears to be offline. Check your network settings and try again.";
"videolibrary.addtoplayers.create.error.title" = "Unable to Create Collection";
"videolibrary.addtoplayers.create.error.text" = "Your Internet connection appears to be offline. Check your network settings and try again.";
"videolibrary.addtoplayers.empty.title" = "No collections yet";
"videolibrary.addtoplayers.empty.subtitle" = "Organize livestreams and videos from your library into collections that can be embedded on your website or shared using a dedicated watch page.";
"videolibrary.addtoplayers.create" = "Create Collection";
"videolibrary.addtoplayers.new" = "New Collection";
"videolibrary.upload.background.tooltip.title" = "Track Your Upload Progress";
"videolibrary.upload.background.tooltip.description" = "Keep the app open to ensure smooth uploading. Uploads will pause if you leave the app or create a new video.";
"videolibrary.uploads.title" = "Uploads";
"videolibrary.uploads.storagelimit" = "Storage limit reached";
"videolibrary.uploads.storagelimit.description" = "Uploads will resume when space is available in your Video Library.";
"videolibrary.uploads.noSubscription" = "No Subscription";
"videolibrary.uploads.noSubscription.description" = "Uploads will resume when your account has an active subscription.";
"videolibrary.uploads.noInternet" = "No Internet Connection";
"videolibrary.uploads.noInternet.description" = "Uploads will resume when your device has a stable network connection.";
"videolibrary.uploads.emptystate.title" = "No Uploads in Progress";
"videolibrary.uploads.emptystate.description" = "You can monitor the status of your next video upload on this screen.";
"videolibrary.uploads.tryagain" = "Try Again";
"videolibrary.uploads.cancelall" = "Cancel All";
"videolibrary.uploads.resume" = "Resume";
"videolibrary.uploads.pause" = "Pause";
"videolibrary.uploads.cancel.title" = "Cancel Upload" ;
"videolibrary.uploads.cancel.description" = "Your upload queue will be deleted. This action cannot be undone." ;
"videolibrary.uploads.cancel.notnow" = "Not Now" ;
"videolibrary.uploads.cancel.yes" = "Yes, Cancel";
"videolibrary.uploads.status.finishing" = "Finishing Upload..." ;
"videolibrary.uploads.status.queued" = "Queued" ;
"videolibrary.uploads.status.start" = "Starting Upload...";
"videolibrary.uploads.status.uploading" = "Uploading... %@%%";
"videolibrary.uploads.status.uploaded" = "Uploaded";
"videolibrary.form.links" = "Links";
"videolibrary.links.add.title" = "Add Links";
"videolibrary.links.add.new" = "New Link";
"videolibrary.links.add.edit" = "Edit Link";
"videolibrary.links.emptystate.description" = "These links will appear in the details tab of this video in every player that you add it to.";
"videolibrary.links.emptystate.title" = "No Links Yet";
"videolibrary.links.noconnection.title" = "Unable to Load Links";
"videolibrary.addlinks.button.text" = "Add Links";
"videolibrary.addlinks.count_singular" = "1 Link";
"videolibrary.addlinks.count_plural %lld" = "%lld Links";
"videolibrary.addlinks.url.error" = "Please enter a valid URL";
"videolibrary.addtags.button.text" = "Add Tags";
"videolibrary.addtags.count_singular" = "1 Tag";
"videolibrary.addtags.count_plural %lld" = "%lld Tags";
"videolibrary.uploads.addlinks.status" = "Create Weblinks";
"videolibrary.uploads.captions.status" = "Create Captions";
"videolibrary.uploads.deletelink" = "Delete Link";
"videolibrary.uploads.addlinks.detail" = "These links will appear in the details tab of this video in every player that you add it to.";
"videolibrary.uploads.addlinks.purchase" = "Hide link until video or subscription is purchased if a gated content pass is applied.";
"videolibrary.uploads.addlinks.more %@ %lld" = "%@ and %lld more";
"videolibrary.form.nolinks" = "No links yet";
"videolibrary.seemore" = "See More";
"videolibrary.seeless" = "See Less";
"studio.output.status.title" = "Destinations";
"studio.output.status.action.end" = "End Stream";
"studio.output.destination.title" = "Your Destinations";
"studio.output.destination.vc" = "Video Catalog";
"studio.output.destination.addnew" = "Add Destination";
"studio.output.destination.next" = "Next";
"studio.output.destination.max" = "You've reached the external destination limit for your plan.";
"studio.output.destination.video.catalog" = "Video Catalog";
"studio.output.destination.others" = "others";
"studio.output.destination.footer.connectedas %@" = "Connected as %@.";
"studio.output.destination.footer.signout" = "Disconnect";
"studio.output.destination.footer.alert.title %@" = "Sign Out from %@";
"studio.output.destination.footer.alert.message" = "Any existing destination will be removed. Are you sure?";
"studio.output.destination.footer.alert.progress" = "Signing Out...";
"studio.output.destination.footer.alert.success" = "Sign Out Successful";
"studio.output.destination.footer.alert.error" = "Unable to Sign Out";
"studio.output.destination.footer.alert.error.info" = "Please check your connection and try again.";
"studio.output.destination.footer.tooplip" = "External destinations cannot be changed or edited once your livestream is scheduled.";
"studio.output.event.title" = "Livestream Details";
"studio.output.event.scheduled %@" = "Livestream is scheduled for **%@**.";
"studio.output.event.golive" = "Go Live Now";
"studio.output.event.schedule.start" = "Starts";
"studio.output.event.schedule" = "Schedule for Later";
"studio.output.event.title.placeholder" = "Give your livestream a name";
"studio.output.event.description.placeholder" = "Tell your audience about your livestream";
"studio.output.event.settings" = "Livestream Settings";
"studio.output.event.settings.quality" = "Quality";
"studio.output.event.settings.quality.tooltip" = "For the best streaming experience, we recommend using 720p resolution or lower.";
"studio.output.event.savetovl" = "Save to Video Library";
"studio.output.event.create.go.live.now.button" = "Create Livestream";
"studio.output.event.create.scheduled.button" = "Schedule Livestream";
"studio.output.event.scheduled.cover.image" = "Thumbnail";
"studio.output.event.scheduled.cover.image.message" = "Recommended size is\n1920 x 1080 pixels.";
"studio.output.event.scheduled.upload.library" = "Camera Roll";
"studio.output.event.switcher.website" = "switcherstudio.com";
"studio.output.event.create.selector.creation.title" = "Create New Livestream";
"studio.output.event.create.selector.creation.description" = "Choose destinations and set up new stream.";
"studio.output.event.create.selector.existing.title" = "Use Existing Scheduled Post";
"studio.output.event.create.selector.existing.description" = "Import post details from Facebook or Youtube.";
"studio.output.event.create.selector.error.all" = "Something went wrong when checking existing scheduled posts.";
"studio.output.event.create.selector.error.partial" = "Something went wrong when checking existing scheduled posts for Facebook/Youtube.";
"studio.output.event.existing.title" = "Scheduled Posts";
"studio.output.event.existing.description" = "Choose the existing scheduled post created on Facebook or Youtube and create an new livestream you can use in Switcher.";
"event.creation.stream.status.delete.error.title" = "Something Went Wrong";
"event.creation.stream.status.delete.error.message" = "We were unable to end your stream properly. Please try again.";
"studio.output.stream.status.error.title" = "Unable to load destinations";
"studio.output.stream.status.error.description" = "Your internet connection appears to be offline. Check your network settings and try again.";
"studio.output.stream.status.active" = "Active";
"studio.output.stream.status.ready" = "Ready";
"studio.output.stream.status.ended" = "Ended";
"studio.output.stream.status.ending" = "Ending Stream...";
"studio.output.stream.status.connecting" = "Connecting...";
"studio.output.stream.status.button.end" = "End Stream";
"studio.output.stream.status.end.confirmation.title" = "End stream now?";
"studio.output.stream.status.end.confirmation.message %@" = "You’re about to end stream to %@. This cannot be undone.";
"studio.output.stream.status.end.confirmation.confirm" = "End Stream";
"studio.output.event.stream.directly" = "Stream directly to your website and landing page. To manage your Video Catalog, visit your account dashboard at **switcherstudio.com**.";
"studio.output.event.turn.on.catalog" = "Turn this on to stream directly to your website and landing page. To manage your Video Catalog, visit your account dashboard at **switcherstudio.com**.";
"destination.stream.to.label" = "Stream to";
"destination.facebook" = "Facebook";
"destination.facebook.header" = "Your Facebook account must be at least 60 days old and your page must have at least 100 followers to go live.";
"destination.facebook.timeline" = "Timeline";
"destination.facebook.timeline.my" = "My Timeline";
"destination.facebook.timeline.private" = "Private Test Stream";
"destination.facebook.pages" = "Pages";
"destination.facebook.page" = "Page";
"destination.facebook.content.tags" = "Content Tags";
"destination.facebook.tag" = "Tag";
"destination.facebook.tags" = "Tags";
"destination.facebook.add.tags" = "Add Tags";
"destination.facebook.search.tags" = "Search Tags";
"destination.facebook.no.tags.selected" = "No Tags Selected";
"destination.facebook.tags.no.results" = "No Results";
"destination.facebook.add.pages" = "Add Pages";
"destination.facebook.pages.no.results" = "No Pages";
"destination.facebook.pages.error" = "Unable to Load Pages";
"destination.facebook.no.pages" = "You don’t have any pages you can crosspost with.";
"destination.facebook.pages.offline.error" = "Your internet connection appears to be offline. Check your network settings and try again.";
"destination.facebook.tags.no.results.second.header" = "Try searching again using a different spelling or keyword";
"destination.facebook.tags.title" = "Content Tags";
"destination.facebook.crossposting" = "Crossposting";
"facebook.tags.error.title" = "Something Went Wrong";
"facebook.tags.error.subtitle" = "There was an error loading content tags. Please try again.";
"destination.facebook.event.everyone.title" = "Everyone";
"destination.facebook.event.everyone.description" = "Anyone on or off Facebook.";
"destination.facebook.event.friends.title" = "Friends";
"destination.facebook.event.friends.description" = "Your friends on Facebook.";
"destination.facebook.event.onlyMe.title" = "Only Me";
"destination.facebook.event.onlyMe.description" = "Private test stream.";
"destination.facebook.event.configuration.title %@" = "Facebook - %@";
"destination.facebook.timeline.event.configuration.title" = "Timeline Settings";
"destination.facebook.event.commentModeration.title" = "COMMENT MODERATION";
"destination.facebook.event.default.title" = "Default";
"destination.facebook.event.default.description" = "All viewers can participate in chat.";
"destination.facebook.event.follower.title" = "Follower";
"destination.facebook.event.follower.description" = "Only your followers will be able to leave comment.";
"destination.facebook.event.slow.title" = "Slow";
"destination.facebook.event.slow.description" = "Commenters will only be able to comment every 10 seconds.";
"destination.facebook.event.discussion.title" = "Discussion";
"destination.facebook.event.discussion.description" = "Only comments over 100 characters will be shown.";
"destination.facebook.event.restricted.title" = "Restricted";
"destination.facebook.event.restricted.description" = "Commenters must have accounts that are at least 2 weeks old.";
"destination.already.added" = "Already Added";
"destination.load.error" = "Unable to load your destinations";
"destination.settings.title" = "Destinations Settings";
"destination.catalog.description %@" = "Stream directly to your website and landing page. To manage your Video Catalog, visit your account dashboard at %@";
"destination.facebook.page.event.configuration.title" = "Page Settings";
"destination.youtube" = "YouTube";
"destination.youtube.header" = "Make sure your channel satisfies the **requirements to go live** on YouTube.";
"destination.youtube.channels.label" = "Channels";
"destination.youtube.forkids.title" = "Made for Kids";
"destination.youtube.forkids.description" = "Made for Kids option indicates that this stream is intended for children. This will restrict some features.";
"destination.youtube.embedded.title" = "Allow Embedding";
"destination.youtube.embedded.description" = "Youtube requires minimum 1,000 channel subscribers and 4,000 watch hours, or user verification (Valid ID or Video Verification) to use Embedded Live Feature.";
"destination.youtube.event.title" = "VISIBILITY";
"destination.youtube.event.public.title" = "Public";
"destination.youtube.event.public.description" = "Anyone can search for and view.";
"destination.youtube.event.private.title" = "Private";
"destination.youtube.event.private.description" = "Only people you choose can view.";
"destination.youtube.event.unlisted.title" = "Unlisted";
"destination.youtube.event.unlisted.description" = "Anyone with the link can view.";
"destination.youtube.event.configuration.title %@" = "YouTube - %@";
"destination.youtube.event.configuration.title" = "Channel Settings";
"destination.twitch" = "Twitch";
"destination.twitch.event.configuration.title" = "Account Settings";
"destination.twitch.add.category" = "Add Category";
"destination.twitch.event.category.title" = "Category";
"destination.twitch.event.category.search.title" = "Search categories";
"destination.twitch.event.category.search.empty.title" = "Search Categories";
"destination.twitch.event.category.search.empty.description" = "Twitch uses categories to classify the live streams and group them up with similar content.";
"destination.twitch.event.ingestServer.title" = "Ingest Server";
"destination.twitch.event.ingestServer.placeholder" = "Select Ingest Server";
"desination.twitch.event.ingestServer.description" = "Visit %@ to get information about ingest servers and to find out which ones are recommended for you.";
"destination.twitch.event.ingestServer.error.title" = "Unable to Load Ingest Servers";
"destination.twitch.event.ingestServer.error.text" = "Your Internet connection appears to be offline. Check your connection and try again.";
"destination.twitch.event.categories.error.title" = "Unable to Load Categories";
"destination.twitch.event.categories.error.text" = "Your Internet connection appears to be offline. Check your connection and try again.";
"destination.catalog.event.shopify" = "LIVE SHOPPING";
"destination.catalog.event.shopify.enable" = "Enable Live Shopping";
"destination.catalog.event.no.shopify" = "You need to connect your Shopify account with Cartr before you can sell live.";
"destination.catalog.event.enable.countdown" = "Show countdown over thumbnail";
"destination.catalog.event.one.time.pass" = "Add One-Time Pass";
"destination.catalog.event.one.time.pass.explain" = "Require purchase to view video.";
"destination.catalog.event.one.time.pass.price" = "Price";
"destination.catalog.one.time.pass.price.placeholder" = "2.00";
"destination.catalog.one.time.pass.price.prefix" = "US$";
"destination.catalog.one.time.pass.price.error" = "Amount must be at least $2.00";
"destination.catalog.one.time.pass.rental.error" = "Value must be greater than 0";
"destination.catalog.one.time.pass.name.placeholder" = "Give your pass a name";
"destination.catalog.event.one.time.pass.none" = "None";
"destination.catalog.event.no.one.time.passes" = "No One-Time passes found.";
"one.time.passes.error.title" = "Unable to Load Passes";
"one.time.passes.error.subtitle" = "Your internet connection appears to be offline. Check your network settings and try again.";
"one.time.passes.alert.title" = "Unable to save changes";
"one.time.passes.alert.message" = "Your internet connection appears to be offline. Check your network seetings and try again.";
"destination.catalog.event.one.time.pass.off" = "To start selling one-time access to your livestreams, visit your account dashboard at **switcherstudio.com**.";
"destination.catalog.event.one.time.pass.no.collection.added" = "Gating options are only possible when a collection has been selected as destination.";
"destination.catalog.one.time.pass.description.placeholder" = "Explain to your viewers what they will be able to watch by purchasing this pass.";
"destination.catalog.event.one.time.pass.creation" = "New Pass";
"one.time.passes.timed.access" = "Timed Access Period";
"one.time.passes.duration.hours" = "Hours";
"one.time.passes.duration.days" = "Days";
"one.time.passes.duration.weeks" = "Weeks";
"Livestream.delete.title.sing" = "Delete Livestream?";
"Livestream.delete.body %@" = "Are you sure you want to delete %@?";
"Livestream.delete.success" = "Livestream deleted successfully.";
"Livestream.delete.failed" = "Unable to Delete Livestream";
"Livestream.delete.progress" = "Deleting...";
"Livestream.go.to" = "Go to Livestream Details";
"livestream.how.to" = "How to Run a Test Livestream...";
"livestream.run.test.stream" = "Run a Test Livestream";
"event.creation.success.title" = "Your Livestream is Ready";
"event.creation.success.schedule.title" = "Your Livestream is Scheduled";
"event.creation.success.description.sing" = "Your destination is now configured and you're ready to go live.";
"event.creation.success.schedule.description.sing" = "Your destination is now configured.";
"event.creation.success.description.plur" = "All destinations have been successfully configured and you're ready to go live.";
"event.creation.success.schedule.description.plur" = "All destinations have been successfully configured.";
"event.creation.success.catalog.on.promotion.description" = "You can organize livestreams and videos from your library into collections. Visit your account at **switcherstudio.com** to learn more about it.";
"event.creation.success.catalog.off.promotion.description" = "You can go live directly to your website by using the Video Catalog. Give it a try when scheduling your next livestream or visit your account at **switcherstudio.com** to learn more about it.";
"event.creation.error.generic" = "There was an error with your %@ connection.";
"event.creation.warning.title" = "Please Check Details Below";
"event.creation.warning.description" = "Your livestream is ready but some of your destinations failed. Live streaming is possible on the configured destinations.";
"event.creation.error.youtube.embed.restriction" = "Youtube requires minimum 1,000 channel subscribers and 4,000 watch hours, or user verification (Valid ID or Video Verification) to use Embedded Live Feature";
"event.creation.error.facebook" = "Please reconnect your Facebook account and allow Switcher to make posts on your behalf.";
"event.creation.preparing" = "Preparing Your Livestream...";
"event.creation.error.title" = "Something Went Wrong";
"event.creation.error.subtitle" = "An error occurred when configuring your livestream.";
"event.creation.goback" = "Go Back";
"event.editing.success.title" = "Livestream Updated";
"event.editing.success.schedule.description" = "Your livestream details have been successfully updated.";
"event.editing.preparing" = "Updating Your Livestream...";
"event.editing.error.subtitle" = "An error occurred when updating your livestream.";
"event.editing.warning.title" = "Attention";
"event.editing.warning.description" = "Some destinations could not be properly updated. See details below.";
"event.editing.error.title" = "Something Went Wrong";
"event.editing.error.subtitle" = "An error occurred when updating your livestream.";
"event.upcoming.title" = "Scheduled Livestreams";
"event.upcoming.count.plur %lld" = "%lld livestreams";
"event.upcoming.count.sing" = "1 livestream";
"event.details.share.link" = "Share Livestream Link";
"event.details.scheduled.for.label %@" = "Scheduled for %@";
"event.details.video.player.details.error" = "Failed to load destination, please refresh.";
"permissions.title" = "Before you start";
"permissions.subtitle" = "Switcher needs access to a few permissions in order to work properly.";
"permissions.tooltip" = "You’re always in control. You can change this anytime in your device settings.";
"permissions.camera.title" = "Camera";
"permissions.camera.subtitle" = "Use your camera to record videos.";
"permissions.mic.title" = "Microphone";
"permissions.mic.subtitle" = "Use your microphone to record sound.";
"permissions.network.title" = "Local Network";
"permissions.network.subtitle" = "Use iPhones and iPads as additional cameras.";
"permissions.speech.title" = "Speech Recognition";
"permissions.speech.subtitle" = "Use speech recognition to generate transcript.";
"permissions.button.set" = "Next";
"transcription.copied.to.clipboard" = "Transcript copied to clipboard.";
"transcription.generation.title" = "Generating transcript...";
"transcription.generation.subtitle %lld" = "%lld caption(s) generated.\n\nPlease don't close the app or put it in the background.";
"transcription.generation.error" = "An error occurred when generating the transcript.";
"transcription.generation.error.noaudio" = "No audio was detected in your video, so we couldn't create a transcript.";
"transcription.start.title" = "Generate transcript";
"transcription.start.description" = "Automatically create transcript and captions for your video using speech recognition.";
"transcription.start.button" = "Start Recognition";
"transcription.access.title" = "Switcher Does Not Have Access to Speech Recognition";
"transcription.access.message" = "Speech recognition is used to generate transcripts of your video.";
"transcription.cloud.error" = "Unable to load transcript";
"transcription.cloud.processing" = "Transcript is processing...";
"productiongroups.empty.title" = "No groups";
"productiongroups.empty.subtitle" = "Streamline your productions by organizing your assets into production groups.";
"productiongroups.new" = "New Group";
"productiongroups.asset.sing" = "%d asset";
"productiongroups.asset.plur" = "%d assets";
"productiongroups.alert.title" = "Delete Group?";
"productiongroups.menu.hide" = "Hide Group";
"productiongroups.menu.unhide" = "Unhide Group";
"productiongroups.rename" = "Rename";
"productiongroups.rename.subtitle"= "Enter new group name";
"productiongroups.menu.rename.title" = "Rename Group";
"productiongroups.menu.delete" = "Delete Group";
"productiongroups.hidden.toast" = "Group hidden from Production Panel.";
"productiongroups.deleted.toast" = "Group deleted from Production Panel.";
"productiongroups.delete.alert.error.title" = "Error";
"productiongroups.delete.alert.error.message" = "Unexpected error occurred";
"productiongroups.delete" = "Delete";
"productiongroups.alert.assets.message %@" = "This group contains assets. These assets will be deleted if you continue.\nAre you sure you want to delete %@?";
"productiongroups.alert.noassets.message %@" = "Are you sure you want to delete %@?";
"productiongroups.add.error.duplicate.title" = "Name Already Taken";
"productiongroups.add.error.duplicate.description" = "Please choose a different name.";
"documentpickerview.filenotfound" = "File not found";
"schedule.live.event" = "Schedule Livestream";
"eventcreation.catalog.password" = "Password";
"eventcreation.catalog.email" = "Email";
"eventcreation.catalog.pass" = "One-Time Pass";
"eventcreation.catalog.collection.sing" = "1 collection";
"eventcreation.catalog.collection.plur" = "%d collections";
"eventcreation.catalog.gated" = "Gated";
"stream.schedule.title" = "Schedule";
"stream.schedule.go.live.now.title" = "Go Live Now";
"stream.schedule.go.live.now.message" = "Create an instant livestream to go live soon. Your audience will not be notified until you begin streaming.";
"stream.schedule.scheduled.for.later.title" = "Schedule for Later";
"stream.schedule.scheduled.for.later.message" = "Create a livestream for a specific date and time to share with your audience.";
"scene.builder" = "Scene Builder";
"scene.builder.fullscreen.title" = "Full-Screen Scene";
"scene.builder.overlay.title" = "Overlay Scene";
"scene.builder.fullscreen.subtitle" = "Combine sources, texts and images.";
"scene.builder.overlay.subtitle" = "Combine texts and images.";
"scene.builder.layer.new.title" = "New Layer";
"scene.builder.layer.text.title" = "Text";
"scene.builder.layer.text.edit.title %@" = "Edit %@";
"scene.builder.layer.image.title" = "Image";
"scene.builder.layer.image.replace" = "Replace Image";
"scene.builder.layer.source.title" = "Source";
"scene.builder.layer.shape.title" = "Shape";
"scene.builder.layer.rectangle.title" = "Rectangle";
"scene.builder.layer.circle.title" = "Circle";
"scene.builder.layer.background.title" = "Background";
"scene.builder.layer.delete.title" = "Delete Layer";
"scene.builder.layer.delete.confirmation.title %@" = "%@ will be deleted. Are you sure?";
"scene.builder.layer.delete.confirmation.confirm.title" = "Delete Layer";
"scene.builder.layer.text.select.font.title" = "Choose Font";
"scene.builder.layer.text.recent.font.title" = "Recents";
"scene.builder.layer.text.line.spacing.title" = "Line Spacing";
"scene.builder.layer.text.align.title" = "Text Alignment";
"scene.builder.layer.content.align.left.title" = "Left Align";
"scene.builder.layer.content.align.center.title" = "Center";
"scene.builder.layer.content.align.right.title" = "Right Align";
"scene.builder.add.sources.title" = "Number of Sources";
"scene.builder.add.sources.hint" = "A maximum of 9 sources can be added to a scene.";
"scene.builder.add.sources.button" = "Add Sources";
"scene.builder.helper" = "Start with an empty scene on which you can add sources (only for full-screen), images and texts to create custom assets.";
"videocatalog.update.error" = "Unable to update catalog settings";
"recommend.quality.title" = "Attention";
"recommend.quality.description" = "Your current connection is not fast enough to support the stream quality you selected, which could create problems when you go live.";
"storage.warning.description %@" = "Your storage has only %@ GB available and it won't be enough to store an hour of recording. We recommend you clean some space before starting the recording, or disable local recording.";
"storage.warning.continue" = "Continue Anyway";
"storage.warning.disable" = "Disable Local Recording";
"recommend.quality.recommend.keep.current.quality %@" = "Keep Current (%@p)";
"recommend.quality.recommend.use.recommended.quality %@" = "Use Recommended (%@p)";
"recommend.bitrate.title" = "Attention";
"recommend.bitrate.description %@" = "Your current connection is not fast enough to support the RTMP video bitrate settings you selected, which could create problems when you go live.\nWe recommend you reduce your bitrate to a maximum of %@ kbps.";
"recommend.bitrate.recommend.keep.current %@" = "Keep Current (%@ kbps)";
"recommend.bitrate.recommend.update.settings" = "Update RTMP settings";
"event.home.empty.title" = "Schedule Your Next Livestream";
"event.home.empty.subtitle" = "Attract a larger audience and promote your stream ahead of time by scheduling your next livestream.";
"event.home.empty.button" = "Schedule Now";
"event.home.see.all" = "See All Livestreams";
"event.home.error.title" = "Something Went Wrong";
"event.home.error.subtitle" = "Your Internet connection appears to be offline. Check your connection and try again.";
"event.time.away.weeks.plural %lld" = "In %lld Weeks";
"event.time.away.weeks.singular" = "In 1 Week";
"event.time.away.days.plural %lld" = "In %lld Days";
"event.time.away.days.singular" = "In 1 Day";
"event.time.away.hours.plural %lld" = "In %lld Hours";
"event.time.away.hours.singular" = "In 1 Hour";
"event.time.away.minutes.plural %lld" = "In %lld Minutes";
"event.time.away.minutes.singular" = "In 1 Minute";
"event.time.away.now" = "Now";
"mixer.error.reactnative.restart" = "Restart";
"mixer.error.reactnative.ignore" = "Ignore";
"mixer.error.reactnative.title" = "A cloud plug-in has raised an unexpected error";
"mixer.error.reactnative.message" = "This can have an impact on Remote Guests, Live Comments and Scoreboard.";
"auto.reconnect.message" = "Poor connection. Reconnecting...";
"camera.menu.exit" = "Disconnect Camera";
"switcher.api.error.no.internet.connection.title" = "Something Went Wrong";
"switcher.api.error.no.internet.connection.subtitle" = "Your Internet connection appears to be offline. Check your connection and try again.";
"switcher.api.error.no.server.connection.title" = "Something Went Wrong";
"switcher.api.error.no.server.connection.subtitle" = "Your connection to the servers appear to be offline. Check your connection and try again.";
"switcher.api.error.user.invalid.title" = "User is Invalid";
"switcher.api.error.user.invalid.subtitle" = "Please login and logout or check your user's active subscription";
"aspect-ratio-free-form" = "Custom";
"vprop.picker.cut" = "Cut";
"vprop.picker.push" = "Push";
"vprop.picker.cross-dissolve" = "Cross Dissolve";
"slideshow.select.firstimage" = "Select First Image";
