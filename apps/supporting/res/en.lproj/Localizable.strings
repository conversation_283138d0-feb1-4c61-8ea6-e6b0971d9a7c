"global-action.cancel.title" = "Cancel";
"global-action.skip.title" = "Skip";
"global-action.ok.title" = "OK";
"global-action.dismiss.title" = "Dismiss";
"global-action.done.title" = "Done";
"global-action.continue.title" = "Continue";
"global-action.close.title" = "Close";
"global-action.remove.title" = "Remove";
"global-text.error.title" = "Error";
"global-text.warning.title" = "Warning";
"global-text.slash-separator" = "/";
"global-text.yes" = "Yes";
"global-text.no" = "No";
"global-text.none" = "None";
"global-action.reset.title" = "Reset";
"brand.kit" = "Brand Kit";
"media-lib.text.Journal" = "Journal";
"media-lib.text.Angle" = "Angle";
"media-lib.text.Audio" = "Audio";
"media-lib.text.Part" = "Part";
"filter-list.title" = "Effects";
"filter-list.transition.title" = "Transitions";
"filter-list.transition.reorder" = "Reorder and Customize";
"filter-list.transition.text" = "Reorder or hide transitions you don't use to personalize your view.";
"filter-list.multi-view.title" = "Multiviews";
"filter-list.vfilter-none.title" = "None";
"filter-list.vfilter-cut.title" = "Cut";
"filter-list.vfilter-crossfade.title" = "Cross Dissolve";
"filter-list.vfilter-cube.title" = "Cube";
"filter-list.vfilter-twist.title" = "Twist";
"filter-list.vfilter-wipe.title" = "Wipe";
"filter-list.vfilter-cockpit.title" = "Dashboard";
"filter-list.vfilter-flap.title" = "Flap";
"filter-list.vfilter-slot.title" = "Slots";
"filter-list.vfilter-pinp.title" = "Picture-in-Picture";
"filter-list.vfilter-split-view.title" = "Split Screen";
"filter-list.vfilter-grid.title" = "Grid";
"filter-list.vfilter-gen.title" = "Frame Aspect Ratio";
"filter-list.vfilter-input.title" = "Frame Aspect Ratio";
"vfilter-prop.duration.title" = "Transition Duration";
"vfilter-prop.bg_color.title" = "Background Color";
"vfilter-prop.bg_image.title" = "Background Image";
"vfilter-prop.bg_alpha.title" = "Background Image Opacity";
"vfilter-prop.direction.title" = "Direction";
"vfilter-prop.test_pattern.title" = "Test Pattern";
"vfilter-prop.border_color.title" = "Border Color";
"vfilter-prop.border_thickness.title" = "Border Thickness";
"vfilter-prop.corner_radius.title" = "Rounding Radius";
"vfilter-prop.pos_x.title" = "Position X";
"vfilter-prop.pos_y.title" = "Position Y";
"vfilter-prop.pos_z.title" = "Position Z";
"vfilter-prop.stopwatch.title" = "Stopwatch";
"vfilter-prop.timer.title" = "Timer";
"vfilter-prop.yaw.title" = "Yaw";
"vfilter-prop.pitch.title" = "Pitch";
"vfilter-prop.roll.title" = "Roll";
"vfilter-prop.center.title" = "Center";
"vfilter-prop.frame_crop_h.title" = "Horizontal Cropping";
"vfilter-prop.frame_crop_v.title" = "Vertical Cropping";
"vfilter-prop.gap_h.title" = "Horizontal Gap";
"vfilter-prop.gap_v.title" = "Vertical Gap";
"vfilter-prop.reflection.title" = "Reflection";
"vfilter-prop.reflection_gap.title" = "Reflection Gap";
"vfilter-prop.reframing_mode.title" = "Framing";
"vfilter-prop.bg_mode.title" = "Layering";
"vfilter-prop.bg_mode.enum.source.title" = "Full-Screen";
"vfilter-prop.bg_mode.enum.overlay.title" = "Overlay";
"vfilter-prop.angle.title" = "Angle";
"vfilter-prop.angle.flap.title" = "Flap Angle";
"vfilter-prop.frame_format.title" = "Format";
"vfilter-prop.frame_format.flap.title" = "Flap Format";
"vfilter-prop.separator_direction.title" = "Separator Direction";
"vfilter-prop.separator_position.title" = "Separator Position";
"vfilter-prop.separator_thickness.title" = "Separator Thickness";
"vfilter-prop.separator_color.title" = "Separator Color";
"vfilter-prop.spacing.title" = "Spacing";
"vfilter-prop.uniform_gap.title" = "Uniform Gap";
"vfilter-prop.bottom_margin.title" = "Bottom Margin";
"vfilter-prop.view_angle.cockpit.title" = "Perspective";
"vfilter-prop.dark_color_transp.title" = "Fade Black to Transparent";
"vfilter-prop.chroma_key.header" = "Chroma Key";
"vfilter-prop.chroma_key.title" = "Enable Chroma Key";
"vfilter-prop.chroma_key_color.title" = "Key Color";
"vfilter-prop.chroma_key_range.title" = "Range";
"vfilter-prop.chroma_key_softness.title" = "Softness";
"vfilter-prop.chroma_key_edge_desat.title" = "Edge Desaturation";
"vfilter-prop.chroma_key_alpha_crop.title" = "Alpha Crop";
"vfilter-prop.chroma_key_bg_image.title" = "Background Image";
"UIFrameFormatPropViewController.title" = "Format";
"PropBundleEditor.disabled-because-no-reframing.msg" = "These properties have no effect because the content perfectly fits the output frame.";
"PropBundleEditorViewController.restore-defaults-button.title" = "Restore Default Settings";
"source-list.title" = "Cameras";
"source-list.inputs.title" = "Inputs";
"source-list.angle.title" = "Angle";
"source-list.audio.title" = "Audio";
"source-list.other-item.title" = "Add Source";
"source-list.built-in-source.title" = "Built-in and Plugged-in Sources";
"source-list.network-source.title" = "Sources on Your Wi-Fi Network";
"source-list.alert.busy.msg.text" = "%@ is busy";
"source-list.alert.bad-version.msg.text" = "%@ version mismatch";
"source-list.alert.bad-format.msg.text" = "%@ format not supported";
"source-list.alert.bad-prod.msg.text" = "%@ not supported";
"source-list.alert.ok-button.text" = "OK";
"source-list.multi-src-name.fmt" = "%@ on %@";
"source-list.not-available-item-local.title" = "Not available";
"source-list.not-available-item-wifi.title" = "None";
"SourceListIcons.menu.title" = "Sources";
"SourceListIcons.source.camera" = "Camera";
"SourceListIcons.source.mobile-screen" = "Mobile Screen";
"SourceListIcons.source.desktop-screen" = "Desktop Screen";
"SourceListIcons.source.connect-url" = "Connect by url";
"SourceListIcons.source.videochat" = "Videochat";
"SourceInformationsTableViewController.instructions.header" = "Instructions";
"SourceInformationsTableViewController.title.camera" = "Camera";
"SourceInformationsTableViewController.instructions.camera-1" = "Open Switcher Studio on your additional iOS device.";
"SourceInformationsTableViewController.instructions.camera-2" = "Tap \"Share Camera or Screen\".";
"SourceInformationsTableViewController.instructions.camera-3" = "Tap \"Share Camera\".";
"SourceInformationsTableViewController.instructions.camera-4" = "Tap the device name in the Sources list on your main device.";
"SourceInformationsTableViewController.camera-extra" = "If you're connected on your additional iOS device, the \"Share Camera or Screen\" option is available in the Menu page";
"SourceInformationsTableViewController.title.mobile-screen" = "Mobile Screen";
"SourceInformationsTableViewController.instructions.mobile-screen-1" = "Open Switcher Studio on your additional iOS device.";
"SourceInformationsTableViewController.instructions.mobile-screen-2" = "Tap \"Share Camera or Screen\".";
"SourceInformationsTableViewController.instructions.mobile-screen-3" = "Tap \"Share Screen\".";
"SourceInformationsTableViewController.instructions.mobile-screen-4" = "Tap the device name in the Sources list on your main device.";
"SourceInformationsTableViewController.title.desktop-screen" = "Desktop Screen";
"SourceInformationsTableViewController.instructions.desktop-screen-1" = "Install Switcher Cast on your computer.";
"SourceInformationsTableViewController.instructions.desktop-screen-2" = "Connect your computer to the same Wi-Fi as your iOS device(s).";
"SourceInformationsTableViewController.instructions.desktop-screen-3" = "Launch Switcher Cast to enable computer screensharing.";
"SourceInformationsTableViewController.instructions.desktop-screen-4" = "Tap your computer source(s) in the Sources list on your main device.";
"SourceInformationsTableViewController.connect-url.header" = "Not seeing your device in the list?";
"SourceInformationsTableViewController.connect-url.title" = "Connect by URL";
"SourceInformationsTableViewController.download-link.title" = "Share download link";
"SourceInformationsTableViewController.download-link.desktop-win" = "Switcher Cast for Windows";
"SourceInformationsTableViewController.download-link.desktop-mac" = "Switcher Cast for MacOS";
"CameraUrlInformationsTableViewController.instructions.header" = "Instructions";
"CameraUrlInformationsTableViewController.instructions.camera-1" = "Open the Switcher app on your main iOS device.";
"CameraUrlInformationsTableViewController.instructions.camera-2" = "Log in and choose \"Horizontal\" or \"Vertical\".";
"CameraUrlInformationsTableViewController.instructions.camera-3" = "Tap the Inputs tab (video camera icon).";
"CameraUrlInformationsTableViewController.instructions.camera-4" = "If your device is not listed as a source, tap \"Add source\".";
"CameraUrlInformationsTableViewController.instructions.camera-5" = "Tap \"Connect by URL\".";
"CameraUrlInformationsTableViewController.instructions.camera-6" = "Enter the URL displayed on this additional iOS device.";
"RecordingInterruptedAlertController.msg" = "Disconnecting a camera will interrupt the audio and video recording on that device.";
"RecordingInterruptedAlertController.ok" = "OK";
"RecordingInterruptedAlertController.dont-show-again" = "Don't show again";
"URLConnectionController.title.text" = "Connect to URL";
"URLConnectionController.url.title.text" = "Enter the camera's URL:";
"URLConnectionController.history.title.text" = "Recently used:";
"URLConnectionController.history.empty.text" = "None";
"URLConnectionController.action-connect.text" = "Connect";
"URLConnectionController.alert.no-wifi.text" = "No Wi-Fi network";
"URLConnectionController.alert.bad-url.text" = "Bad URL";
"URLConnectionController.alert.itself.text" = "You cannot connect to yourself";
"IOSAVProvider.camera.title" = "Built-in Camera";
"IOSAVProvider.back-camera.title" = "Back Camera";
"IOSAVProvider.front-camera.title" = "Front Camera";
"LocalAudio.source.title" = "Built-in Mic/Audio Input";
"MixerViewController.program.text" = "Live";
"MixerViewController.preview.text" = "Preview";
"MixerViewController.sources.text" = "Sources";
"MixerViewController.prev-to-prog-button.left.text" = "Preview";
"MixerViewController.prev-to-prog-button.right.text" = "Live";
"MixerViewController.stream-validation.msg" = "Stream Validation in Progress...";
"CheckDevice.generic-alert.msg" = "%@ lets you film from multiple angles with multiple devices on your Wi-Fi network. To do that, it needs access to your camera, microphone, and local network. You can enable access in Settings > Switcher.";
"CheckDevice.settings.title" = "Adjust Settings";
"CheckDevice.no-local-network.title" = "Switcher Does Not Have Access to Your Local Network";
"CheckDevice.no-microphone.title" = "Switcher Does Not Have Access to Your Microphone";
"CheckDevice.no-camera.title" = "Switcher Does Not Have Access to Your Camera";
"MediaViewController.byte-unit" = "B";
"MediaViewController.media-state.searching.title" = "Searching...";
"MediaViewController.media-state.missing.title" = "Missing";
"MediaViewController.media-state.missing-hostname.title" = "Missing (recorded on %@)";
"MediaViewController.media-state.missing-connection.title" = "Missing (please connect %@)";
"MediaViewController.media-state.cancelled.title" = "Cancelled";
"MediaViewController.media-state.cancelling.title" = "Cancelling...";
"MediaViewController.media-state.error.title" = "Error";
"MediaViewController.media-state.out-of-mem.title" = "Out of memory (RAM)";
"MediaViewController.media-state.out-of-storage.title" = "Not enough storage available on your device";
"MemoryViewController.title" = "Memory";
"MemoryViewController.available-label.text" = "Available Memory";
"MemoryViewController.enlarge-button.text" = "Enlarge";
"MemoryViewController.build-button.text" = "Build Composition";
"MemoryViewController.ignore-switch.text" = "Ignore Memory Limits";
"MemoryViewController.help" = "If you run out of memory while rendering the composition, please close all apps, turn off the device and turn it on again. For more information, please visit our web site.";
"MemoryViewController.byte-unit" = "B";
"CameraControl.zoom" = "Zoom";
"CameraControl.focus" = "Focus";
"CameraControl.expo" = "Exposure";
"CameraControl.wbal" = "White Balance";
"CameraControl.light" = "LED Light";
"CameraControl.stab" = "Stabilization";
"CameraControl.noFocusWhenLive" = "Lock Autofocus When Live";
"CameraControl.resetToDefaults" = "Restore Default Settings";
"CameraControl.saveSettings" = "Save Camera Settings";
"CameraControl.vptz.restore-default-presets" = "Restore Default Presets";
"CameraControl.vptz.enable" = "Enable ePTZ";
"CameraControl.vptz.unsupported-iphone-camera-tip" = "This feature is not compatible with iPhone's build-in camera. Please select a different camera.";
"CameraControl.tab.vptz" = "ePTZ";
"CameraControl.tab.controls" = "Controls";
"AudioViewController.title" = "Audio";
"AudioViewController.main-grp.title" = "Main Channel";
"AudioViewController.main-grp.switcher.title" = "Audio for Recording/Broadcast";
"AudioViewController.aux-grp.title" = "Auxiliary Channels";
"AudioViewController.aux-grp.footer" = "Auxiliary channels are recorded but not included in the final composition.";
"AudioViewController.global-settings-grp.title" = "Options";
"AudioViewController.global-settings-grp.footer" = "Audio Monitoring allows you to listen to the recorded/broadcasted audio through your headphones or other output currently active. On some audio interfaces, this could generate echo.\
Voice Mode provides audio-processing features like echo cancellation. It is required for AirPods and Bluetooth headsets. Turn Voice Mode off when using wired or external audio devices or playing music.";
"AudioViewController.monitoring.title" = "Audio Monitoring";
"AudioViewController.voice-mode.title" = "Voice Mode";
"CameraControlTableViewController.title" = "Camera Control";
"ResyncDelayLevel.0.long" = "Low-Latency Mode";
"ResyncDelayLevel.1.long" = "Standard Wi-Fi Mode";
"ResyncDelayLevel.2.long" = "Reinforced Wi-Fi Mode";
"ResyncDelayLevel.0.short" = "Low-Latency";
"ResyncDelayLevel.1.short" = "Standard";
"ResyncDelayLevel.2.short" = "Reinforced";
"ResyncDelayLevel.0.description" = "In low-latency mode, video is displayed very shortly after being captured, but the quality may be compromised. In this mode, stabilization and 4K-recording are not allowed.";
"ResyncDelayLevel.1.description" = "The standard mode is the best trade-off between latency and video quality. It requires a robust Wi-Fi network.";
"ResyncDelayLevel.2.description" = "This mode makes no compromise on video quality but introduces a 1-second delay on the displayed video to work around most limitations introduced by Wi-Fi networks that may be weak, overloaded or subject to interference.";
"ResyncDelayLevel.2.description-temporary-addon" = "(temporary)";
"OutputViewController.title" = "Outputs";
"OutputViewController.outputs.title" = "Outputs";
"OutputViewController.outputs.camo.title" = "To Zoom, Meet, Teams, Skype, ...";
"OutputViewController.external-display.source.title" = "To HDMI/AirPlay";
"OutputViewController.external-display.not-allowed.title" = "HDMI/AirPlay";
"OutputViewController.external-display.not-allowed.msg" = "To use HDMI or AirPlay, please consider using Switcher Studio";
"OutputViewController.external-display.not-allowed.more-info" = "More Info";
"OutputViewController.streaming.title" = "Broadcast Mode";
"OutputViewController.streamingOptions.title" = "Broadcasting Options";
"OutputViewController.streaming.recordonly.title" = "Recording";
"OutputViewController.streaming.recordonly-autosave.title" = "Recording";
"OutputViewController.streaming.simulcast.title" = "Livestreaming";
"OutputViewController.practice.mode.title" = "Practice";
"OutputViewController.streaming.selectedEvent" = "Active Livestream";
"OutputViewController.streaming.selectAnotherEvent" = "Select Another Livestream";
"OutputViewController.event.noevents.header" =  "No Livestreams";
"OutputViewController.event.noevents.body" = "Create your livestreams in advance and they will appear here.";
"OutputViewController.streaming.generic.title" = "Custom RTMP";
"OutputViewController.event.noevent" = "No Livestreams";
"OutputViewController.event.noevent.subtitle" = "Create one to get started";
"OutputViewController.event.nochannel" = "No Channel";
"OutputViewController.event.nochannel.subtitle" = "Create new or select existing";
"OutputViewController.event.golive" = "Go Live Now";
"OutputViewController.event.button.create" = "Create";
"OutputViewController.event.button.change" = "Change";
"OutputViewController.event.destinations %d" = "%d destinations";
"OutputViewController.event.destinations.sing" = "1 destination";
"OutputViewController.event.share" = "Share Link";
"OutputViewController.rec.title" = "Recording";
"OutputViewController.rec.director-mode.title" = "Director Mode";
"OutputViewController.rec.rtmp.reconnect.title" = "RTMP Reconnect";
"OutputViewController.rec.options.title" = "Advanced Settings";
"OutputViewController.rec.tip.msg" = "Enable the Director Mode to record high quality video.";
"OutputViewController.isocam-warning.msg" = "Enabling “Director Mode” will disable all built-in and USB cameras on this device. When you start your production, each camera will make its own recording. A full 1080p HD or 4K video can then be made using the source files. Make sure you have enough storage on each device before continuing. Visit our knowledge base for more information.";
"OutputViewController.wi-fi.title" = "Wi-Fi Camera Communication";
"OutputViewController.wi-fi.resync-delay.title" = "Wi-Fi Optimization Mode";
"OutputViewController.refresh-control.title" = "Synchronizing with the Dashboard...";
"OutputViewController.display-selection.program.short" = "Live";
"OutputViewController.display-selection.mirroring.short" = "All";
"OutputViewController.resolution.recording" = "Video Quality";
"OutputViewController.resolution.streaming" = "Stream Quality";
"OutputSourcesTableViewController.display-selection.mirroring.long" = "Output the Whole Screen";
"OutputSourcesTableViewController.display-selection.program.long" = "Output the Live Feed";
"OutputSourcesTableViewController.airplay-tip.msg" = "To enable or disable AirPlay, please use the corresponding button in the Control Center. Mirroring has to be switched on.";
"OutputSourcesTableViewController.underscan.title" = "Underscan";
"OutputSourcesTableViewController.underscan.auto.title" = "Auto";
"OutputSourcesTableViewController.rotation.title" = "Rotation";
"LimitedAccessOutputViewController.header" = "Expired Account";
"LimitedAccessOutputViewController.body" = "Your account has expired.\
\
<NAME_EMAIL> with any questions.";
"LimitedAccessOutputViewController.iap.header" = "No subscription";
"LimitedAccessOutputViewController.iap.body" = "You need an active subscription to access Outputs settings.";
"BCProfileListViewController.title" = "RTMP Channels";
"BCProfileListViewController.new-profile.name" = "My Channel";
"BCProfileListViewController.new-profile.button" = "New Channel";
"BCProfileListViewController.no-profile.msg" = "No RTMP Channels";
"BCProfileListViewController.new.msg" = "Swipe down to refresh";
"BCProfileListViewController.refresh.button" = "Refresh";
"BCProfileListViewController.delete" = "Delete";
"RTMPDefViewController.title" = "RTMP Parameters";
"RTMPDefViewController.name.title" = "Channel Name";
"RTMPDefViewController.name.placeholder" = "Enter channel name";
"RTMPDefViewController.url.title" = "Server URL";
"RTMPDefViewController.url.placeholder" = "Enter your stream URL";
"RTMPDefViewController.stream.title" = "Stream Key";
"RTMPDefViewController.stream.placeholder" = "Enter your stream key or ID";
"RTMPDefViewController.options.title" = "Broadcast Options";
"RTMPDefViewController.vformat.title" = "Video Resolution";
"RTMPDefViewController.vbitrate.title" = "Video Bitrate";
"RTMPDefViewController.aformat.title" = "Audio Format";
"RTMPDefViewController.abitrate.title" = "Audio Bitrate";
"RTMPDefViewController.compat.title" = "Compatibility Options";
"RTMPDefViewController.compat-fmle.title" = "Emulate Flash Media Live Encoder";
"RTMPDefViewController.speed-test" = "Speed Test";
"RTMPDefViewController.speed-test.title" = "Run Speed Test";
"RTMPDefViewController.speed-test.msg" = "Configure the broadcast options based on the speed of your current Internet connection.";
"RTMPDefViewController.default.short-form.title" = "Default";
"RTMPDefViewController.default.long-form.title" = "Default value";
"RTMPDefViewController.error.title" = "Unable to Save Changes";
"RTMPDefViewController.error.msg" = "Please check your connection and try again.";
"menu.title" = "Menu";
"library.title" = "Video Library";
"home.title" = "Home";
"create.new.title.loggedIn" = "New Video";
"menu.knowledge-base.title" = "Help Center";
"menu.settings.title" = "App Settings";
"menu.media.title" = "Local Recordings";
"menu.diag.title" = "Diagnostic Data";
"menu.home.title" = "Go Back to Home Screen";
"menu.home.confirm.title" = "Are you sure you want to quit?";
"menu.home.confirm.comment" = "This will disconnect all cameras and return to the home screen.";
"menu.home.confirm.yes" = "Disconnect & Quit";
"menu.home.confirm.no" = "Resume";
"menu.account.title" = "Account";
"menu.brand-profile.title" = "Brand Kit";
"menu.iphone.studio-mixer.info.title" = "About Devices";
"GalileoConnectionViewController.auto-detect.help.text" = "The auto-detection allows you to connect Galileo as soon as the app starts.";
"GalileoConnectionViewController.auto-detect.switch.text" = "Auto-Detection";
"GalileoConnectionViewController.status.connected" = "Connected";
"GalileoConnectionViewController.status.unconnected" = "Twist to Connect";
"GalileoConnectionViewController.title" = "Motrr Galileo";
"ArtworkProdFlow.error.asset-on-icloud.mgs" = "This asset is not stored on your device.";
"ArtworkProdFlow.error.unsupported-video.mgs" = "Unsupported video format. Please convert to AVC (H.264) or HEVC (H.265).";
"ArtworkProdFlow.error.noavasset.mgs" = "No asset available";
"ArtworkProdFlow.error.notvalid.mgs" = "The format of the asset is not valid";
"ArtworkProdFlow.download-switchercloud.msg" = "Download from Switcher Cloud";
"ArtworkProdFlow.download-icloud.msg" = "Download from iCloud";
"ArtworkProdFlow.download-switchercloud.error.msg" = "Download failed from Switcher Cloud";
"ArtworkProdFlow.error.no-phasset.mgs" = "This asset is not available";
"ElementCollectionViewController.title %@" = "Slot %@ Source";
"ElementCollectionViewController.menu2.edit.title" = "Edit Properties";
"ElementCollectionViewController.menu2.duplicate.title" = "Duplicate";
"ElementCollectionViewController.menu2.move.title" = "Reorder";
"ElementCollectionViewController.menu2.remove.title" = "Remove";
"ElementCollectionViewController.menu2.quick-multiview.title" = "Combine";
"ElementCollectionViewController.multiview-menu.alone.title" = "Solo";
"ElementCollectionViewController.plus-button.tip" = "Tap [+] to add cameras, photos, videos,\
titles, logos and multiviews.";
"TitleListViewController.title" = "Titles";
"TitleListViewController.generic.title" = "Generic Labels";
"TitleListViewController.generic.free-title.title" = "Full Screen Text";
"TitleListViewController.generic.free-label.title" = "Text Overlay";
"TitleListViewController.specific-layout.title" = "Frames";
"TitleListViewController.specific-layout.postcard.title" = "Postcard";
"TitleListViewController.specific-content.title" = "Templates";
"LowerThirdListViewController.title" = "Lower Thirds";
"PlatformOverlayListViewController.title" = "Platform Overlays";
"PlatformOverlayBannerContent.title" = "Customize overlays for your social media or donation platforms.";
"BroadcastNotificationListViewController.title" = "Broadcast Notifications";
"VMakerEditorViewController.title.blank" = "Blank";
"VMakerEditorViewController.title.live" = "Live";
"VMakerEditorViewController.picker.blank" = "Blank Canvas";
"VMakerEditorViewController.picker.live" = "Live Output";
"VMakerEditorViewController.tips" = "To resize a graphic, pinch and zoom. To reposition, tap and drag.";
"VMakerEditorViewController.apply-on-preview" = "Apply on Preview";
"VMakerEditorViewController.stay-on-top" = "Stay on Top";
"VMakerEditorViewController.restore-defaults" = "Restore Default Settings";
"VMakerEditorViewController.transition-picker" = "In & Out Transition";
"ElementGroupsViewController.transitions.disabled" = "Disabled";
"VMakerEditorViewController.group-picker" = "Production Group";
"ArtworkEditorViewController.title" = "Properties";
"CameraEditorViewController.title" = "Camera Properties";
"CameraEditorViewController.screen-content.title" = "Screen Properties";
"PlayedVideoEditorViewController.title" = "Video Properties";
"PlayedVideoEditorViewController.loop.title" = "Loop";
"PlayedVideoEditorViewController.loop.description" = "When loop is enabled, the video asset will play continually until removed from screen.";
"PlayedVideoEditorViewController.thumbnail-selector.title" = "Thumbnail Selection";
"PlayedVideoEditorViewController.end-on-last-frame.title" = "End on Last Frame";
"PlayedVideoEditorViewController.audio-enabled.title" = "Enable Audio";
"PlayedVideoEditorViewController.trimmer.title" = "Trimmer";
"PlayedVideoEditorViewController.trimmer.from" = "From";
"PlayedVideoEditorViewController.trimmer.to" = "to";
"PlayedVideoEditorViewController.trimmer.end" = "the end";
"PlayedVideoEditorViewController.volume.title" = "Volume";
"PlayedVideoEditorViewController.volume.value-db" = "%@ dB";
"PlayedVideoThumbnailSelectorViewController.title" = "Thumbnail Selection";
"PlayedVideoTrimmerViewController.feature-trimmer-only.title" = "Trimmer";
"PlayedVideoTrimmerViewController.infos.from" = "From:";
"PlayedVideoTrimmerViewController.infos.to" = "To:";
"PlayedVideoTrimmerViewController.infos.duration" = "Duration:";
"MultiviewEditorViewController.title" = "Multiview Properties";
"MultiviewInputSubtableController.num-of-inputs.title" = "Number of Sources";
"UserAccount.subscription-expired.msg" = "Account inactive - subscription expired";
"UserAccount.access-refused.msg" = "Access refused";
"AccountViewController.buy.no-product.title" = "No subscription available";
"AccountViewController.buy.success.title" = "Congratulations!";
"AccountViewController.buy.success.message" = "Your purchase succeeded";
"AccountViewController.buy.failed.title" = "Purchase Failed";
"AccountViewController.restore.no-product.title" = "Finished";
"AccountViewController.restore.no-product.message" = "No product to restore";
"AccountViewController.restore.success.title" = "Congratulations!";
"AccountViewController.restore.success.message" = "Subscriptions successfully restored";
"AccountViewController.restore.failed.title" = "Failed";
"AccountViewController.processing.title" = "Processing";
"AccountViewController.processing.message" = "This will only take a moment.";
"AccountViewController.restore.button.title" = "Restore purchases";
"AccountViewController.restore.receipt-warning.title" = "There was an issue during your last transaction. Please restore your purchased subscription.";
"AccountViewController.restore.logout-warning.title" = "There was an issue during your last transaction, please go back to the main screen and log you in.";
"AppleInAppManagerError.noAppleReceiptFound" = "Apple receipt was not found in the phone";
"AppleInAppManagerError.noPurchaseToRestore" = "No purchase to restore";
"AppleInAppManagerError.paymentWasCancelled" = "In-App Purchase process was cancelled";
"AppleInAppManagerError.productRequestFailed" = "Unable to fetch available In-App Purchase products at the moment";
"AppleInAppManagerError.noProductsFound" = "No In-App Purchases were found";
"AppleInAppManagerError.noProductIDsFound" = "No In-App Purchase product identifiers were found";
"CloudInAppManagerError.uploadReceiptError" = "Unable to upload receipt on server";
"CloudInAppManagerError.productListError" = "Unable to get in-app product list";
"CloudInAppManagerError.decodingDatas" = "Decoding datas issue";
"MediaManViewController.title" = "Send to Computer";
"MediaPlayerViewController.trash-menu.remove.title" = "Remove";
"MediaPlayerViewController.asset-type.title" = "type";
"MediaPlayerViewController.creation-date.title" = "creation date";
"MediaPlayerViewController.frame-size.title" = "frame size";
"MediaPlayerViewController.file-size.title" = "file size";
"MediaPlayerViewController.duration.title" = "duration";
"MediaPlayerViewController.encoding.title" = "encoding";
"AssetExporter.send-menu.share-to-fb-reels.title" = "Share to Facebook Reels";
"AssetExporter.send-menu.share-to-fb-stories.title" = "Share to Facebook Stories";
"AssetExporter.send-menu.share-to-ig-stories.title" = "Share to Instagram Stories";
"AssetExporter.send-menu.share-to-ig-feed.title" = "Share to Instagram Feed";
"AssetExporter.send-menu.share-to-tiktok.title" = "Share to TikTok";
"AssetExporter.send-menu.share-to-meta-loo-long.msg" = "Videos longer than %d minutes cannot be shared to %@.";
"AssetExporter.send-menu.share-to-app-not-installed.msg" = "%@ app is required for this action, please install it first.";
"AssetExporter.send-menu.share-video-could-not-be-read.msg" = "Video file could not be read.";
"AssetExporter.send-menu.share-video-all-photos-perm-req.msg" = "This feature requires 'All Photos' access to Photo Library. You can adjust Photos permission for Switcher in the Settings app.";
"AssetExporter.open-in.no-app.msg" = "No app found to open this media";
"AssetExporter.progress.title" = "Import in progress";
"AssetExporter.send-menu.webdav.title" = "Send to Media Manager";
"RecProfileViewController.director-mode.title" = "Director Mode";
"RecProfileViewController.director-mode.disabled" = "These options are only available when Director Mode is enabled.";
"RecProfileViewController.director-mode.frame-size.title" = "Composition Format";
"RecProfileViewController.director-mode.frame-size.auto.short" = "Auto";
"RecProfileViewController.director-mode.frame-size.auto.long" = "Auto";
"RecProfileViewController.director-mode.frame-size.tip" = "Only compatible devices will record in the selected resolution. Other devices will record in their highest respective resolutions. NOTE: 4K recording is not allowed in Low-Latency Wi-Fi Mode, is not supported on devices running iOS14 and is limited to 30 fps.";
"RecProfileViewController.director-mode.frame-rate.title" = "Composition Frame Rate";
"RecProfileViewController.director-mode.frame-rate.variable.title" = "Camera VFR Mode";
"RecProfileViewController.director-mode.frame-rate.variable.tip" = "Use VFR (Variable Frame Rate) mode for high quality in low lighting";
"RecProfileViewController.director-mode.bit-rate.title" = "Recording Bitrate";
"RecProfileViewController.remote-camera.title" = "Remote Cameras";
"RecProfileViewController.remote-camera.rec.title" = "Record Remote Cameras";
"RecProfileViewController.remote-camera.rec.disabled" = "This option is only available when Director Mode is disabled.";
"RecProfileViewController.wifi.title" = "Wi-Fi Camera Communication";
"RecProfileViewController.outputs.title" = "Outputs";
"RecProfileViewController.live-output.title" = "Live Output";
"RecProfileViewController.live-output.rec.title" = "Local Recording";
"RecProfileViewController.live-output.frame-size.title" = "Live Output Format";
"RecProfileViewController.live-output.frame-size.tip" = "These parameters only apply when no broadcasting channel is selected.";
"prod-action.alert.stop-rec-and-broadcast-button.title" = "Stop Rec + Broadcast";
"prod-action.alert.stop-broadcast-button.title" = "Continue Recording";
"prod-action.alert.recover-broadcast-button.title" = "Recover Broadcast";
"prod-action.alert.stop-button.title" = "Stop Broadcast";
"ProdError.error.BadUrlScheme.msg" = "Unsupported protocol";
"ProdError.error.NoHost.msg" = "Streaming server not reachable";
"ProdError.error.NoSocket.msg" = "No connection socket";
"ProdError.error.NoConnection.msg" = "Streaming server connection failed";
"ProdError.error.NoESocket.msg" = "Connection error: No transport layer";
"ProdError.error.NoESocketHandshake.msg" = "Connection error: The security validation is missing";
"ProdError.error.NoRTMPHandshake.msg" = "The streaming server doesn't respond";
"ProdError.error.BadRTMPHandshake.msg" = "Streaming server bad response";
"ProdError.error.UnknownRTMPError.msg" = "RTMP protocol error";
"ProdError.error.SocketClosedByPeer.msg" = "Connection closed by the server";
"ProdError.error.SocketError.msg" = "Broadcasting communication error";
"ProdError.error.SocketTxError.msg" = "Transmission failed";
"ProdError.error.SocketRxError.msg" = "Reception failed";
"ProdError.error.PracticeModeFailed.msg" = "Practice livestream failed";
"ProdError.error.SocketSecurityError.msg" = "Security error";
"ProdError.error.InvalidAccount.msg" = "No valid account on the selected streaming service";
"ProdError.error.InvalidIngestionSettings.msg" = "Invalid streaming resolution";
"ProdError.error.StreamingProviderGenericError.msg" = "Streaming provider error";
"ProdError.error.InconsistentProfile.msg" = "The broadcasting parameters are not consistent with the server ones. Please return to livestream settings to reload all parameters";
"ProdError.error.BadHTTPResponseCode.msg" = "The server rejected the request";
"ProdError.error.BadHTTPResponseFormat.msg" = "Bad response from server";
"ProdError.error.HTTPRequestFailed.msg" = "The server is not available";
"ProdError.error.DualStreamingNotSupported.msg" = "Unsupported streaming mode";
"ProdError.error.NothingToStream.msg" = "The stream has no content";
"ProdError.error.HLSFailed.msg" = "HLS streaming error";
"ProdError.error.sec.TLS_INVALID_CERT.msg" = "Connection error: Invalid security certificate";
"ProdError.error.sec.TLS_NO_CERT.msg" = "Connection error: No security certificate";
"ProdError.error.sec.TLS_EXPIRED_CERT.msg" = "Connection error: Security certificate is expired";
"ProdError.error.sec.TLS_HANDSHAKE.msg" = "Connection error: Security validation failed";
"ProdError.error.sec.NET_CLOSED.msg" = "Secure connection interrupted by the server";
"ProdError.error.RecordingError.msg" = "Recording error";
"ProdError.error.network.connectivity.title" = "Poor Network Connection";
"ProdError.error.network.connectivity.msg" = "Make sure you are connected to a stable network and recover your broadcast.";
"ProdError.error.DiskFull.msg" = "There is not enough available storage space to continue the recording";
"VideoBitRate.title" = "Video Bitrate";
"VideoBitRate.value.title" = "Video Bitrate";
"VideoBitRate.mode.title" = "Video Bitrate Mode";
"VideoBitRate.mode.default.title.long" = "Standard Bitrate";
"VideoBitRate.mode.default.title.short" = "Standard";
"VideoBitRate.mode.uniform.title" = "Uniform Bitrate";
"VideoBitRate.mode.uniform.tip" = "The same bitrate is used for all video formats";
"VideoBitRate.mode.adaptative.title" = "Scale Bitrate with Frame Rate and Size";
"VideoBitRate.mode.adaptative.tip" = "For video formats not displayed here above, the bitrate is adjusted to stay proportional to the used frame size and frame rate.";
"VideoBitRate.mode.avc720p.title" = "720p, %d fps (AVC/H.264)";
"VideoBitRate.mode.avc1080p.title" = "1080p, %d fps (AVC/H.264)";
"VideoBitRate.mode.hevc2160p.title" = "4K, %d fps (HEVC)";
"RLBatchViewController.collect-remote-media.title" = "Collect media from remote devices";
"RLBatchViewController.remove-remote-media.title" = "Remove remote media";
"RLBatchViewController.render.title" = "Render final compositions";
"RLBatchViewController.execute.title" = "Execute";
"RLBatchViewController.done.title" = "Operation done";
"RLInfoViewController.title" = "Devices";
"RLInfoViewController.audio-input.title" = "Audio Input";
"RLInfoViewController.audio-output.title" = "Audio Output";
"RLInfoViewController.audio-output.disabled-speaker.title" = "None";
"RLInfoViewController.battery.title" = "Battery";
"RLInfoViewController.storage.title" = "Used Storage";
"RLInfoViewController.cpu-load.title" = "CPU Load";
"RLInfoViewController.capture-size.title" = "Source Size";
"RLInfoViewController.stream-size.title" = "Stream Size";
"RLInfoViewController.stream-type.title" = "Stream Type";
"RLInfoViewController.this-device.title" = "This Device";
"RLInfoViewController.audio-source-drift.title" = "Audio In Drift";
"RLInfoViewController.audio-dest-drift.title" = "Audio Out Drift";
"RLInfoViewController.pkt-loss.title" = "Packet Loss";
"RLInfoViewController.latency.title" = "Network Latency";
"RLInfoViewController.speedtest.title" = "Upload Speed Test";
"RLInfoViewController.speedtest.status" = "Status";
"RLInfoViewController.speedtest.status.idle" = "Idle";
"RLInfoViewController.speedtest.status.inProgress %lld" = "In progress (%lld%%)";
"RLInfoViewController.speedtest.status.success" = "Completed";
"RLInfoViewController.speedtest.status.error" = "Error";
"RLInfoViewController.speedtest.status.skipped" = "Skipped";
"RLInfoViewController.speedtest.status.cancelled" = "Cancelled";
"RLInfoViewController.speedtest.result" = "Result";
"RLInfoViewController.speedtest.result.na" = "N/A";
"RLInfoWarningView.warning.msg" = "Your Wi-Fi network appears to be dropping video data. Switch to Reinforced Wi-Fi mode for a smoother stream.";
"RLInfoWarningView.warning.btn.title" = "Switch Now";
"CameraValidationViewController.device.text" = "Device";
"CameraValidationViewController.perm-msg.text" = "is taking control";
"CameraValidationViewController.perm-allow-button.text" = "Allow";
"CameraValidationViewController.perm-reject-button.text" = "Reject";
"CameraValidationViewController.perm-auto-mode-switch.text" = "Allow Automatically";
"MixerModel.create-new-event.title" = "No Livestream Available";
"MixerModel.create-new-event.subtitle" = "Please create a livestream and try again.";
"MixerModel.create-new-event.button" = "Create New Livestream";
"MixerModel.create-new-event.button.short" = "Schedule";
"MixerModel.no-rtmp-channel.title" = "No RTMP Channel Available";
"MixerModel.no-rtmp-channel.subtitle" = "Please set up an RTMP channel and try again.";
"MixerModel.no-rtmp-channel.button" = "  Set Up RTMP  ";
"MixerModel.create-edit-event.title" = "Edit Livestream";
"MixerModel.audio-warning.title" = "Warning";
"MixerModel.audio-warning.msg" = "Audio is currently disabled or being used by another app.";
"MixerModel.audio-warning.restart" = "Enable Audio";
"OnePaneMixerViewController.stream-validation.msg" = "Stream Validation in Progress...";
"OnePaneCameraViewController.remote-xfer-progress.text" = "Media Transfer";
"OnePaneCameraViewController.menu.title" = "Menu";
"OnePaneCameraViewController.disconnect.title" = "Disconnect from the Switcher";
"OnePaneCameraViewController.disconnect.named.title" = "Disconnect from Switcher \"%@\"";
"OnePaneCameraViewController.disconnect.confirm.msg" = "Do you really want to disconnect from the switcher?";
"OnePaneCameraViewController.disconnect.confirm.named.msg" = "Do you really want to disconnect from switcher \"%@\"?";
"OnePaneCameraViewController.disconnect.confirm.yes" = "Disconnect";
"OnePaneCameraViewController.disconnect.confirm.no" = "Stay Connected";
"OsmoConnectionViewController.title" = "DJI Osmo Mobile";
"OsmoConnectionViewController.searching.title" = "Searching...";
"OsmoConnectionViewController.error.connection.msg" = "Cannot connect to %@";
"MediaCollectionViewController.location.photo-lib.title" = "Photo Library";
"MediaCollectionViewController.media-type.journal.title" = "Journal";
"MediaCollectionViewController.media-type.live.title" = "Live";
"MediaCollectionViewController.media-type.composition.title" = "Composition";
"MediaCollectionViewController.media-type.aux.title" = "Auxiliary Asset";
"MediaCollectionViewController.media-type.unknown.title" = "Unknown Asset";
"MediaCollectionViewController.media-type.special.title" = "Special Asset";
"LocalMediaTableViewController.media-type.unknown.title" = "Unknown Asset";
"LocalMediaTableViewController.media-type.special.title" = "Special Asset";
"LocalMediaTableViewController.type.recorded-audio.title" = "Audio Source";
"LocalMediaTableViewController.type.recorded-video.title" = "Video Source";
"LocalMediaTableViewController.type.recorded-compo.title" = "Composition";
"LocalMediaTableViewController.type.recorded-live.title" = "Live Program";
"LocalMediaTableViewController.type.recorded-clip.title" = "Clip";
"LocalMediaTableViewController.type.imported-audio.title" = "Imported Audio Asset";
"LocalMediaTableViewController.type.imported-video.title" = "Imported Video Asset";
"LocalMediaTableViewController.type.imported-image.title" = "Imported Image";
"LocalMediaTableViewController.photo-imported.title" = "Imported Photos";
"LocalMediaTableViewController.video-imported.title" = "Imported Videos";
"LocalMediaTableViewController.video-recordings.title" = "Local Recordings";
"JournalViewController.remove.nothing.title" = "Nothing can be deleted.";
"JournalViewController.remove.single.title" = "Are you sure you want to delete 1 asset?";
"JournalViewController.remove.multiple.title" = "Are you sure you want to delete %d assets?";
"JournalViewController.remove.msg" = "Auxiliary assets and assets stored in the Photo Library will not be deleted.";
"JournalViewController.remove.single.action" = "Delete 1 Asset";
"JournalViewController.remove.multiple.action" = "Delete %d Assets";
"JournalViewController.send-to.compo.action" = "Composition";
"JournalViewController.send-to.all.action" = "All";
"MediaProcessing.photo-lib.error.no-access" = "This app has no access to your photo library.";
"MediaProcessing.photo-lib.error.failed" = "The transfer to your Photo Library failed.";
"MediaProcessing.photo-lib.success" = "The transfer succeeded.";
"CityProducerViewController.title" = "Send to CTpro";
"CityProducerViewController.local.title" = "CTpro on this Device";
"CityProducerViewController.local-dest.title" = "Apps";
"CityProducerViewController.remote-dest.title" = "Remote Devices";
"CityProducerViewController.missing.msg" = "CTpro is not available on this device.";
"welcome-d3.button.close" = "Close";
"welcome-d3.button.back" = "Back";
"welcome-d3.button.login" = "Log In";
"welcome-d3.button.logout" = "Log Out";
"welcome-d3.button.login.info" = "Log in to access your account";
"welcome-d3.button.create-account" = "Sign Up For Free Trial";
"welcome-d3.button.share-device" = "Share this device";
"welcome-d3.button.switcher-mode" = "Use as Switcher";
"welcome-d3.separator.or" = "OR";
"welcome-d3.button.troubleshoot" = "Trouble Logging In?";
"welcome-d3.field.user" = "Email Address";
"welcome-d3.field.password" = "Password";
"welcome-d3.field.forgot.password" = "Forgot Password?";
"welcome-d3.field.create-account.user.placeholder" = "Enter your Email address";
"welcome-d3.field.create-account.password.placeholder" = "Create password";
"welcome-d3.field.create-account.confirmation.placeholder" = "Confirm password";
"welcome-d3.alert.authentication-error" = "Authentication Error";
"welcome-d3.alert.authentication-connection-error.title" = "No Internet Connection";
"welcome-d3.alert.authentication-connection-error.message" = "Your Internet connection appears to be offline. Check your connection and try again.";
"welcome-d3.alert.authentication-input-error.title" = "Unable to Log In";
"welcome-d3.alert.authentication-input-error.message" = "This email and password combination is incorrect. Please double-check and try again.";
"welcome-d3.message.go-live-on" = "Go Live on";
"welcome-d3.warning.refresh-denied" = "Your session expired or you logged in on another device.";
"welcome-login.text.value-prop" = "**Switcher is where your video lives**";
"welcome-login.button.login-signup" = "Log In or Sign Up";
"welcome-login.button.share" = "Share Camera or Screen";
"account-creation.generic.error" = "";
"account-creation.web-error.error" = "Account creation failed during authentication";
"account-creation.auth-error.error" = "Authentication Error";
"account-creation.no-connection.error" = "Connection failed";
"account-creation.invalid-user.error" = "Invalid Email address";
"account-creation.unmatched-passwords.error" = "Password confirmation doesn't match";
"email-confirmation.title" = "Email Confirmation";
"email-confirmation.text1" = "Check your email to verify your account.";
"email-confirmation.text2" = "<html>We've sent an email with instructions to <a action=\"email\">%@</a>. To confirm, click the link in the message.</html>";
"email-confirmation.resend.title" = "Didn't get an email?";
"email-confirmation.resend.ack" = "Another copy of the email has been sent to %@.";
"sharing-choice.button.share-screen.start" = "Share Screen";
"sharing-choice.button.share-screen.subtitle" = "Use this device's screen as a source in your production.";
"sharing-choice.button.share-screen.stop" = "Stop sharing this screen";
"sharing-choice.button.share-camera.start" = "Share Camera";
"sharing-choice.button.share-camera.subtitle" = "Use this device's camera as a source in your production.";
"waiting-connection.title.share-screen" = "You are sharing this screen";
"waiting-connection.message.waiting-for-connection" = "Waiting for connection";
"waiting-connection.more-informations.title" = "More information";
"waiting-connection.share-camera.message %@" = "Tap this iOS device’s name in the Inputs tab (%@) on your main iOS device.";
"waiting-connection.infos-line1.message" = "Don’t see this camera in the list?";
"waiting-connection.infos-line2.message" = "Tap Add Source, and Connect By URL, then enter this URL: ";
"waiting-connection.infos-line3.message" = "Wi-Fi not available";
"AssetExportToPHLibViewController.phlib.title.label" = "Send to Camera Roll";
"AssetExportToPHLibViewController.ctpro.title.label" = "Send to CTpro";
"AssetExportToPHLibViewController.countmedias.label" = "%d media(s) selected";
"AssetExportToPHLibViewController.move.button" = "Move";
"AssetExportToPHLibViewController.move.description" = "Removing file(s) from Switcher";
"AssetExportToPHLibViewController.copy.button" = "Copy";
"AssetExportToPHLibViewController.copy.description" = "Keeping file(s) in Switcher";
"AssetExportToPHLibViewController.loading.label" = "Please wait until transfer is complete.";
"AssetExportToPHLibViewController.ctpro.opening" = "Opening CTpro";
"ToolLibrary.reorder.title" = "Customize";
"ToolLibrary.reorder.titleForDeleteConfirmationButton" = "Remove";
"ToolLibrary.section.visibleTools" = "Included";
"ToolLibrary.section.hiddenTools" = "More tools";
"ToolLibrary.section.restoreTools" = "";
"ToolLibrary.section.restoreTools.restore-defaults-button.title" = "Restore Default Order";
"ToolLibrary.section.restoreTools.confirmation.msg" = "Are you sure you want to restore the default order?";
"ToolLibrary.section.restoreTools.confirmation.button.title" = "Restore";
"ToolLibrary.tool.input" = "Inputs";
"ToolLibrary.tool.output" = "Outputs";
"ToolLibrary.tool.effect" = "Effects";
"ToolLibrary.tool.camera-control" = "Camera Control";
"ToolLibrary.tool.audio" = "Audio";
"ToolLibrary.tool.comment" = "Live Comments";
"ToolLibrary.tool.more" = "More";
"ToolLibrary.tool.info" = "Info";
"ToolLibrary.tool.encoding" = "Encoding";
"ToolLibrary.tool.star" = "Star";
"ToolLibrary.tool.custom" = "Custom";
"ToolLibrary.tool.react-native" = "React Native";
"ToolLibrary.tool.auto-switch" = "Auto-switch";
"ToolLibrary.tool.development" = "Development Tool";
"ToolLibrary.tool.markers" = "Timestamps";
"RLAudioEngine.SrcLineIn.title" = "Analog Audio Input (Jack)";
"RLAudioEngine.SrcBuiltInMic.title" = "Built-in Microphone";
"RLAudioEngine.SrcWiredInput.title" = "Wired Line/Mic Input";
"RLAudioEngine.SrcBluetoothHFP.title" = "Bluetooth HFP Microphone";
"RLAudioEngine.SrcUSBAudio.title" = "USB/Lightning Audio Input";
"RLAudioEngine.SrcUSBCAudio.title" = "USB Audio Input";
"RLAudioEngine.SrcCarAudio.title" = "Car Audio Input";
"RLAudioEngine.SrcVirtual.title" = "Virtual Audio Input";
"RLAudioEngine.SrcPCI.title" = "PCI Input";
"RLAudioEngine.SrcFireWire.title" = "FireWire Input";
"RLAudioEngine.SrcDisplayPort.title" = "Display Port Input";
"RLAudioEngine.SrcAVB.title" = "Audio Video Bridging";
"RLAudioEngine.SrcThunderbolt.title" = "Thunderbolt Input";
"RLAudioEngine.DestLineOut.title" = "Analog Audio Output (Jack)";
"RLAudioEngine.DestHeadphones.title" = "Headphones/Headset";
"RLAudioEngine.DestBluetoothA2DP.title" = "Bluetooth Output";
"RLAudioEngine.DestBuiltInReceiver.title" = "Built-in Phone Speaker";
"RLAudioEngine.DestBuiltInSpeaker.title" = "Built-in Speaker";
"RLAudioEngine.DestHDMI.title" = "HDMI Output";
"RLAudioEngine.DestAirPlay.title" = "Airplay Output";
"RLAudioEngine.DestBluetoothLE.title" = "Bluetooth LE Output";
"RLAudioEngine.DestBluetoothHFP.title" = "Bluetooth HFP Output";
"RLAudioEngine.DestUSBAudio.title" = "USB Audio Output";
"RLAudioEngine.DestCarAudio.title" = "Car Audio Output";
"RLAudioEngine.DestVirtual.title" = "Virtual Audio Output";
"RLAudioEngine.DestPCI.title" = "PCI Output";
"RLAudioEngine.DestFireWire.title" = "FireWire Output";
"RLAudioEngine.DestDisplayPort.title" = "Display Port Output";
"RLAudioEngine.DestAVB.title" = "Audio Video Bridging";
"RLAudioEngine.DestThunderbolt.title" = "Thunderbolt Output";
"RLAudioEngine.unknown.title" = "Unknown";
"CloudAssetsListViewController.title" = "Switcher Cloud";
"CloudAssetsListViewController.edit.title" = "Select";
"CloudAssetsListViewController.error.title" = "Could not load assets from Cloud : %@";
"CloudAssetsListViewController.switcher-filetype.mmsrc.title" = "Audio/Video";
"CloudAssetsListViewController.switcher-filetype.mmvideo.title" = "Video";
"CloudAssetsListViewController.switcher-filetype.mmaudio.title" = "Audio";
"CloudAssetsListViewController.switcher-filetype.mmfx.title" = "Effect";
"CloudAssetsListViewController.switcher-filetype.mmart.title" = "Artwork";
"CloudAssetsListViewController.switcher-filetype.mmjrnl.title" = "Journal";
"CloudAssetsListViewController.switcher-filetype.undefined.title" = "Switcher file undefined";
"CloudAssetsListViewController.switcher-filetype.image.title" = "Image";
"CloudAssetsListViewController.switcher-filetype.movie.title" = "Movie";
"CloudAssetsListViewController.switcher-filetype.audio.title" = "Audio";
"CloudAssetsListViewController.no-asset.title" = "No asset available.";
"CloudAsseUploadViewController.title.label" = "Upload assets to Switcher Cloud";
"CloudAssetUploadViewController.countmedias.label" = "%d asset(s) selected";
"CloudAssetUploadViewController.success.label" = "Upload %d asset(s) with success";
"CloudAssetUploadViewController.failed.label" = "Upload to Switcher Cloud failed";
"CloudAssetUploadViewController.failed.max-assets.label" = "You have exceeded the allowed number of assets on Switcher Cloud";
"CloudVideoStorageView.text.primary.mid" = "Storage limit approaching";
"CloudVideoStorageView.text.primary.high" = "Storage limit reached";
"CloudVideoStorageView.text.studio.attributed" = "You are approaching your overall storage limit of video uploads. Upgrade Your Plan";
"CloudVideoStorageView.text.studio.limit.attributed" = "You have reached your overall storage limit of video uploads. Upgrade Your Plan";
"CloudVideoStorageView.text.attributed.substring" = "Upgrade Your Plan";
"CloudVideoStorageView.text.business.attributed" = "You are approaching your overall storage limit of video uploads. Delete videos or contact us for more storage";
"CloudVideoStorageView.text.business.limit.attributed" = "You have reached your overall storage limit of video uploads. Delete videos or contact us for more storage";
"CloudVideoStorageView.text.business.attributed.substring" = "contact us for more storage";
"CloudVideoStorageView.text.studio.no.sub.attributed" = "You are approaching your overall storage limit of video uploads. Visit your account at switcherstudio.com to manage your subscription.";
"CloudVideoStorageView.text.studio.no.sub.limit.attributed" = "You have reached your overall storage limit of video uploads. Visit your account at switcherstudio.com to manage your subscription.";
"CloudVideoStorageView.text.studio.no.sub.attributed.substring" = "switcherstudio.com";

"CloudVideoStorageView.progress.text %lld %lld" = "%lld of %lld";
"SelectMenuManager.edit.title" = "Select";
"D3UserMainViewController.badge.dev" = "Active Profile";
"D3UserMainViewController.badge.trial" = "Trial";
"D3UserMainViewController.badge.limited-access" = "Expired Account";
"D3UserMainViewController.badge.dev.alert" = "You have an active profile.\napiUrl: %@\nUser: %@\nSign Out > Menu > Profile Selection to change profile";
"D3UserMainViewController.badge.trial.alert" = "You’re currently on a free 14-day trial that gives you temporary access to all of Switcher’s features. Videos you create during the trial will include Switcher’s watermark. For questions about using Switcher after your trial and removing the watermark, please contact our support <NAME_EMAIL>.";
"UserMainView.message" = "Hi there!\n What do you want to create today?";
"UserMainView.Creation.title" = "Go Live or Record Video";
"UserMainView.Creation.text" = "Use our full suite of live editing and multi-cam tools.";
"UserMainView.horizontal" = "Horizontal (16:9)";
"UserMainView.vertical" = "Vertical (9:16)";
"UserMainView.ratio" = "VIDEO RATIO";
"AssetProdCollectionViewController.title" = "Assets";
"AssetProdCollectionViewController.new" = "New";
"AssetProdCollectionViewController.choice.photo" = "Photo";
"AssetProdCollectionViewController.choice.video" = "Video";
"AssetProdCollectionViewController.choice.audio" = "Audio";
"AssetProdCollectionViewController.choice.shopping" = "Card";
"AssetProdCollectionViewController.choice.text-graphics" = "Text & Graphics";
"AssetProdCollectionViewController.choice.multiviews" = "Multiview";
"AssetProdCollectionViewController.choice.cloud" = "Switcher Cloud";
"AssetProdCollectionViewController.choice.camera" = "Input";
"AssetProdCollectionViewController.choice.logo" = "Logo";
"AssetProdCollectionViewController.choice.imagesoverlay" = "Image Overlays";
"AssetProdCollectionViewController.choice.lowerthird" = "Lowerthird";
"AssetProdCollectionViewController.choice.image-overlay" = "Image Overlay";
"MediaListTableViewController.photo.title" = "Photo";
"MediaListTableViewController.video.title" = "Video";
"MediaListTableViewController.audio.title" = "Audio";
"MediaListTableViewController.choice.audio-from-video" = "Audio From Video";
"MediaListTableViewController.choice.audio-imported" = "Imported Audio";
"MediaListTableViewController.choice.photo-library" = "Photo Library";
"MediaListTableViewController.choice.photo-imported" = "Imported Images";
"MediaListTableViewController.choice.video-imported" = "Imported Videos";
"MediaListTableViewController.choice.my-recordings" = "Local Recordings";
"MediaListTableViewController.choice.samples" = "Samples";
"MediaListTableViewController.choice.switcher-cloud" = "Switcher Cloud";
"MediaListTableViewController.choice.ios-picker" = "Files/USB Drive";
"MediaListTableViewController.choice.no-image" = "No image";
"MediaListTableViewController.choice.backgrounds" = "Backgrounds";
"MediaListTableViewController.choice.gradient" = "Gradient";
"MediaListTableViewController.choice.platform-icons" = "Platform Icons";
"MediaListTableViewController.choice.patterns" = "Patterns";
"MediaListTableViewController.choice.frames" = "Frames";
"GradientGeneratorViewController.title" = "Gradient";
"GradientGeneratorViewController.params.main-color" = "Main color";
"GradientGeneratorViewController.params.nuance" = "Nuance";
"GradientGeneratorViewController.params.brightness" = "Brightness";
"GradientGeneratorViewController.params.orientation" = "Orientation";
"MediaListTableViewController.social-icons.title" = "Socials";
"MediaListTableViewController.choice.social.facebook" = "Facebook";
"MediaListTableViewController.choice.social.instagram" = "Instagram";
"MediaListTableViewController.choice.social.youtube" = "YouTube";
"MediaListTableViewController.choice.social.twitter" = "Twitter";
"MediaListTableViewController.choice.social.linkedin" = "LinkedIn";
"MediaListTableViewController.donation-icons.title" = "Donations";
"MediaListTableViewController.choice.donation.cashapp" = "Cashapp";
"MediaListTableViewController.choice.donation.givelify" = "Givelify";
"MediaListTableViewController.choice.donation.patreon" = "Patreon";
"MediaListTableViewController.choice.donation.paypal" = "Paypal";
"MediaListTableViewController.choice.donation.tithely" = "Tithely";
"MediaListTableViewController.choice.donation.venmo" = "Venmo";
"AssetGridViewController.photo.title" = "All photos";
"AssetGridViewController.video.title" = "All videos";
"AlbumListTableViewController.title" = "Albums";
"AlbumListTableViewController.section.smart-albums" = "Smart Albums";
"AlbumListTableViewController.section.user-albums" = "User Albums";
"AlbumListTableViewController.section.icloud-albums" = "iCloud Photos";
"AlbumListTableViewController.section.empty.title" = "No album available";
"SamplesCollectionViewController.title" = "Samples";
"SamplesCollectionViewController.choice.pattern" = "Test Pattern";
"SamplesCollectionViewController.choice.cornerbug" = "Logo";
"SamplesCollectionViewController.choice.slide" = "Slide";
"SamplesCollectionViewController.choice.image-fullscreen" = "Photo";
"SamplesCollectionViewController.choice.video-cereals" = "Cereals";
"SamplesCollectionViewController.choice.video-ocean" = "Ocean";
"BackgroundsCollectionViewController.title" = "Backgrounds";
"OpacityViewManager.loading.title" = "Loading...";
"TextAndGraphicsListTableViewController.title" = "Text & Graphics";
"TextAndGraphicsListTableViewController.choice.title" = "Titles";
"TextAndGraphicsListTableViewController.choice.lower-third" = "Lower Thirds";
"TextAndGraphicsListTableViewController.choice.animated-text" = "Animated Text";
"TextAndGraphicsListTableViewController.choice.timers" = "Timers and Stopwatches";
"TimersCollectionViewController.timers.stopwatches.section" = "Timers and Stopwatches";
"TimersCollectionViewController.timers.section" = "Timers Only";
"TextAndGraphicsListTableViewController.choice.platform-overlay" = "Platform Overlays";
"TextAndGraphicsListTableViewController.choice.broadcast-notification" = "Broadcast Notifications";
"TextAndGraphicsListTableViewController.choice.corner-bug" = "Logos - Corner Bugs";
"TextAndGraphicsListTableViewController.choice.image-overlay" = "Image Overlays";
"TextAndGraphicsListTableViewController.choice.slideshow" = "Image Slideshow";
"TextAndGraphicsListTableViewController.choice.image-lower-third" = "Image as Lower Third";
"MultiviewsCollectionViewController.title" = "Multiviews";
"MultiviewsCollectionViewController.choice.dashboard" = "Dashboard";
"MultiviewsCollectionViewController.choice.split-screen" = "Split Screen";
"MultiviewsCollectionViewController.choice.flap" = "Flap";
"MultiviewsCollectionViewController.choice.slots" = "Slots";
"MultiviewsCollectionViewController.choice.picture-in-picture" = "Picture-in-Picture";
"MultiviewsCollectionViewController.choice.grid" = "Grid";
"BaseProdFlow.alert.cloud.available-soon" = "Available Soon";
"AudioSettingTableViewController.title" = "Audio Settings";
"AudioSettingsTableViewController.boost-section.title" = "Boost";
"AudioSettingsTableViewController.boost-section.value-template" = "%d dB";
"AudioSettingsTableViewController.routing-section.title" = "Routing";
"AudioSettingsTableViewController.routing-section.none" = "None";
"AudioSettingsTableViewController.routing-section.leftToMono" = "Left to mono";
"AudioSettingsTableViewController.routing-section.rightToMono" = "Right to mono";
"AudioSettingsTableViewController.routing-section.sumToMono" = "Sum to mono";
"LimitedAccessViewController.header" = "Expired Account";
"LimitedAccessViewController.body" = "Your account has expired.\
\
<NAME_EMAIL>\
with any questions.";
"LimitedAccessViewController.support-button" = "Contact Support";
"SubscriptionExpiredAlert.header" = "Expired Account";
"SubscriptionExpiredAlert.body" = "Your account has expired.\n\nPlease contact \n[<EMAIL>](mailto:<EMAIL>)\nwith any questions.";
"NetworkUnavailableAlert.header" = "No Internet Connection";
"NetworkUnavailableAlert.body" = "Your Internet connection appears to be offline. Check your connection and try again.";
"StorageLimitAlert.header" = "Attention";
"StorageLimitAlert.body %lld %lld" = "You have reached your overall storage limit of video uploads (%lld of %lld). Delete videos to free up space before uploading more.";
"StorageLimitAlert.button.manage" = "Manage Video Library";
"StorageLimitAlert.button.dismiss" = "Dismiss";
"PHAssetCustomError.notSupported.msg" = "Format not supported";
"PHAssetCustomError.notLocal.msg" = "Not a local file";
"PHAssetCustomError.noValidSource.msg" = "Not a valid source";
"PHAssetCustomError.notResolvedUrl.msg" = "The url cannot be resolved";
"PHAssetCustomError.noAVAsset.msg" = "No AVasset available";
"PHAssetCustomError.cancelRequest.msg" = "The request has been cancelled by the user";
"MyRecordingHeaderView.sort.date" = "By Date";
"MyRecordingHeaderView.sort.name" = "By Name";
"CloudyToolViewController.progress.msg" = "Downloading...";
"CloudyToolViewController.not-available.msg" = "Not Available";
"Asset.in-photo-library.single" = "Photo Library Asset";
"Asset.in-photo-library.multiple" = "Photo Library Assets";
"ColorPickerViewControllerV2.title" = "Color";
"ColorPickerViewControllerV2.selector.palette" = "Palette";
"ColorPickerViewControllerV2.selector.rgb" = "RGB";
"ColorPickerViewControllerV2.selector.predefined-colors" = "Swatches";
"ColorPickerViewControllerV2.css-color-code.alert.title" = "CSS Color Code";
"ColorPickerViewControllerV2.css-color-code.alert.placeholder" = "CSS Color Code";
"ColorPickerViewControllerV2.rgb-sliders.red-label" = "R";
"ColorPickerViewControllerV2.rgb-sliders.green-label" = "G";
"ColorPickerViewControllerV2.rgb-sliders.blue-label" = "B";
"DiagInformationsTableViewController.informations.title" = "Diagnostic";
"DiagInformationsTableViewController.version.title" = "Application Version";
"DiagInformationsTableViewController.build-number.title" = "Application Build Number";
"DiagInformationsTableViewController.from.title" = "Origin";
"DiagInformationsTableViewController.tool-bundle-version.title" = "Tool Bundle Version";
"DiagInformationsTableViewController.tool-api-version.title" = "Tool Api Version";
"DiagInformationsTableViewController.sessions.title" = "Sessions";
"DiagInformationsTableViewController.log-activation.title" = "Diagnostic Logs";
"DiagInformationsTableViewController.log-activation.enable.title" = "Logs";
"DiagInformationsTableViewController.log-activation.enable.msg" = "The diagnostic log will be enabled on the next app launch for next %d hours. This helps R&D and support teams to analyse issues that may occur.";
"DiagInformationsTableViewController.log-activation.disable.title" = "Stop Logging";
"DiagInformationsTableViewController.log-activation.disable.msg" = "The diagnostic log will be disabled on the next app launch.";
"DiagInformationsTableViewController.log-activation.currently-enable.msg" = "The diagnostic log is currently enabled. This helps R&D and support teams to analyse issues that may occur.";
"AutoSwitchTableViewController.title" = "Auto-switch";
"AutoSwitchTableViewController.sources.header.title" = "Sources";
"AutoSwitchTableViewController.actions.header.title" = "Actions";
"AutoSwitchTableViewController.settings.header.title" = "Settings";
"AutoSwitchTableViewController.sources.no-source.title" = "No Source";
"AutoSwitchTableViewController.action-item.start.title" = "Start";
"AutoSwitchTableViewController.action-item.stop.title" = "Stop";
"AutoSwitchTableViewController.action.playing" = "Live";
"AutoSwitchTableViewController.settings.transition-timing.title" = "Interval";
"AutoSwitchTableViewController.settings.shuffle.title" = "Shuffle";
"LoaderInAppViewController.button.ok" = "OK";
"AudioEditorViewController.title" = "Editor";
"AudioEditorViewController.volume.title" = "Volume";
"AudioEditorViewController.volume.value-db" = "%@ dB";
"AudioEditorViewController.thumbnail.tag.title" = "Tag";
"AudioEditorViewController.thumbnail.tag.placeholder" = "No text";
"AudioEditorViewController.loop.title" = "Loop";
"AudioEditorViewController.loop.description" = "When loop is enabled, the audio asset will play continually until removed from screen.";
"MediaPlayerVideoCore.clips.button.title" = "Clips";
"ClipsTableViewController.title" = "Clips";
"ClipsTableViewController.clip-list.header.title" = "List of clips";
"ClipsTableViewController.clip-list.no-clip.title" = "No clip";
"MultiviewsProdFlow.group-1.title" = "1 View";
"MultiviewsProdFlow.group-2.title" = "2 Views";
"MultiviewsProdFlow.group-3.title" = "3 Views";
"MultiviewsProdFlow.group-4.title" = "4 Views";
"MultiviewsProdFlow.group-5.title" = "5 Views";
"MultiviewsProdFlow.group-6.title" = "6 Views or More";
"ElementTransitions.title" = "In & Out Transition";
"ElementTransitions.toggle.title" = "Enable Transition";
"ElementTransitions.toggle.hint" = "When enabled, this will override the main transition settings.";
"ElementTransitions.select.title" = "Transition";
"ElementTransitions.select.hint" = "To adjust transition properties, please go to the Transitions tool.";
"ElementGroupsViewController.elementGroups.title" = "Production Groups";
"ElementGroupsViewController.section.existingGroups" = "Existing Groups";
"ElementGroupsViewController.section.addNewGroup.button.title" = "Add New Group";
"ElementGroupsViewController.groups.mainGroup" = "Main";
"ElementGroupsViewController.groups.delete.elements" = "This production group contains assets. What do you want to do?";
"ElementGroupsViewController.groups.delete.all" = "Delete Production Group and Assets";
"ElementGroupsViewController.groups.delete.collection" = "Delete Production Group Only";
"ZoomSliderViewController.editSections.title" = "Edit Groups";
"Clips.short-description" = "Clips are modified versions of the original recording.";
"Clips.long-description" = "Clips are modified versions of the original recording. They can be trimmed, slowed down and sped up.";
"Clips.edit-original-asset.tip" = "Please create a new clip and edit it as needed.";
"Clips.create-new.title" = "Create New Clip";
"Clips.extract.title" = "Create New Clip (Trim)";
"CamoHelpViewController.title" = "Switcher as a Webcam";
"CamoHelpViewController.instructions.intro" = "You can output Switcher audio and video into Zoom, Google Meet, Teams, or any other video conferencing tool running on your Mac or PC. Here is how:";
"CamoHelpViewController.instructions.1.0" = "Download and run Camo Studio on your Mac or PC.";
"CamoHelpViewController.instructions.1.1" = "Download Camo Studio on camo.studio website.";
"CamoHelpViewController.instructions.1.share-button" = "Share Download Link";
"CamoHelpViewController.instructions.2.0" = "Connect this iPad/iPhone to your Mac or PC.";
"CamoHelpViewController.instructions.2.1" = "Use a reliable cable (such as the one that came with your iPhone). Avoid using a hub.";
"CamoHelpViewController.instructions.3.0" = "Follow the instructions in Camo Studio.";
"CamoHelpViewController.instructions.footnote" = "If you want to hear the other participants, connect headphones to your computer. Otherwise, turn off your computer speakers to avoid echo and audio feedback.";
"news.since-version" = "13.7.0";
"news.title" = "What's New";
"news.version-prefix" = "Version";
"news.description" = "# NEW\
* NEW slideshow asset, a built-in way to loop through a set of logos or images\
* Check the available storage before starting a live with local recording\
* Bug fixes and stability improvements";
"SourcePropBundleEditor.overlay.tips" = "An overlay is displayed on top of a full-screen asset or video source.";
"SourcePropBundleEditor.chromakey.tips" = "Select the color to be removed or replaced with your background image.";
"SourcePropBundleEditor.align.vertical" = "Vertical";
"SourcePropBundleEditor.align.horizontal" = "Horizontal";
"MultiviewBehavior.title" = "Slot Assignment";
"MultiviewBehavior.description" = "Choose how the source slots are assigned.";
"MultiviewBehavior.multiview.title" = "Select Each Time";
"MultiviewBehavior.multiview.description" = "Each time you choose this Multiview, you'll be prompted to select sources or assets to fill each slot.";
"MultiviewBehavior.scene.title" = "Preselect";
"MultiviewBehavior.scene.description" = "You'll first select this Multiview's sources in the previous Multiview Properties window (camera and screen sources only). Then, every time you choose this Multiview, all slots will automatically be filled with your selections.";
"MultiviewBehavior.overlay.title" = "Reuse Current Source";
"MultiviewBehavior.overlay.description" = "You'll first select all but one of this Multiview's sources in the previous Multiview Properties window. Then, every time you choose this Multiview, the final remaining slot will automatically be filled with your current (last-selected) source. Tap a new source to change the final slot, or re-tap the Multiview to remove it and display only your selected source.";
"MultiviewBehavior.special-inputs.unknown.title" = "Unknown";
"MultiviewBehavior.special-inputs.unassigned.title" = "None";
"MultiviewBehavior.special-inputs.overlay-background.title" = "Current Source";
"MultiviewBehavior.special-inputs.image.title" = "Image";
"MultiviewBehavior.special-inputs.title.title" = "Title";
"MultiviewBehavior.special-inputs.video.title" = "Video";
"IntroGenerator.editor.title" = "Create intro";
"IntroGenerator.selection.title" = "Intro video";
"IntroGenerator.processing.title" = "Please wait while exporting introduction video";
"IntroGenerator.processing.error" = "Unable to create intro video";
"ShoppingListViewController.title" = "Card";
"ShoppingList.choice.camera-as-background" = "Camera as background";
"ShoppingList.choice.image-as-background" = "Image as background";
"ShoppingList.choice.multiview" = "2 views";
"ShoppingList.choice.live-selling-samples" = "Live selling samples";
"ShoppingList.submenu.title" = "Card";
"clip.export.title" = "Please wait while we finalize your clip";
"clip.export.error" = "Error while creating the clip.";
"clip.export.retry" = "Retry";
"clip-image-select" = "Choose...";
"clip-trim-start %@" = "Start: %@";
"clip-trim-end %@" = "End: %@";
"clip-save" = "Save";
"clip-speed" = "Speed";
"clip-family-font" = "Font Family";
"clip-format" = "Format";
"clip-layers" = "Layers";
"clip-image" = "Image";
"clip-add-image" = "Add Image";
"clip-edit-image" = "Edit Image";
"clip-progress" = "Progress Bar";
"clip-add-progress" = "Add Progress Bar";
"clip-edit-progress" = "Edit Progress Bar";
"clip-title" = "Title";
"clip-add-title" = "Add Title";
"clip-edit-title" = "Edit Title";
"clip-final-duration" = "Final Duration";
"clip-reset-speed" = "Reset Speed";
"clip-layer %lld" = "Layers (%lld)";
"clip-add-layer" = "Add Layer";
"clip-no-layer" = "There is no layer yet";
"clip-tap-edit" = "Text (tap to edit)";
"clip-font" = "Font";
"clip-background" = "Background";
"clip-position" = "Position";
"clip-adjust" = "Edit";
"clip-add" = "Add";
"clip-apply" = "Apply";
"clip-create" = "Create";
"clip-framing" = "Framing";
"clip-zoom" = "Zoom";
"clip-background-color" = "Background Color";
"clip-layer-edit" = "Edit";
"clip-layer-delete" = "Delete";
"clip-image-size" = "Size";
"clip-dimension" = "Dimension";
"clip-height" = "Height";
"clip-font-family" = "Family";
"clip-font-color" = "Color";
"clip-font-alignment" = "Alignment";
"clip-font-size" = "Size";
"clip-position-horizontal" = "Horizontal";
"clip-position-vertical" = "Vertical";
"clip-position-spacing" = "Spacing";
"clip-color" = "Color";
"clip-speed-info %lf" = "%.1lfx";
"clip-caption-title" = "Captions";
"clip-caption-auto-title" = "Generating Captions";
"clip-caption-auto-subtitle" = "We use speech recognition on your video to generate captions automatically. The recognition is offline and your data stays on your phone.";
"clip-caption-auto-no-language" = "There is no language available.";
"clip-caption-auto-language-subtitle" = "Please pick the language spoken in your video so we can transcribe it correctly.";
"clip-caption-ask-permission-detail" = "To start auto captioning, you need first to give your permission to use speech recognition. Please click first on \"Start Recognition\" to give your permission.";
"clip-caption-ask-permission" = "Start Recognition (Ask Permission)";
"clip-caption-permission-denied" = "Open Settings";
"clip-caption-permission-denied-detail" = "Access to speech recognition is disabled. You can open Settings page and then enable Speech Recognition.";
"clip-caption-adjust-warning" = "You have captions attached to this video. If you trim your video, all captions will be deleted.";
"clip-caption-auto-error" = "Oops! An error occurred in transcription service.";
"clip-caption-auto-error-nocaption" = "Oops! No caption detected in the audio.";
"clip-caption-auto %lld" = "%lld caption(s) generated";
"clip-caption-auto-analyze" = "Speech recognition start process...";
"clip-caption-auto-success" = "Process Succeeded";
"clip-caption-auto-cancel" = "Process Cancelled";
"clip-caption-auto-start" = "Generate Transcript";
"transcript-generate" = "Generate";
"clip-caption-auto-waiting" = "Please wait, speech recognition is in progress. Do not close the application.";
"clip-caption-auto-manual" = "Add Caption Manually";
"clip-caption-use-existing" = "Use Existing Transcript";
"clip-caption-home-info" = "Drag the timeline and press \"+\" to add a caption at this position.";
"clip-caption-edit-title" = "Edit Caption";
"clip-caption-edit-info" = "Type your text here";
"clip-caption-style-title" = "Style";
"clip-caption-fake" = "Your subtitles will appear in this box";
"clip-caption-srt-mode" = "Edit SRT";
"clip-caption-speech-recognition" = "Run Speech Recognition";
"clip-caption-select-language" = "Please choose a language";
"clip-caption-srt-import" = "Import";
"clip-caption-srt-export" = "Export";
"AccountInfos.name" = "Name";
"AccountInfos.email" = "Email";
"AccountInfos.expiration-date.days-counting.%d" = "Expires in %d days";
"AccountInfos.expiration-date.expired.title" = "Expired";
"AccountInfos.subscription-active.title" = "Subscription active";
"AccountInfos.subscription-canceled.title" = "Subscription canceled";
"AccountInfos.subscription-error.title" = "Action required";
"AccountInfos.subscription-error.message" = "Oops! An error occurred with your method of payment. Please log in to the Dashboard to resolve this issue and continue using Switcher uninterrupted.";
"AccountProductActive.expiration-date.title" = "Expires on";
"AccountProductActive.renewable-date.title" = "Renews on";
"SubscriptionTableViewController.active-plan.title" = "Active plan";
"SubscriptionTableViewController.available-plans.title" = "Available plans";
"BrandProfileSubtableController.name" = "Brand Profile";
"BrandProfileSubtableController.action.apply" = "Apply";
"BrandProfileSubtableController.action.configure" = "Configure";
"PaletteSubtableController.name" = "Theme";
"PaletteSubtableController.custom" = "Custom";
"PaletteSubEditor.title" = "Theme";
"PaletteSubEditor.section.brand-profile" = "Brand Profile";
"PaletteSubEditor.section.generic" = "Generic";
"PaletteSubEditor.action.shuffle" = "Shuffle Selected Colors";
"target-url.ack.title" = "Configuration Notice";
"target-url.ack.dest" = "Switcher Studio is ready to stream to the following destination:";
"target-url.ack.tip.no-user" = "Please close this message, log into your Switcher account, then select \"Horizontal\" or \"Vertical\" and press the 🔴 button to go live.";
"target-url.ack.tip.user" = "Please close this message, select \"Horizontal\" or \"Vertical\" and press the 🔴 button to go live.";
"Settings.Guide.SafeArea" = "Safe Area Guides";
"welcome-d3.button.manage-subscription" = "Manage Subscription";
"welcome-d3.button.delete-account" = "Delete Account";
"tutorials.getstarted" = "Featured Tutorials";
"tutorials.title" = "View Featured Tutorials";
"tutorials.all.information" = "These videos will help you get started with some of Switcher Studio's most popular features. For more tutorials, visit our Help Center.";
"tutorials.all.title" = "Tutorial Videos";
"filter-list.vfilter-cross-zoom.title" = "Cross Zoom";
"filter-list.vfilter-cover.title" = "Cover";
"filter-list.vfilter-push.title" = "Push";
"filter-list.vfilter-split.title" = "Split";
"filter-list.vfilter-iris.title" = "Iris";
"filter-list.vfilter-star_wipe.title" = "Star Wipe";
"filter-list.vfilter-swipe.title" = "Swipe";
"filter-list.vfilter-fade.title" = "Fade";
"filter-list.vfilter-flicker.title" = "Flicker";
"filter-list.vfilter-shatter.title" = "Shatter";
"filter-list.vfilter-rolling.title" = "Rolling";
"filter-list.vfilter-whip_pan.title" = "Whip Pan";
"filter-list.vfilter-stinger.title" = "Stinger";
"vfilter-prop.fade_out_duration.title" = "Fade Out Duration";
"vfilter-prop.wait_duration.title" = "Wait Duration";
"vfilter-prop.fade_in_duration.title" = "Fade In Duration";
"vfilter-prop.fade_in_title" = "Fade In";
"vfilter-prop.fade_out_title" = "Fade Out";
"vfilter-prop.fade_setting.title" = "Audio Fading";
"vfilter-prop.logo.title" = "Logo";
"vfilter-prop.background_color.title" = "Background Color";
"vfilter-prop.external_border_color.title" = "External Border Color";
"vfilter-prop.internal_border_color.title" = "Internal Border Color";
"vfilter-prop.scale_flap.title" = "Flap Scale";
"vfilter-prop.scale_inset.title" = "Inset Scale";
"vfilter-prop.margin_flap.title" = "Flap Margin";
"vfilter-prop.margin_inset.title" = "Inset Margin";
"vfilter-prop.invert_flap.title" = "Invert Flap Position";
"vfilter-prop.zoom_max.title" = "Zoom";
"vfilter-prop.uncover.title" = "Uncover";
"vfilter-prop.reverse.title" = "Reverse";
"PlaybackSettingsSubEditorView.title" = "Playback Settings";
"PlaybackSettingsSubEditorView.play_once" = "Play Once";
"PlaybackSettingsSubEditorView.infinite" = "Infinite Loop";
"PlaybackSettingsSubEditorView.loop_count" = "Loop Count";
"PlaybackSettingsSubEditorView.loop_duration" = "Display Duration";
"PlaybackSettingsSubEditorView.repeat %d" = "Repeat %lldx";
"PlaybackSettingsSubEditorView.play_for %@" = "Play for %@";
"PlaybackSettingsSubEditorView.times" = "Times";
"AssetGridViewController.photoLibrary.manage.title" = "Manage";
"AssetGridViewController.photoLibrary.limitedPhotos.title" = "You've given Switcher access to only a selected number of photos.";
"asset.collection.no.asset" = "No asset";
"asset.collection.reorder" = "Drag & Drop to reorder";
"asset.collection.hidden.sing" = "You have 1 hidden production group.";
"asset.collection.hidden %lld" = "You have %lld hidden production groups.";
"asset.collection.manage" = "Manage";
"asset.collection.disconnect" = "Disconnect";
"asset.collection.delete.confirm.title" = "Are you sure?";
"asset.collection.delete.confirm.message.plur" = "You're about to delete %d assets.";
"asset.collection.delete.confirm.message.sing" = "You're about to delete 1 asset.";
"asset.collection.delete.confirm.button" = "Yes, delete 1 asset";
"asset.collection.delete" = "Delete";

"export.extension.title" = "Use in Switcher";
"export.extension.import.as.assets.title" = "Import as Assets";
"export.extension.import.as.assets.subtitle" = "Add images or videos to use as full-screen or overlaid assets in your productions.";
"export.extension.import.as.assets.button" = "Import";
"export.extension.upload.to.library.title" = "Upload to Video Library";
"export.extension.upload.to.library.subtitle" = "Store, edit video details, and manage your content in the Cloud Video Library.";
"export.extension.upload.to.library.button" = "Upload";
"export.upload.to.library.success" = "Videos added to your Uploads";

"export.extension.send.image.to.switcher %lld" = "Import %lld Images";
"export.extension.send.image.to.switcher" = "Import Image";
"export.extension.send.video.to.switcher %lld" = "Import %lld Videos";
"export.extension.send.video.to.switcher" = "Import Video";

"export.extension.orientation" = "Orientation";
"export.extension.thumbnail.selection" = "Select Thumbnail";
"export.extension.thumbnail.tag" = "Thumbnail Tag";
"export.extension.video.audio-only" = "Audio Only";

"export.image.import.success %lld" = "%lld images imported successfully";
"export.image.import.success %lld %lld" = "%lld / %lld images imported successfully";
"export.image.import.success" = "Image imported successfully";

"export.video.import.success %lld" = "%lld videos imported successfully";
"export.video.import.success %lld %lld" = "%lld / %lld videos imported successfully";
"export.video.import.success" = "Video imported successfully";

"export.mixed-content.warning" = "We have a little problem . . .\n\
In order to batch import to Switcher Studio, please ensure your selections are all the same media type.";
"export.multiple-videos.warning" = "We have a little problem...\n\
In order to upload to the Video Library, please ensure you've selected only one video.\
Uploading multiple videos simultaneously is unsupported at this time.";

"tag.golive" = "Go live or start recording to add a timestamp.";
"tag.notag" = "No timestamp";
"tag.manual" = "Manage";
"tag.automatic" = "Automatic";
"tag.add.manual.tag" = "Add New Manual Timestamp";
"tag.manage" = "Manage";
"tag.count %lld" = "All Timestamps (%lld)";
"tag.toast.message" = "Timestamp added";
"tag.manual.description" = "Manual Timestamp (%@)";
"tag.jump.to" = "Jump to...";

"summary.prod.title" = "Your video is ready!";
"summary.prod.share.link" = "Share Broadcast Link";
"summary.prod.export.record" = "Share Recorded File";
"summary.prod.create.clip" = "Create Clip";
"summary.prod.director.mode" = "Director Mode Files";
"summary.prod.leave.studio" = "Leave Studio";
"summary.prod.loading" = "One moment — your video is processing.";

"summary.clip.title" = "Your clip is ready!";
"summary.clip.loading" = "One moment — your clip is processing.";
"summary.tooltip.timestamp" = "Use your timestamps to jump to moments you want to clip.";
"summary.tooltip.videolibrary" = "NEW: Upload your recording to your Video Library, and then visit the Dashboard to add it to your Switcher Player.";
"clip.tooltip.timestamp" = "Review 1 timestamp";
"clip.tooltip.timestamps %lld" = "Review %lld timestamps";

"exporter.title" = "Share";
"exporter.more.options" = "More Sharing Options...";
"exporter.poto.library" = "Send to Camera Roll";
"exporter.files" = "Save to Files";
"exporter.city.producer" = "Send to CTpro";

"ProdSummaryViewController.state1.cancel.confirmation.title" = "You’re so close! Are you sure you want to cancel?\
 Your video is still transferring to the site on which it’s playing.\
 Canceling now will cut your stream short.";
"ProdSummaryViewController.state1.cancel.button.confirmation.title" = "Yes, cancel";
"ProdSummary.videolibrary.info" = "Uploaded to your Video Library";
"ProdSummary.localrecording.info" = "Saved in Local Recordings";
"ProdSummary.practice.title" = "Practice Livestream Complete";
"ProdSummary.practice.text" = "A recording can be privately watched via your online Dashboard at **switcherstudio.com** for the next 14 days.";
"ProdSummary.practice.go.home" = "Go Back Home";

"clip.trim.start" = "Begin Clip";
"clip.trim.end" = "End Clip";
"clip.caption.trim.start" = "Begin Caption";
"clip.caption.trim.end" = "End Caption";


"auto-dismiss.property.enable.title" = "Auto Dismiss";
"auto-dismiss.property.delay.title" = "Display Duration";
"auto-dismiss.property.delay.none" = "Disabled";
"auto-dismiss.property.tip" = "Auto Dismiss removes timers, prerecorded videos, and non-looping animated text from the screen upon completion.";
"auto-dismiss.delay-editor.title" = "Display Duration";
"auto-dismiss.delay-editor.enable.title" = "Enable";
"auto-dismiss.delay-editor.delay.header" = "Duration";
"auto-dismiss.delay-editor.delay.tip" = "Display Duration determines how long images, lower thirds, titles, logos, social and donation overlays, and looping animated text are shown on screen.";
"sharing-choice.button.share-seemo.start" = "Share SeeMo";
"sharing-choice.button.share-seemo.subtitle" = "Use the HDMI camera as a source in your production.";
"sharing-choice.button.share-ext-camera.start" = "Share USB Video Source";
"sharing-choice.button.share-ext-camera.subtitle" = "Use the USB Webcam or Capture Device in your production.";

"seemo-settings.title" = "Accsoon SeeMo Settings";
"seemo-settings.latency" = "HDMI Camera Latency";
"seemo.settings.footer" = "This setting only applies to the camera connected to this device through Accsoon SeeMo.";
"seemo.addsource.hdmi.title" = "HDMI Camera/Source via Accsoon SeeMo";
"seemo.addsource.helper.description" = "Use Accsoon SeeMo to connect DSLR cameras and other HDMI sources to Switcher.\r\r\rOnce your source is connected to this device via Accsoon SeeMo, it will appear in the Inputs tab as the built-in camera.\r\r\rAccsoon SeeMo sources can also be used as remote cameras within Switcher. To use an Accsoon SeeMo source as a remote camera, connect your DSLR or other HDMI source to an auxiliary iPhone or iPad running Switcher and control it remotely using this device.";

"MyRecordingTableViewController.edit.title" = "Select";
"MyRecordingTableViewController.edit.rename.title" = "Rename";
"MyRecordingTableViewController.edit.trash.confirm.title" = "Remove the recording";
"MyRecordingTableViewController.edit.trash.confirm-multi.title" = "Remove %d recordings";
"MyRecordingTableViewController.edit.rename.alert.msg" = "Unable to rename";

"MyRecordingTableViewController.title" = "Local Recordings";
"MyRecordingTableViewController.edit.select-all.title" = "Select All";
"MyRecordingTableViewController.edit.deselect-all.title" = "Deselect All";

"MyRecording.Delete.title.sing" = "Delete Video?";
"MyRecording.Delete.title.plur %lld" = "Delete %lld Videos?";
"MyRecording.Delete.body" = "Deleting cannot be undone. Are your sure you want to continue?";

"MyRecordings.no-asset.title" = "Create With Switcher";
"MyRecordings.no-asset.subtitle" = "Start streaming or recording and access every video you’ve made with Switcher right here.";
"MyRecordings.no-asset.hint.guest" = "Get started now";
"MyRecordings.no-asset.hint.loggedIn" = "Go live or record your first video";
"MyRecordings.select.title" = "Select Recordings";
"MyRecordings.select.sing" = "1 recording selected";
"MyRecordings.select.plur %lld" = "%lld recordings selected";
"MyRecordingTableViewCell.delete" = "Delete";
"MyRecordings.sort.title" = "Sort By";
"MyRecordingTableViewController.edit.tools.title" = "Tools";
"MyRecordings.sort.creation.date" = "Date Created";
"MyRecordings.sort.modification.date" = "Date Modified";
"MyRecordings.sort.name" = "Name";

"MyRecordings.export.live.output" = "Share";
"MyRecordings.delete.live.output" = "Delete Live Recording";
"MyRecordings.export.clip" = "Export Clip";
"MyRecordings.export.video" = "Export Video";
"MyRecordings.export.audio" = "Export Audio";


"MyRecordings.clips.creation.info" = "Create video clips from your original recording.";
"MyRecordings.clip.source %@" = "Source: %@";
"MyRecordings.clip.create.menu" = "Create a clip for :";
"MyRecordings.clip.create.menu.others" = "Angles";
"MyRecordings.clip.create" = "Start Clipping";

"MyRecordings.select.clip.title" = "Select Clips";
"MyRecordings.select.clip.sing" = "1 clip selected";
"MyRecordings.select.clip.plur %lld" = "%lld clips selected";

"MyRecordings.banner.text" = "Now you can upload directly to the Dashboard video library.";
"MyRecordings.banner.learn-more" = "Learn More...";
"MyRecordings.banner.webview.title" = "Upload to Library";
"MyRecordings.last-uploaded" = "You uploaded this video %@";
"MyRecordings.transcript" = "Transcript";
"MyRecordings.transcript.edited" = "Edited";
"MyRecordings.transcript.generated" = "Generated automatically";
"MyRecordings.transcript.empty" = "No transcript available for this video.";
"MyRecordings.transcript.menu.copy" = "Copy Text";
"MyRecordings.transcript.menu.edit" = "Edit";
"MyRecordings.transcript.menu.export" = "Export .SRT";
"MyRecordings.transcript.menu.import" = "Import .SRT";
"MyRecordings.transcript.menu.delete" = "Delete Transcript";
"MyRecordings.transcript.menu.delete.confirmation.title" = "Delete Transcript?";
"MyRecordings.transcript.menu.delete.confirmation.subtitle" = "Note: You can generate or import a new transcript at any time.";
"MyRecordings.transcript.menu.delete.cloud.confirmation.subtitle" = "Note: You can import a new transcript at any time.";
"MyRecordings.transcript.menu.delete.progress.title" = "Please wait...";
"MyRecordings.transcript.menu.delete.progress.subtitle" = "Don’t close the app or put it in the background.";
"MyRecordings.transcript.edit.title" = "Edit Transcript";
"MyRecordings.transcript.tooltip" = "On your device, go to **Settings** > **Accessibility** > **Voice Control** > **Language** to add new languages.";

"MyRecordingTableViewCell.mode.live" = "Live";
"MyRecordingTableViewCell.mode.director-mode-recording" = "DM Recording";
"MyRecordingTableViewCell.mode.director-mode-composition" = "DM Composition";
"MyRecordingTableViewCell.mode.channel" = "Channel";
"MyRecordingTableViewCell.mode.channels" = "Channels";
"MyRecordingTableViewCell.mode.clip" = "Clip";
"MyRecordingTableViewCell.mode.clips" = "Clips";
"MyRecordingTableViewCell.rename" = "Rename";
"MyRecordingModeTableViewController.mode.live" = "Live Recording";
"MyRecordingModeTableViewController.mode.director-mode-composition" = "Director Mode Composition";
"MyRecordingModeTableViewController.mode.director-mode-recording" = "Director Mode Recording";
"MyRecordingModeTableViewController.mode.clips" = "Clips";

"asset.cloud.error" = "An unexpected error occurred. Please try again.";
"asset.cloud.emptystate.title" = "Nothing to see here yet";
"asset.cloud.emptystate.description" = "To upload assets to the Switcher Cloud: go to the sources panel, tap Select, choose your desired assets, and then tap the cloud icon.";

"asset.upload.conflict.title" = "Resolve Conflict";
"asset.download.conflict.download" = "Download & Replace";
"asset.download.conflict.keep" = "Keep Local Version";
"asset.download.conflict.exist" = "This file already exists locally";

"mediagrid.empty.title" = "No image";
"prod-flow.photo.button.more.short" = "More";
"prod-flow.photo.button.more.long" = "More Options...";

"social-platform-icons-donation" = "donation";
"social-platform-icons-social" = "social";
"social-platform-icons-title" = "Platform Icons";



"asset.upload.conflict.download" = "Upload & Replace";
"asset.upload.conflict.keep" = "Keep Cloud Version";
"asset.upload.conflict.exist" = "This file already exists on the Switcher Cloud";

"asset.upload.progress.title" = "One moment — your assets are uploading.";
"asset.upload.success.title" = "Asset uploaded";
"asset.download.success.title" = "Asset downloaded";
"asset.download.group.success.title" = "Group downloaded";
"asset.upload.cancel" = "Cancel Upload";
"asset.upload.delete.local" = "Delete Local Assets";
"asset.upload.keep.local" = "Keep Local Assets";
"asset.upload.error.title" = "An error occurred";
"asset.conflict.sing.title" = "1 conflicted file";
"asset.conflict.plur.title %lld" = "%lld conflicted files";
"asset.upload.cloud" = "Cloud";
"asset.upload.local" = "Local";
"asset.upload.delete.sing.information" = "Asset deleted";
"asset.upload.delete.plur.information" = "%d assets deleted";
"asset.download.delete.cloud.selection" = "Select Assets";
"asset.download.delete.cloud.selected.plur %lld" = "%lld Assets Selected";
"asset.download.delete.cloud.selected.sing" = "1 Asset Selected";
"asset.download.delete.cloud.confirm" = "You’re about to delete permanently asset(s) from the Switcher Cloud. Are you sure you want to continue?";
"asset.download.delete.cloud.title" = "One moment — your Switcher Cloud assets are being deleted.";

//GLOBAL FORM
"global.form.fieldrequired %@" = "%@ is required";

// GLOBAL ALERT VIEW
"global.alertview.retry" = "Retry";
"global.alertview.waiting" = "Please do not lock screen or leave the app.";
"global.alertview.error.title" = "Oops";

// Upload to Video Library
"videolibrary.title" = "Upload Video";
"videolibrary.title.plural" = "Upload Videos";
"videolibrary.videotoolong.title" = "Video is too big";
"videolibrary.videotoolong.description" = "Maximum video duration is 30 GB";
"videolibrary.videonotfound.title" = "Video not found";
"videolibrary.videonotfound.description" = "Cannot export the file to the video library.";
"videolibrary.badformat.title" = "Format not supported";
"videolibrary.badformat.description" = "Cannot export the file to the video library. The encoding of the video is not supported (supported encoding: h264 and h265)";
"videolibrary.upload.progress.title" = "One moment — your video is uploading";
"videolibrary.form.file" = "File:";
"videolibrary.form.name" = "Name";
"videolibrary.form.name.placeholder" = "Give your video a name";
"videolibrary.form.description" = "Description";
"videolibrary.form.description.placeholder" = "Let viewers know what your video is about (optional).";
"videolibrary.form.visibility" = "Visibility";
"videolibrary.form.showInCatalog" = "Show In Video Catalog";
"videolibrary.form.collections" = "Collections";
"videolibrary.form.tags" = "Tags";
"videolibrary.form.transcript.label" = "Transcript";
"videolibrary.form.transcript.choose.label" = "Choose an .SRT file on your device.";
"videolibrary.form.transcript.replace.label" = "Replace existing transcript with an .SRT file.";
"videolibrary.form.include.transcript" = "Include existing video transcript.";
"videolibrary.form.transcript.menu.replace.label" = "Replace File";
"videolibrary.form.upload.button" = "Upload";
"videolibrary.form.explaination" = "Want this video on your website? Visit the Switcher Dashboard after your upload completes and add it to a Switcher Player playlist.";
"videolibrary.multiExport.error.description.sing" = "A video has been removed from your selection (unsupported encoding).";
"videolibrary.multiExport.error.description.plur" = "Some videos have been removed from your selection (size or encoding not supported).";

//Video Library Uploads Manager & Related
"videolibrary.uploads.toast.completed" = "Upload complete.\nVideo is now available in your library.";
"videolibrary.uploads.toast.failed" = "Upload failed. Please try again.";
"videolibrary.uploads.paused" = "Paused";
"videolibrary.uploads.failed" = "Upload Failed";
"videolibrary.uploads.success" = "Uploaded";

"menu.section.profile" = "PROFILE";
"menu.section.support" = "HELP";
"menu.support.tutorials" = "Video Tutorials";
"menu.section.more" = "MANAGE";
"menu.logout.confirm" = "Are you sure you want to log out?";

"menu.manage.beta.features" = "Beta Features";
"menu.manage.beta.features.disclaimer" = "The following experimental features are in beta stage, meaning they could cause unexpected behavior when in use. You may preview these features and provide feedback, but generally we do not provide support for beta features.";
"menu.manage.beta.features.empty.title" = "Welcome to Beta Features";
"menu.manage.beta.features.empty.content" = "There are no beta features currently available. Check back soon for future updates.";
"menu.manage.beta.features.get.features.error.title" = "Unable to load the page";
"menu.manage.beta.features.get.features.error.detail" = "Your internet connection appears to be offline. Check your network settings and try again.";
"menu.manage.beta.features.get.features.error.retry" = "Retry";

"usermain.subscribe.banner.title" = "You don't have an active subscription right now.";
"usermain.subscribe.banner.button" = "Subscribe";

"usermain.storageFull.banner.title %lld %lld" = "**Attention** – You have reached your overall storage limit of video uploads (%lld of %lld).";
"usermain.storageWarning.banner.title %lld %lld" = "**Attention** – You are approaching your overall storage limit of video uploads (%lld of %lld).";

"menu.subscription.multiPlan.title" = "Choose a plan";
"menu.subscription.trial.title" = "Your first 14 days for free";
"menu.subscription.trial.subtitle" = "Pay nothing today and cancel anytime";
"menu.subscription.trial.subscribe" = "Start 14-Day Trial";
"menu.subscription.trial.subscribeTo %@" = "Subscribe To %@";
"menu.subscription.trial.comment" = "Billing begins when your free trial ends. Cancel before trial ends and you won’t be charged. Subscription automatically renews monthly until you cancel. Cancel anytime in your Apple account.";
"menu.subscription.title" = "Start creating with Switcher";
"menu.subscription.subtitle" = "No commitment. Cancel anytime";
"menu.subscription.price %@" = "Renews automatically at %@";
"menu.subscription.priceMonthly %@" = "%@/month";

"menu.subscription.perMonth" = "/month";
"menu.subscription.error" = "Something went wrong.";
"menu.subscription.subscribe" = "Subscribe Now";
"menu.subscription.subscribeTo %@" = "Subscribe To %@";
"menu.subscription.comment" = "Subscription automatically renews monthly until you cancel. Cancel anytime in your Apple account.";

"menu.subscription.feature.multicam" = "Multicamera, multisource video";
"menu.subscription.feature.multistreaming" = "Built-in Multistreaming";
"menu.subscription.feature.graphics" = "Built-in graphics templates";
"menu.subscription.feature.guests" = "Invite up to five remote guests";

"menu.subscription.termsofservice" = "Terms of Service";
"menu.subscription.privacypolicy" = "Privacy Policy";
"menu.subscription.restore" = "Restore Purchase";

"menu.subscription.current.plan" = "My plan";
"menu.subscription.available.plans" = "Available plan";
"menu.subscription.extra.informations" = "Please log in to **switcherstudio.com** to manage your subscription.";
"menu.subscription.start.trial" = "Start Free Trial";
"menu.subscription.manage" = "Manage Subscription";
"menu.subscription.cancel" = "Cancel Subscription";
"menu.subscription.skip.confirmation.title" = "Your account is created, but you won't have full access to all the great features until you subscribe.";
"menu.subscription.skip.confirmation.button" = "Finish Later";
"menu.subscription.skip.confirmation.cancel" = "Resume";

"videolibrary.selector.localrecording" = "Videos created with Switcher Studio on this device";
"videolibrary.selector.cloudvideos" = "Cloud videos that can be added to a Switcher Player";

// Video Library List View
"videolibrary.processing" = "Your video is processing...";
"videolibrary.login.title" = "Log in to see your videos";
"videolibrary.login.subtitle" = "Once you log in, you’ll find all your videos here.";
"videolibrary.video.error.description" = "Processing error";
"videolibrary.error.title" = "Unable to load your videos";
"rtmp.channel.error.title" = "Unable to load your channels";
"videolibrary.error.subtitle" = "Your Internet connection appears to be offline. Check your network settings and try again.";
"videolibrary.error.button" = "Try again";
"videolibrary.empty.title" = "Take Control of Your Content";
"videolibrary.empty.subtitle" = "Safely store all your videos on the cloud for access to every Switcher feature across all of your devices.";
"videolibrary.empty.hint" = "Upload or create your first video";
"videolibrary.empty.search.title" = "No videos found";
"videolibrary.empty.search.subtitle" = "We couldn’t find any videos that match your criteria.";
"videolibrary.empty.clearfilters" = "Clear Filters";
"videolibrary.download.button" = "Download";
"videolibrary.download.success" = "Video Saved to Camera Roll";
"videolibrary.download.error" = "Cannot Save Video to Camera Roll";
"videolibrary.share.button" = "Share Link";

"videolibrary.select.title" = "Select Videos";
"videolibrary.select.sing" = "1 Video Selected";
"videolibrary.select.plur %d" = "%d Videos Selected";
"videolibrary.sort.nameAZ" = "Title A-Z";
"videolibrary.sort.nameZA" = "Title Z-A";
"videolibrary.sort.newest" = "Newest First";
"videolibrary.sort.oldest" = "Oldest First";
"videolibrary.sort.leastviews" = "Least Views";
"videolibrary.sort.mostviews" = "Most Views";
"videolibrary.default.label" = "(default)";
"videolibrary.delete.cloud.progress" = "Deleting...";
"videolibrary.delete.cloud.error.sing" = "Unable to Delete Video";
"videolibrary.delete.cloud.error.plur" = "Unable to Delete Videos";
"videolibrary.delete.success.sing" = "Video deleted";
"videolibrary.delete.success.plur" = "Videos deleted";
"videolibrary.tooltip.title" = "Change Library";
"videolibrary.tooltip.description" = "Switch between your Video Library and Local Recordings here.";

"videolibrary.details.edit" = "Edit Details";
"videolibrary.details.edit.event" = "Edit Livestream Details";
"videolibrary.details.edit.event.save.button" = "Save Changes";
"videolibrary.details.thumbnail" = "Thumbnail";
"videolibrary.thumbnail.rec.size" = "Recommended size is 1920 x 1080 pixels.";
"videolibrary.details.thumbnail.frame" = "Select Video Frame";
"videolibrary.details.edit.confirm.cancel" = "Are you sure you want to discard your changes?";
"videolibrary.details.edit.confirm.cancel.discard" = "Discard Changes";
"videolibrary.details.edit.confirm.cancel.keepediting" = "Keep Editing";
"videolibrary.details.edit.saving" = "Saving changes";
"videolibrary.details.nodescription" = "No Description";
"videolibrary.details.collectionsandpasses" = "Collections & Passes";
"videolibrary.details.collections.empty" = "Not used in any collections yet";
"videolibrary.details.players.plur %lld" = "%lld Players";
"videolibrary.details.gating" = "Gating Options";
"videolibrary.details.pass.empty" = "No one-time passes yet";
"videolibrary.details.pass.sing" = "1 One-Time Pass";
"videolibrary.details.pass.plur %lld" = "%lld One-Time Passes";
"videolibrary.details.pass.password" = "Password";
"videolibrary.details.password.access" = "Enable Password Access";
"videolibrary.details.password.explain" = "Viewers who enter password will bypass email entry and purchase.";
"videolibrary.details.password.create" = "Create a password";
"videolibrary.details.email.access" = "Require Email Address to View";
"videolibrary.details.email.explain" = "Automatically collected with purchase.";
"videolibrary.details.analytics.tip" = "Data does not include plays from social platforms.";
"videolibrary.details.about" = "Information";
"videolibrary.details.about.size" = "Size";
"videolibrary.details.about.size.unknown" = "Unknown";
"videolibrary.details.about.create" = "Created";
"videolibrary.details.about.dimensions" = "Dimensions";
"videolibrary.details.about.dimensions.processing" = "Processing...";
"videolibrary.details.about.links" = "Interactive Links";
"videolibrary.details.analytics" = "Analytics";
"videolibrary.details.analytics.all_time" = "Lifetime views";
"videolibrary.details.analytics.seven_days" = "Last 7 days";
"videolibrary.details.premiering" = "Premiering";
"videolibrary.details.scheduled" = "Scheduled";
"videolibrary.details.unpublished" = "This video is unpublished";
"videolibrary.details.unpublished.short" = "Unpublished";
"videolibrary.details.published.short" = "Published";
"videolibrary.details.catalog.link.title" = "Want this video on your website?";
"videolibrary.details.catalog.link.description" = "Learn how to make your videos available on your website by using Video Catalog and Collections.";

"videolibrary.setvisibility.title" = "Video Visibility";
"videolibrary.visibility.published.title" = "Publish";
"videolibrary.visibility.published.subtitle" = "Your audience can watch this video.";
"videolibrary.visibility.scheduled.title" = "Schedule Premiere";
"videolibrary.visibility.scheduled.subtitle" = "Publish your video at a specific date and time.";
"videolibrary.visibility.scheduled.when" = "When";
"videolibrary.visibility.unpublished.title" = "Unpublished";
"videolibrary.visibility.unpublished.subtitle" = "Only you can watch this video inside your Switcher account.";
"videolibrary.visibility.upload.published.title" = "Publish Now";
"videolibrary.visibility.upload.scheduled.title" = "Schedule Premiere";
"videolibrary.visibility.upload.unpublished.title" = "Save as Unpublished";

"videolibrary.visibility.unpublished.alert.title" = "Attention";
"videolibrary.visibility.unpublished.alert.message" = "You are unpublishing a video that some of your audience may be paying for access to. If you continue, they will no longer be able to watch this content unless you republish.";
 
"videolibrary.upload.library" = "Camera Roll";
"videolibrary.upload.complete" = "Upload Complete";
"videolibrary.upload.files" = "Choose From Files";
"videolibrary.upload.title" = "Upload to Video Library";
"videolibrary.gorecordings.title" = "Go to Local Recordings";
"videolibrary.finalizevideo.title" = "Finalize Video";
"videolibrary.schedule.next.event.title" = "Schedule Next Livestream";
"videolibrary.thumbnail.choose" = "Choose Image";
"videolibrary.thumbnail.frame" = "Video Frame";
"videolibrary.thumbnail.photo" = "Take Photo";
"videolibrary.thumbnail.frame.title" = "Select Video Frame";
"videolibrary.thumbnail.reset" = "Reset Default";

"videolibrary.filterby" = "Filters";
"videolibrary.tags" = "Tags";
"videolibrary.filterby.all" = "All";
"videolibrary.filterby.inplayer" = "In Collection";
"videolibrary.filterby.notinplayer" = "Not in Collection";
"videolibrary.filterby.gated" = "Gated";
"videolibrary.filterby.notgated" = "Not Gated";
"videolibrary.filterby.published" = "Published";
"videolibrary.filterby.unpublished" = "Unpublished";
"videolibrary.filterby.scheduled.for.premiere" = "Scheduled for Premiere";

"videolibrary.search.title" = "Search";
"videolibrary.upload.thumbnail.error" = "Failed to upload thumbnail. Please try again.";
"videolibrary.manage" = "Manage Video Library";
"videolibrary.warning.full.title" = "Attention";
"videolibrary.warning.full.description %lld %lld" = "You have reached your overall storage limit of video uploads (%lld of %lld). If you proceed with the current output selection for this livestream, **the oldest video in your Video Library will be automatically deleted.**";
"videolibrary.tags.emptystate.title" = "No tags";
"videolibrary.tags.emptystate.filter.description1" = "Tags allow you to group and filter your videos for easier content management.";
"videolibrary.tags.emptystate.filter.description2" = "You can create your first tag by editing the details of any video you've created or uploaded.";
"videolibrary.categories.emptystate.description" = "Start by creating one, it's a great way to organize and filter your videos.";
"videolibrary.tags.add.title" = "Add Tags";
"videolibrary.tags.add.new" = "New Tag";
"videolibrary.tags.edit.title" = "Edit Tags";
"videolibrary.tags.rename.title" = "Rename Tag";
"videolibrary.tags.delete" = "Delete Tag";
"videolibrary.tags.delete.title" = "Delete Tag?";
"videolibrary.tags.delete.msg %@" = "Are you sure you want to delete %@?";
"videolibrary.tags.title" = "Tag";
"videolibrary.tags.noconnection.title" = "Unable to Load Tags";
"videolibrary.tags.add.error.title" = "Unable to Create Tag";
"videolibrary.tags.add.error.subtitle" = "Something went wrong while creating your new tag.";
"videolibrary.tags.videocount_zero" = "No videos";
"videolibrary.tags.videocount_singular" = "1 video";
"videolibrary.tags.videocount_plural %lld" = "%lld videos";
"videolibrary.categories.add" = "Add";
"videolibrary.cloudvideo.view_singular" = "1 View";
"videolibrary.cloudvideo.view_plural %lld" = "%lld Views";
"videolibrary.loading.fail" = "Loading failed. **Tap to Retry**";
"videolibrary.loading.finished" = "No more videos to show.";

"stream.settings.advanced.title" = "Advanced Settings";
"stream.settings.advanced.subtitle" = "RTMP advanced settings only apply when using Custom RTMP mode. For Livestreaming, stream details will be used.";

//SURVEY
"survey.onboarding.title" = "Tell Us About Yourself";
"survey.exit.title" = "Unsure about your Subscription?";

"survey.button.continue" = "Continue";
"survey.button.done" = "Done";

"survey.thankyou.title" = "Thank you!";
"survey.thankyou.text" = "We appreciate your time.\rYour responses will help shape the future of Switcher.";
"survey.thankyou.continue" = "Continue to the app";

"survey.page0.title" = "How would you describe yourself?";
"survey.page0.subtitle" = "Take a moment to help us get to know you.";
"survey.page0.option1" = "Business Owner";
"survey.page0.option2" = "Marketing Professional";
"survey.page0.option3" = "Influencer/Creator";
"survey.page0.option4" = "Hobbyist";
"survey.page0.option5" = "Other (please specify)";

"survey.page1.title" = "What brings you to Switcher?";
"survey.page1.subtitle" = "Select all that apply.";
"survey.page1.option1" = "Recording videos";
"survey.page1.option2" = "Livestreaming on social media";
"survey.page1.option3" = "Charging viewers to watch my content";
"survey.page1.option4" = "Embedding videos on my website";
"survey.page1.website_query" = "www.website.com (optional)";
"survey.page1.website_query_prompt" = "What's your website URL?";

"survey.page2.title" = "Do you have any existing videos to upload?";
"survey.page2.subtitle" = "And if yes, how many?";
"survey.page2.option1" = "No, I don't have any existing videos";
"survey.page2.option2" = "Fewer than 10 videos";
"survey.page2.option3" = "Between 10 and 50 videos";
"survey.page2.option4" = "Between 51 and 100 videos";
"survey.page2.option5" = "More than 100 videos";

"survey.page3.title" = "Where will your viewers watch your videos?";
"survey.page3.subtitle" = "Select all that apply.";
"survey.page3.option1" = "My Website";
"survey.page3.option2" = "Facebook";
"survey.page3.option3" = "YouTube";
"survey.page3.option4" = "Twitch";
"survey.page3.option5" = "TikTok";
"survey.page3.option6" = "Instagram";
"survey.page3.option7" = "Other (please specify)";
"survey.page3.where_query" = "Other (please specify)";

"survey.page4.title" = "How did you find out about Switcher?";
"survey.page4.subtitle" = "This is the last question and you're all set.";
"survey.page4.option1" = "Online Search";
"survey.page4.option2" = "App Store";
"survey.page4.option3" = "Social Media";
"survey.page4.option4" = "Digital Ad";
"survey.page4.option5" = "Personal Recommendation";
"survey.page4.option6" = "Blog Post";
"survey.page4.option7" = "Other (please specify)";

"ElementMainView.loop_count %d" = "Loop %dx";
"Eptz.Help" = "Once zoomed, drag left or right to pan and up or down to tilt.";
"Eptz.Help.Button" = "Tap To Dismiss";

//Menu/Profile Messaging
"menu.message.expired_account" = "Your account has expired.";
"menu.message.no_active_subscriptions" = "No active subscription.";
"menu.message.on_a_trial" = "You're on a trial.";
"menu.message.csm-hubspot.title" = "Need help getting started?";
"menu.message.csm-hubspot.subtitle" = "Book time with an expert";
"menu.message.csm-hubspot.webview.title" = "Schedule a Call";
"menu.platforms" = "Platforms";
"menu.platforms.connect.message" = "Connect and manage your external streaming destinations or stream to nearly any platform using a Stream Key and Server URL provided by that platform.";
"social.platforms.error.title" = "Unable to load external destinations";
"social.platform.error.subtitle" = "Your internet connection appears to be offline. Check your network settings and try again.";

"SwitcherNavigationActionSelectorView.streaming" = "Go Live";
"SwitcherNavigationActionSelectorView.practice" = "Practice";
"SwitcherNavigationActionSelectorView.rec" = "Record Video";
"SwitcherNavigationActionSelectorView.upload" = "Upload Video";
"SwitcherNavigationOrientationSelectorView.enter" = "Enter Studio";
"Show.Instructions" = "Show Instructions";
"studio.loading.fail" = "Loading failed. **Tap to Close**";
"studio.share.title" = "Share Link";
"studio.share.practice.title" = "Share Private Link to Watch";

"SwitcherNavigationOrientationSelectorView.no.event.found.title" = "No Livestreams";
"SwitcherNavigationOrientationSelectorView.no.event.found.message" =  "When you’re ready to go live, you can create a new livestream from the Outputs tab.";
"SwitcherNavigationOrientationSelectorView.practice.text" = "Test out your livestream and familiarize yourself with all the features of Switcher. Practice streams will only be visible to you, and recordings can be watched in your online dashboard for 14 days.";

//Add To Players
"videolibrary.addtoplayers.addtocollections.nav.title" = "Add to Collections";
"videolibrary.addtoplayers.videocount_singular" = "1 Video";
"videolibrary.addtoplayers.videocount_plural %lld" = "%lld Videos";
"videolibrary.addtoplayers.playertitle.placeholder.label" = "Example Player";
"videolibrary.addtoplayers.information.text" = "This livestream will automatically stream to your Video Library. Visit your account at **switcherstudio.com** to manage your Video Catalog and collections.";
"videolibrary.addtoplayers.information.text.replay" = "This replay is automatically available in your Video Library. Visit your account at **switcherstudio.com** to manage your Video Catalog and collections.";
"videolibrary.addtocollections.button.text" = "Add to Collections";
"videolibrary.addtocollections.count_singular" = "1 Collection";
"videolibrary.addtocollections.count_plural %lld" = "%lld Collections";
"videolibrary.addtoplayers.load.error.title" = "Unable to Load Collections";
"videolibrary.addtoplayers.load.error.text" = "Your Internet connection appears to be offline. Check your network settings and try again.";
"videolibrary.addtoplayers.create.error.title" = "Unable to Create Collection";
"videolibrary.addtoplayers.create.error.text" = "Your Internet connection appears to be offline. Check your network settings and try again.";
"videolibrary.addtoplayers.empty.title" = "No collections yet";
"videolibrary.addtoplayers.empty.subtitle" = "Organize livestreams and videos from your library into collections that can be embedded on your website or shared using a dedicated watch page.";
"videolibrary.addtoplayers.create" = "Create Collection";
"videolibrary.addtoplayers.new" = "New Collection";

"videolibrary.upload.background.tooltip.title" = "Track Your Upload Progress";
"videolibrary.upload.background.tooltip.description" = "Keep the app open to ensure smooth uploading. Uploads will pause if you leave the app or create a new video.";

"videolibrary.uploads.title" = "Uploads";
"videolibrary.uploads.storagelimit" = "Storage limit reached";
"videolibrary.uploads.storagelimit.description" = "Uploads will resume when space is available in your Video Library.";
"videolibrary.uploads.noSubscription" = "No Subscription";
"videolibrary.uploads.noSubscription.description" = "Uploads will resume when your account has an active subscription.";
"videolibrary.uploads.noInternet" = "No Internet Connection";
"videolibrary.uploads.noInternet.description" = "Uploads will resume when your device has a stable network connection.";
"videolibrary.uploads.emptystate.title" = "No Uploads in Progress";
"videolibrary.uploads.emptystate.description" = "You can monitor the status of your next video upload on this screen.";
"videolibrary.uploads.tryagain" = "Try Again";
"videolibrary.uploads.cancelall" = "Cancel All";
"videolibrary.uploads.resume" = "Resume";
"videolibrary.uploads.pause" = "Pause";
"videolibrary.uploads.cancel.title" = "Cancel Upload" ;
"videolibrary.uploads.cancel.description" = "Your upload queue will be deleted. This action cannot be undone." ;
"videolibrary.uploads.cancel.notnow" = "Not Now" ;
"videolibrary.uploads.cancel.yes" = "Yes, Cancel";
"videolibrary.uploads.status.finishing" = "Finishing Upload..." ;
"videolibrary.uploads.status.queued" = "Queued" ;
"videolibrary.uploads.status.start" = "Starting Upload...";
"videolibrary.uploads.status.uploading" = "Uploading... %@%%";
"videolibrary.uploads.status.uploaded" = "Uploaded";

// Links
"videolibrary.form.links" = "Links";
"videolibrary.links.add.title" = "Add Links";
"videolibrary.links.add.new" = "New Link";
"videolibrary.links.add.edit" = "Edit Link";
"videolibrary.links.emptystate.description" = "These links will appear in the details tab of this video in every player that you add it to.";
"videolibrary.links.emptystate.title" = "No Links Yet";
"videolibrary.links.noconnection.title" = "Unable to Load Links";
"videolibrary.addlinks.button.text" = "Add Links";
"videolibrary.addlinks.count_singular" = "1 Link";
"videolibrary.addlinks.count_plural %lld" = "%lld Links";
"videolibrary.addlinks.url.error" = "Please enter a valid URL";
"videolibrary.addtags.button.text" = "Add Tags";
"videolibrary.addtags.count_singular" = "1 Tag";
"videolibrary.addtags.count_plural %lld" = "%lld Tags";
"videolibrary.uploads.addlinks.status" = "Create Weblinks";
"videolibrary.uploads.captions.status" = "Create Captions";
"videolibrary.uploads.deletelink" = "Delete Link";
"videolibrary.uploads.addlinks.detail" = "These links will appear in the details tab of this video in every player that you add it to.";
"videolibrary.uploads.addlinks.purchase" = "Hide link until video or subscription is purchased if a gated content pass is applied.";
"videolibrary.uploads.addlinks.more %@ %lld" = "%@ and %lld more";
"videolibrary.form.nolinks" = "No links yet";

"videolibrary.seemore" = "See More";
"videolibrary.seeless" = "See Less";

"studio.output.status.title" = "Destinations";
"studio.output.status.action.end" = "End Stream";

"studio.output.destination.title" = "Your Destinations";
"studio.output.destination.vc" = "Video Catalog";
"studio.output.destination.addnew" = "Add Destination";
"studio.output.destination.next" = "Next";
"studio.output.destination.max" = "You've reached the external destination limit for your plan.";
"studio.output.destination.video.catalog" = "Video Catalog";
"studio.output.destination.others" = "others";

"studio.output.destination.footer.connectedas %@" = "Connected as %@.";
"studio.output.destination.footer.signout" = "Disconnect";
"studio.output.destination.footer.alert.title %@" = "Sign Out from %@";
"studio.output.destination.footer.alert.message" = "Any existing destination will be removed. Are you sure?";
"studio.output.destination.footer.alert.progress" = "Signing Out...";
"studio.output.destination.footer.alert.success" = "Sign Out Successful";
"studio.output.destination.footer.alert.error" = "Unable to Sign Out";
"studio.output.destination.footer.alert.error.info" = "Please check your connection and try again.";
"studio.output.destination.footer.tooplip" = "External destinations cannot be changed or edited once your livestream is scheduled.";

"studio.output.event.title" = "Livestream Details";
"studio.output.event.scheduled %@" = "Livestream is scheduled for **%@**.";
"studio.output.event.golive" = "Go Live Now";
"studio.output.event.schedule.start" = "Starts";
"studio.output.event.schedule" = "Schedule for Later";
"studio.output.event.title.placeholder" = "Give your livestream a name";
"studio.output.event.description.placeholder" = "Tell your audience about your livestream";
"studio.output.event.settings" = "Livestream Settings";
"studio.output.event.settings.quality" = "Quality";
"studio.output.event.settings.quality.tooltip" = "For the best streaming experience, we recommend using 720p resolution or lower.";
"studio.output.event.savetovl" = "Save to Video Library";
"studio.output.event.create.go.live.now.button" = "Create Livestream";
"studio.output.event.create.scheduled.button" = "Schedule Livestream";
"studio.output.event.scheduled.cover.image" = "Thumbnail";
"studio.output.event.scheduled.cover.image.message" = "Recommended size is\n1920 x 1080 pixels.";
"studio.output.event.scheduled.upload.library" = "Camera Roll";
"studio.output.event.switcher.website" = "switcherstudio.com";

"studio.output.event.create.selector.creation.title" = "Create New Livestream";
"studio.output.event.create.selector.creation.description" = "Choose destinations and set up new stream.";
"studio.output.event.create.selector.existing.title" = "Use Existing Scheduled Post";
"studio.output.event.create.selector.existing.description" = "Import post details from Facebook or Youtube.";
"studio.output.event.create.selector.error.all" = "Something went wrong when checking existing scheduled posts.";
"studio.output.event.create.selector.error.partial" = "Something went wrong when checking existing scheduled posts for Facebook/Youtube.";
"studio.output.event.existing.title" = "Scheduled Posts";
"studio.output.event.existing.description" = "Choose the existing scheduled post created on Facebook or Youtube and create an new livestream you can use in Switcher.";

"event.creation.stream.status.delete.error.title" = "Something Went Wrong";
"event.creation.stream.status.delete.error.message" = "We were unable to end your stream properly. Please try again.";
"studio.output.stream.status.error.title" = "Unable to load destinations";
"studio.output.stream.status.error.description" = "Your internet connection appears to be offline. Check your network settings and try again.";
"studio.output.stream.status.active" = "Active";
"studio.output.stream.status.ready" = "Ready";
"studio.output.stream.status.ended" = "Ended";
"studio.output.stream.status.ending" = "Ending Stream...";
"studio.output.stream.status.connecting" = "Connecting...";
"studio.output.stream.status.button.end" = "End Stream";
"studio.output.stream.status.end.confirmation.title" = "End stream now?";
"studio.output.stream.status.end.confirmation.message %@" = "You’re about to end stream to %@. This cannot be undone.";
"studio.output.stream.status.end.confirmation.confirm" = "End Stream";


// adding spaces because they will go together in one Text view but have different fonts/weights
"studio.output.event.stream.directly" = "Stream directly to your website and landing page. To manage your Video Catalog, visit your account dashboard at **switcherstudio.com**.";
"studio.output.event.turn.on.catalog" = "Turn this on to stream directly to your website and landing page. To manage your Video Catalog, visit your account dashboard at  **switcherstudio.com**.";

// Common destination
"destination.stream.to.label" = "Stream to";

// Facebook destination
"destination.facebook" = "Facebook";
"destination.facebook.header" = "Your Facebook account must be at least 60 days old and your page must have at least 100 followers to go live.";
"destination.facebook.timeline" = "Timeline";
"destination.facebook.timeline.my" = "My Timeline";
"destination.facebook.timeline.private" = "Private Test Stream";
"destination.facebook.pages" = "Pages";
"destination.facebook.page" = "Page";
"destination.facebook.content.tags" = "Content Tags";
"destination.facebook.tag" = "Tag";
"destination.facebook.tags" = "Tags";
"destination.facebook.add.tags" = "Add Tags";
"destination.facebook.search.tags" = "Search Tags";
"destination.facebook.no.tags.selected" = "No Tags Selected";
"destination.facebook.tags.no.results" = "No Results";
"destination.facebook.add.pages" = "Add Pages";
"destination.facebook.pages.no.results" = "No Pages";
"destination.facebook.pages.error" = "Unable to Load Pages";
"destination.facebook.no.pages" = "You don’t have any pages you can crosspost with.";
"destination.facebook.pages.offline.error" = "Your internet connection appears to be offline. Check your network settings and try again.";
"destination.facebook.tags.no.results.second.header" = "Try searching again using a different spelling or keyword";
"destination.facebook.tags.title" = "Content Tags";
"destination.facebook.crossposting" = "Crossposting";
"facebook.tags.error.title" = "Something Went Wrong";
"facebook.tags.error.subtitle" = "There was an error loading content tags. Please try again.";


"destination.facebook.event.everyone.title" = "Everyone";
"destination.facebook.event.everyone.description" = "Anyone on or off Facebook.";
"destination.facebook.event.friends.title" = "Friends";
"destination.facebook.event.friends.description" = "Your friends on Facebook.";
"destination.facebook.event.onlyMe.title" = "Only Me";
"destination.facebook.event.onlyMe.description" = "Private test stream.";
"destination.facebook.event.configuration.title %@" = "Facebook - %@";
"destination.facebook.timeline.event.configuration.title" = "Timeline Settings";

// Facebook Page
"destination.facebook.event.commentModeration.title" = "COMMENT MODERATION";
"destination.facebook.event.default.title" = "Default";
"destination.facebook.event.default.description" = "All viewers can participate in chat.";
"destination.facebook.event.follower.title" = "Follower";
"destination.facebook.event.follower.description" = "Only your followers will be able to leave comment.";
"destination.facebook.event.slow.title" = "Slow";
"destination.facebook.event.slow.description" = "Commenters will only be able to comment every 10 seconds.";
"destination.facebook.event.discussion.title" = "Discussion";
"destination.facebook.event.discussion.description" = "Only comments over 100 characters will be shown.";
"destination.facebook.event.restricted.title" = "Restricted";
"destination.facebook.event.restricted.description" = "Commenters must have accounts that are at least 2 weeks old.";

"destination.already.added" = "Already Added";
"destination.load.error" = "Unable to load your destinations";
"destination.settings.title" = "Destinations Settings";
"destination.catalog.description %@" = "Stream directly to your website and landing page. To manage your Video Catalog, visit your account dashboard at %@";
"destination.facebook.page.event.configuration.title" = "Page Settings";

// Youtube destination
"destination.youtube" = "YouTube";
"destination.youtube.header" = "Make sure your channel satisfies the **requirements to go live** on YouTube.";
"destination.youtube.channels.label" = "Channels";
"destination.youtube.forkids.title" = "Made for Kids";
"destination.youtube.forkids.description" = "Made for Kids option indicates that this stream is intended for children. This will restrict some features.";
"destination.youtube.embedded.title" = "Allow Embedding";
"destination.youtube.embedded.description" = "Youtube requires minimum 1,000 channel subscribers and 4,000 watch hours, or user verification (Valid ID or Video Verification) to use Embedded Live Feature.";
"destination.youtube.event.title" = "VISIBILITY";
"destination.youtube.event.public.title" = "Public";
"destination.youtube.event.public.description" = "Anyone can search for and view.";
"destination.youtube.event.private.title" = "Private";
"destination.youtube.event.private.description" = "Only people you choose can view.";
"destination.youtube.event.unlisted.title" = "Unlisted";
"destination.youtube.event.unlisted.description" = "Anyone with the link can view.";
"destination.youtube.event.configuration.title %@" = "YouTube - %@";
"destination.youtube.event.configuration.title" = "Channel Settings";

// Twitch destination
"destination.twitch" = "Twitch";
"destination.twitch.event.configuration.title" = "Account Settings";
"destination.twitch.add.category" = "Add Category";
"destination.twitch.event.category.title" = "Category";
"destination.twitch.event.category.search.title" = "Search categories";
"destination.twitch.event.category.search.empty.title" = "Search Categories";
"destination.twitch.event.category.search.empty.description" = "Twitch uses categories to classify the live streams and group them up with similar content.";
"destination.twitch.event.ingestServer.title" = "Ingest Server";
"destination.twitch.event.ingestServer.placeholder" = "Select Ingest Server";
"desination.twitch.event.ingestServer.description" = "Visit %@ to get information about ingest servers and to find out which ones are recommended for you.";
"destination.twitch.event.ingestServer.error.title" = "Unable to Load Ingest Servers";
"destination.twitch.event.ingestServer.error.text" = "Your Internet connection appears to be offline. Check your connection and try again.";
"destination.twitch.event.categories.error.title" = "Unable to Load Categories";
"destination.twitch.event.categories.error.text" = "Your Internet connection appears to be offline. Check your connection and try again.";

"destination.catalog.event.shopify" = "LIVE SHOPPING";
"destination.catalog.event.shopify.enable" = "Enable Live Shopping";
"destination.catalog.event.no.shopify" = "You need to connect your Shopify account with Cartr before you can sell live.";
"destination.catalog.event.enable.countdown" = "Show countdown over thumbnail";
"destination.catalog.event.one.time.pass" = "Add One-Time Pass";
"destination.catalog.event.one.time.pass.explain" = "Require purchase to view video.";
"destination.catalog.event.one.time.pass.price" = "Price";
"destination.catalog.one.time.pass.price.placeholder" = "2.00";
"destination.catalog.one.time.pass.price.prefix" = "US$";
"destination.catalog.one.time.pass.price.error" = "Amount must be at least $2.00";
"destination.catalog.one.time.pass.rental.error" = "Value must be greater than 0";
"destination.catalog.one.time.pass.name.placeholder" = "Give your pass a name";
"destination.catalog.event.one.time.pass.none" = "None";
"destination.catalog.event.no.one.time.passes" = "No One-Time passes found.";
"one.time.passes.error.title" = "Unable to Load Passes";
"one.time.passes.error.subtitle" = "Your internet connection appears to be offline. Check your network settings and try again.";
"one.time.passes.alert.title" = "Unable to save changes";
"one.time.passes.alert.message" = "Your internet connection appears to be offline. Check your network seetings and try again.";
"destination.catalog.event.one.time.pass.off" = "To start selling one-time access to your livestreams, visit your account dashboard at **switcherstudio.com**.";
"destination.catalog.event.one.time.pass.no.collection.added" = "Gating options are only possible when a collection has been selected as destination.";
"destination.catalog.one.time.pass.description.placeholder" = "Explain to your viewers what they will be able to watch by purchasing this pass.";
"destination.catalog.event.one.time.pass.creation" = "New Pass";
"one.time.passes.timed.access" = "Timed Access Period";
"one.time.passes.duration.hours" = "Hours";
"one.time.passes.duration.days" = "Days";
"one.time.passes.duration.weeks" = "Weeks";

// Livestream
"Livestream.delete.title.sing" = "Delete Livestream?";
"Livestream.delete.body %@" = "Are you sure you want to delete %@?";
"Livestream.delete.success" = "Livestream deleted successfully.";
"Livestream.delete.failed" = "Unable to Delete Livestream";
"Livestream.delete.progress" = "Deleting...";
"Livestream.go.to" = "Go to Livestream Details";
"livestream.how.to" = "How to Run a Test Livestream...";
"livestream.run.test.stream" = "Run a Test Livestream";

"event.creation.success.title" = "Your Livestream is Ready";
"event.creation.success.schedule.title" = "Your Livestream is Scheduled";
"event.creation.success.description.sing" = "Your destination is now configured and you're ready to go live.";
"event.creation.success.schedule.description.sing" = "Your destination is now configured.";
"event.creation.success.description.plur" = "All destinations have been successfully configured and you're ready to go live.";
"event.creation.success.schedule.description.plur" = "All destinations have been successfully configured.";
"event.creation.success.catalog.on.promotion.description" = "You can organize livestreams and videos from your library into collections. Visit your account at **switcherstudio.com** to learn more about it.";
"event.creation.success.catalog.off.promotion.description" = "You can go live directly to your website by using the Video Catalog. Give it a try when scheduling your next livestream or visit your account at **switcherstudio.com** to learn more about it.";
"event.creation.error.generic" = "There was an error with your %@ connection.";
"event.creation.warning.title" = "Please Check Details Below";
"event.creation.warning.description" = "Your livestream is ready but some of your destinations failed. Live streaming is possible on the configured destinations.";
"event.creation.error.youtube.embed.restriction" = "Youtube requires minimum 1,000 channel subscribers and 4,000 watch hours, or user verification (Valid ID or Video Verification) to use Embedded Live Feature";
"event.creation.error.facebook" = "Please reconnect your Facebook account and allow Switcher to make posts on your behalf.";
"event.creation.preparing" = "Preparing Your Livestream...";
"event.creation.error.title" = "Something Went Wrong";
"event.creation.error.subtitle" = "An error occurred when configuring your livestream.";
"event.creation.goback" = "Go Back";

"event.editing.success.title" = "Livestream Updated";
"event.editing.success.schedule.description" = "Your livestream details have been successfully updated.";
"event.editing.preparing" = "Updating Your Livestream...";
"event.editing.error.subtitle" = "An error occurred when updating your livestream.";
"event.editing.warning.title" = "Attention";
"event.editing.warning.description" = "Some destinations could not be properly updated. See details below.";
"event.editing.error.title" = "Something Went Wrong";
"event.editing.error.subtitle" = "An error occurred when updating your livestream.";

"event.upcoming.title" = "Scheduled Livestreams";
"event.upcoming.count.plur %lld" = "%lld livestreams";
"event.upcoming.count.sing" = "1 livestream";
"event.details.share.link" = "Share Livestream Link";
"event.details.scheduled.for.label %@" = "Scheduled for %@";
"event.details.video.player.details.error" = "Failed to load destination, please refresh.";

// Permissions
"permissions.title" = "Before you start";
"permissions.subtitle" = "Switcher needs access to a few permissions in order to work properly.";
"permissions.tooltip" = "You’re always in control. You can change this anytime in your device settings.";
"permissions.camera.title" = "Camera";
"permissions.camera.subtitle" = "Use your camera to record videos.";
"permissions.mic.title" = "Microphone";
"permissions.mic.subtitle" = "Use your microphone to record sound.";
"permissions.network.title" = "Local Network";
"permissions.network.subtitle" = "Use iPhones and iPads as additional cameras.";
"permissions.speech.title" = "Speech Recognition";
"permissions.speech.subtitle" = "Use speech recognition to generate transcript.";
"permissions.button.set" = "Next";


// Transcripts
"transcription.copied.to.clipboard" = "Transcript copied to clipboard.";
"transcription.generation.title" = "Generating transcript...";
"transcription.generation.subtitle %lld" = "%lld caption(s) generated.\n\nPlease don't close the app or put it in the background.";
"transcription.generation.error" = "An error occurred when generating the transcript.";
"transcription.generation.error.noaudio" = "No audio was detected in your video, so we couldn't create a transcript.";
"transcription.start.title" = "Generate transcript";
"transcription.start.description" = "Automatically create transcript and captions for your video using speech recognition.";
"transcription.start.button" = "Start Recognition";
"transcription.access.title" = "Switcher Does Not Have Access to Speech Recognition";
"transcription.access.message" = "Speech recognition is used to generate transcripts of your video.";
"transcription.cloud.error" = "Unable to load transcript";
"transcription.cloud.processing" = "Transcript is processing...";

// Productions group
"productiongroups.empty.title" = "No groups";
"productiongroups.empty.subtitle" = "Streamline your productions by organizing your assets into production groups.";
"productiongroups.new" = "New Group";
"productiongroups.asset.sing" = "%d asset";
"productiongroups.asset.plur" = "%d assets";
"productiongroups.alert.title" = "Delete Group?";
"productiongroups.menu.hide" = "Hide Group";
"productiongroups.menu.unhide" = "Unhide Group";
"productiongroups.rename" = "Rename";
"productiongroups.rename.subtitle"= "Enter new group name";
"productiongroups.menu.rename.title" = "Rename Group";
"productiongroups.menu.delete" = "Delete Group";
"productiongroups.hidden.toast" = "Group hidden from Production Panel.";
"productiongroups.deleted.toast" = "Group deleted from Production Panel.";

"productiongroups.delete.alert.error.title" = "Error";
"productiongroups.delete.alert.error.message" = "Unexpected error occurred";
"productiongroups.delete" = "Delete";
"productiongroups.alert.assets.message %@" = "This group contains assets. These assets will be deleted if you continue.\nAre you sure you want to delete %@?";
"productiongroups.alert.noassets.message %@" = "Are you sure you want to delete %@?";
"productiongroups.add.error.duplicate.title" = "Name Already Taken";
"productiongroups.add.error.duplicate.description" = "Please choose a different name.";

"documentpickerview.filenotfound" = "File not found";
"schedule.live.event" = "Schedule Livestream";
"eventcreation.catalog.password" = "Password";
"eventcreation.catalog.email" = "Email";
"eventcreation.catalog.pass" = "One-Time Pass";
"eventcreation.catalog.collection.sing" = "1 collection";
"eventcreation.catalog.collection.plur" = "%d collections";
"eventcreation.catalog.gated" = "Gated";

// Schedule Stream
"stream.schedule.title" = "Schedule";
"stream.schedule.go.live.now.title" = "Go Live Now";
"stream.schedule.go.live.now.message" = "Create an instant livestream to go live soon. Your audience will not be notified until you begin streaming.";
"stream.schedule.scheduled.for.later.title" = "Schedule for Later";
"stream.schedule.scheduled.for.later.message" = "Create a livestream for a specific date and time to share with your audience.";

"scene.builder" = "Scene Builder";
"scene.builder.fullscreen.title" = "Full-Screen Scene";
"scene.builder.overlay.title" = "Overlay Scene";
"scene.builder.fullscreen.subtitle" = "Combine sources, texts and images.";
"scene.builder.overlay.subtitle" = "Combine texts and images.";
"scene.builder.layer.new.title" = "New Layer";
"scene.builder.layer.text.title" = "Text";
"scene.builder.layer.text.edit.title %@" = "Edit %@";
"scene.builder.layer.image.title" = "Image";
"scene.builder.layer.image.replace" = "Replace Image";
"scene.builder.layer.source.title" = "Source";
"scene.builder.layer.shape.title" = "Shape";
"scene.builder.layer.rectangle.title" = "Rectangle";
"scene.builder.layer.circle.title" = "Circle";
"scene.builder.layer.background.title" = "Background";
"scene.builder.layer.delete.title" = "Delete Layer";
"scene.builder.layer.delete.confirmation.title %@" = "%@ will be deleted. Are you sure?";
"scene.builder.layer.delete.confirmation.confirm.title" = "Delete Layer";

"scene.builder.layer.text.select.font.title" = "Choose Font";
"scene.builder.layer.text.recent.font.title" = "Recents";
"scene.builder.layer.text.line.spacing.title" = "Line Spacing";
"scene.builder.layer.text.align.title" = "Text Alignment";
"scene.builder.layer.content.align.left.title" = "Left Align";
"scene.builder.layer.content.align.center.title" = "Center";
"scene.builder.layer.content.align.right.title" = "Right Align";

"scene.builder.add.sources.title" = "Number of Sources";
"scene.builder.add.sources.hint" = "A maximum of 9 sources can be added to a scene.";
"scene.builder.add.sources.button" = "Add Sources";

"scene.builder.helper" = "Start with an empty scene on which you can add sources (only for full-screen), images and texts to create custom assets.";
"videocatalog.update.error" = "Unable to update catalog settings";

// Recommend Quality
"recommend.quality.title" = "Attention";
"recommend.quality.description" = "Your current connection is not fast enough to support the stream quality you selected, which could create problems when you go live.";

"storage.warning.description %@" = "Your storage has only %@ GB available and it won't be enough to store an hour of recording. We recommend you clean some space before starting the recording, or disable local recording.";
"storage.warning.continue" = "Continue Anyway";
"storage.warning.disable" = "Disable Local Recording";

"recommend.quality.recommend.keep.current.quality %@" = "Keep Current (%@p)";
"recommend.quality.recommend.use.recommended.quality %@" = "Use Recommended (%@p)";

// Recommend Bit Rate Change
"recommend.bitrate.title" = "Attention";
"recommend.bitrate.description %@" = "Your current connection is not fast enough to support the RTMP video bitrate settings you selected, which could create problems when you go live.\nWe recommend you reduce your bitrate to a maximum of %@ kbps.";

"recommend.bitrate.recommend.keep.current %@" = "Keep Current (%@ kbps)";
"recommend.bitrate.recommend.update.settings" = "Update RTMP settings";

// Livestreams Home
"event.home.empty.title" = "Schedule Your Next Livestream";
"event.home.empty.subtitle" = "Attract a larger audience and promote your livestream by scheduling them ahead of time.";
"event.home.empty.button" = "Schedule Now";

"event.home.see.all" = "See All Scheduled Livestreams";

"event.home.error.title" = "Something Went Wrong";
"event.home.error.subtitle" = "Your Internet connection appears to be offline. Check your connection and try again.";

"event.time.away.weeks.plural %lld" = "In %lld Weeks";
"event.time.away.weeks.singular" = "In 1 Week";
"event.time.away.days.plural %lld" = "In %lld Days";
"event.time.away.days.singular" = "In 1 Day";
"event.time.away.hours.plural %lld" = "In %lld Hours";
"event.time.away.hours.singular" = "In 1 Hour";
"event.time.away.minutes.plural %lld" = "In %lld Minutes";
"event.time.away.minutes.singular" = "In 1 Minute";
"event.time.away.now" = "Now";

"mixer.error.reactnative.restart" = "Restart";
"mixer.error.reactnative.ignore" = "Ignore";
"mixer.error.reactnative.title" = "A cloud plug-in has raised an unexpected error";
"mixer.error.reactnative.message" = "This can have an impact on Remote Guests, Live Comments and Scoreboard.";

// Auto Reconnect
"auto.reconnect.message" = "Poor connection. Reconnecting...";

"camera.menu.exit" = "Disconnect Camera";

// Switcher API Errors
"switcher.api.error.no.internet.connection.title" = "Something Went Wrong";
"switcher.api.error.no.internet.connection.subtitle" = "Your Internet connection appears to be offline. Check your connection and try again.";

"switcher.api.error.no.server.connection.title" = "Something Went Wrong";
"switcher.api.error.no.server.connection.subtitle" = "Your connection to the servers appear to be offline. Check your connection and try again.";

"switcher.api.error.user.invalid.title" = "User is Invalid";
"switcher.api.error.user.invalid.subtitle" = "Please login and logout or check your user's active subscription";


"aspect-ratio-free-form" = "Custom";
"audiosource.changed.toast" = "Audio source changed to \"%@\"";


"vprop.picker.cut" = "Cut";
"vprop.picker.push" = "Push";
"vprop.picker.cross-dissolve" = "Cross Dissolve";
"slideshow.select.firstimage" = "Select First Image";

// Tips
"tip.practice.mode.access.title" = "Access Practice Mode";
"tip.practice.mode.access.subtitle" = "You can switch to Practice mode at any time from here.";
"tip.practice.mode.home.title" = "New to Switcher?";
"tip.practice.mode.home.subtitle" = "Practice mode is the best way to try out all the features before going live to your audience.";
"tip.practice.mode.home.button" = "Try Practice Mode";
