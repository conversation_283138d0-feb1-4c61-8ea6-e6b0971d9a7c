//
//  SwitcherAppDelegate.swift
//  Cap-iOS
//
//  Created by <PERSON> on 14/11/2024.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//

import TikTokOpenSDK
import StoreKit
#if SHOW_TOUCHES
import ShowTouches
#endif

#if CRASHLYTICS
import FirebaseCore
#endif


class AppDelegate: NSObject, UIApplicationDelegate {

    var diagrepTerminated = false

    override init() {
        signal(SIGPIPE, SIG_IGN)
        cap_conf_init()
        DiagManager.shared.begin()
    }

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        application.isIdleTimerDisabled = true

        TikTokOpenSDKApplicationDelegate.sharedInstance().application(application, didFinishLaunchingWithOptions: launchOptions)

        _ = NetworkInterfacesMonitor.shared

        AVCaptureDevice.centerStageControlMode = .user
        SwitcherInAppMessage.shared.setup(launchOptions: launchOptions)

        AppStateWatcher.shared.addGraceTimeDelegate(self)
        Analytics.shared.setup()

        // setup features
        FeatureManager.shared.checkForFirstAppLaunch()
        FeatureManager.shared.checkAndUpgradeSettings()

        // setup cloudy tool manager
        CloudyToolManager.shared.appLaunchSetup()

        // setup react native swift modules
        RNInteractivityModule.register()

        // load font list
        FontCache.shared.load()
        
#if CRASHLYTICS
        FirebaseApp.configure()
#endif

        self.initializeClarity()
        
    #if SHOW_TOUCHES
        if let activeProfile = ConfigurationDevelopmentManager.shared.activeProfile {
            if let showTouches = activeProfile.showTouches, showTouches {
                UIWindow.showTouches()
            } else {
                UIWindow.showTouches(false)
            }
        }
    #endif

        // enable Apple's ad network attribution
        SKAdNetwork.registerAppForAdNetworkAttribution()

        return true
    }

    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        InterfaceOrientationCoordinator.shared.supportedOrientations
    }

    func initializeClarity() {
        ClarityLogger.shared.initialize()
    }

}


extension AppDelegate: GraceTimeDelegate {

    func needsMoreGraceTime(_ graceTime: Double) -> Bool {
        if !diagrepTerminated {
            diagrepTerminated = true
            DiagManager.shared.end()
        }
        return false
    }

}
