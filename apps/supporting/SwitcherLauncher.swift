//
//  SwitcherLauncher.swift
//  Cap-iOS
//
//  Created by <PERSON> on 14/11/2024.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//

import TikTokOpenSDK

@MainActor
class SwitcherLauncher {

    var isInitialized = false

    func initialize() {

        guard !isInitialized else { return }
        isInitialized = true
        
        EmbeddedAssetStock.shared.add(ShoppingCardImages.shared.embeddedAssets)
        EmbeddedAssetStock.shared.add(BackgroundImages.shared.embeddedAssets)
        EmbeddedAssetStock.shared.add(PlatformImages.shared.platformIcons)
        EmbeddedAssetStock.shared.add(FakeBrandsImages.shared.embeddedAssets)

        SeemoManager.shared.start()

        // Clean up any orphaned source panel log capture files from previous sessions
        SourceCaptureManager.performStartupCleanup()

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            SwiAccountBackgroundRefresh.shared.refresh()
        }

        DispatchQueue.main.async {

            mio_memory_coordinator_clear_cache(MediaLibrary.shared.inProgressDirPath)

            // scan media in the media library

            MediaList.shared.startAutoDetection()

            // import media recorded by Switcher Cast

            Task {
                SwitcherCastMediaBridge.shared.importAllRecordedFiles()
            }

            // pre-populate assets

            Populator.shared.populateIfNeeded()

            // get tools out of the dev JSON file

            Lab.shared.scanJSON()
        }
    }

    func handleIncomingUrl(url: URL) {

        Analytics.shared.reportEvent(name: "OpenLinks", properties: ["openURL": url])

        // I don't know why we have that... I think it is something we could remove
        if TikTokOpenSDKApplicationDelegate.sharedInstance().application(UIApplication.shared, open: url, sourceApplication: nil, annotation: url) {
            return
        }

        if ReturnApi.sharedInstance.openUrl(url) {
            return
        }

        if let scheme = url.scheme, scheme.hasPrefix("switchershare") {
            guard let ud = UserDefaults(suiteName: "group.com.switcherstudio.common") else {
                return
            }

            var options = ud.dictionary(forKey: UserDefaultsHelper.import) ?? [:]
            var filePaths = options["URLs"] as? [String] ?? []

            let importer = AppFileImporter.shared
            importer.numberOfFilesToBeImported = filePaths.count
            if let isMovie = options["isMovie"] as? Bool {
                importer.fileIsVideo = isMovie
            }

            var result = true
            filePaths.forEach { path in
                if let url = URL(string: path) {
                    result = result && importer.addUrl(url)
                }
            }

            // Upload to library
            options = ud.dictionary(forKey: UserDefaultsHelper.uploadToLibrary) ?? [:]
            filePaths = options["URLs"] as? [String] ?? []

            result = self.addFilesForUpload(filePaths: filePaths)
            return

        } else {
            _ = AppFileImporter.shared.importUrl(url)
        }
    }

    private func addFilesForUpload(filePaths: [String]) -> Bool {
        var result = false

        var urls: [URL] = []
        for filePath in filePaths {
            if let url = URL(string: filePath) {
                urls.append(url)
            }
        }

        result = AppFileUploader.shared.addUrls(urls)

        UserDefaults.standard.removeObject(forKey: UserDefaultsHelper.import)
        UserDefaults.standard.removeObject(forKey: UserDefaultsHelper.uploadToLibrary)
        UserDefaults.standard.synchronize()

        return result
    }
}
