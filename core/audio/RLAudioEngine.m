//
//  RLAudioEngine.m
//  Cap-iOS
//
//  Created by <PERSON><PERSON> on June 24, 2016.
//  Copyright © 2016 Switcher Inc. All rights reserved.
//

#import "InternalSwift.h"
#import "RLAudioEngine.h"
#import <AVFoundation/AVFoundation.h>
#include "cap_conf.h"
#include "amediator.h"
#include <gmutil/gmutil.h>



/*
 * In September 24, 2019, <PERSON> was able to produce a bug with Zoom-H6 as input
 * and AirPods as output on an iPad Pro 3 11-inch.
 * In this configuration, the segment length was 480 and ajoint buffer was too
 * small.
 */
const int RLAudioEngineMinimumSegmentLength = 480;


@implementation RLAudioEngine
{
    CLsLogger *_logger;
    BOOL _running;
    MFLOW_MEDIATOR *_mediator;
    NSPointerArray *_observers;
    BOOL _sourceStereo;
    BOOL _destStereo;
    BOOL _voiceChat;
    NSTimer *_logTimer;
    MFLOW_PCM_TO_VUMETER *_source_pcm2vum;
    dispatch_queue_t _source_pcm2vum_queue;
    GMU_LIST(MFLOW_VUMETER *) _source_vumeters;
    NSObject *_audioRouteChangeInstance;
    AudioInterruptionReason *_interruptionReason;
    NSString *_incidentId;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        _logger = [[CLsLogger alloc] initWithSubsystem: @"swi.core" category: @"RLAudioEngine"];

        _observers = [NSPointerArray weakObjectsPointerArray];

        _engine = aengine_create(NULL);
        _mediator = aengine_create_mediator(_engine);

        _monitoring = YES;
        _voiceChat = NO;
        _voiceChatAllowed = NO;
        _echoCancellationAllowed = NO;

        AVAudioSession *as = [AVAudioSession sharedInstance];

        _sourceStereo = (as.inputNumberOfChannels > 1);
        _destStereo = (as.outputNumberOfChannels > 1);
        _sampleRate = 48000;

        NSNotificationCenter *nc = [NSNotificationCenter defaultCenter];
        [nc addObserver:self
               selector:@selector(audioRouteDidChangeTramp:)
                   name:AVAudioSessionRouteChangeNotification
                 object:nil];

        [nc addObserver:self
               selector:@selector(audioInterruptionDidOccurTramp:)
                   name:AVAudioSessionInterruptionNotification
                 object:nil];

        [self printCurrentAudioRoute];

        GMU_LIST_INIT(_source_vumeters);
    }
    return self;
}

- (void)dealloc
{
    NSNotificationCenter *nc = [NSNotificationCenter defaultCenter];
    [nc removeObserver:self];

    c3_release(&_mediator->z);
    c3_release(&_engine->z);
    _mediator = NULL;
    _engine = NULL;

    [_logTimer invalidate];
    _logTimer = nil;

#ifdef DEBUG
    assert(_source_vumeters.count == 0);
#endif
    GMU_LIST_DEINIT(_source_vumeters);
}

- (BOOL)isSourceStereo
{
    return _sourceStereo;
}

- (BOOL)isDestStereo
{
    return _destStereo;
}

- (NSString *)sourceName
{
    AVAudioSession *session = [AVAudioSession sharedInstance];

    NSArray<AVAudioSessionPortDescription *> *inputs = session.currentRoute.inputs;
    AVAudioSessionPortDescription *input = inputs.count ? inputs[0] : nil;
    if (!input)
        return nil;

    NSString *inputName;
    if ([input.portType isEqualToString:AVAudioSessionPortLineIn])
        inputName = @"SrcLineIn";
    else if ([input.portType isEqualToString:AVAudioSessionPortBuiltInMic])
        inputName = @"SrcBuiltInMic";
    else if ([input.portType isEqualToString:AVAudioSessionPortHeadsetMic])
        inputName = @"SrcWiredInput";
    else if ([input.portType isEqualToString:AVAudioSessionPortBluetoothHFP])
        inputName = @"SrcBluetoothHFP";
    else if ([input.portType isEqualToString:AVAudioSessionPortUSBAudio])
        inputName = @"SrcUSBAudio";
    else if ([input.portType isEqualToString:AVAudioSessionPortCarAudio])
        inputName = @"SrcCarAudio";
    else  {
        if ([input.portType isEqualToString:AVAudioSessionPortVirtual])
            inputName = @"SrcVirtual";
        else if ([input.portType isEqualToString:AVAudioSessionPortPCI])
            inputName = @"SrcPCI";
        else if ([input.portType isEqualToString:AVAudioSessionPortFireWire])
            inputName = @"SrcFireWire";
        else if ([input.portType isEqualToString:AVAudioSessionPortDisplayPort])
            inputName = @"SrcDisplayPort";
        else if ([input.portType isEqualToString:AVAudioSessionPortAVB])
            inputName = @"SrcAVB";
        else if ([input.portType isEqualToString:AVAudioSessionPortThunderbolt])
            inputName = @"SrcThunderbolt";
        else if (input.portType != nil)
            inputName = [NSString stringWithFormat:@"Unknown (%@)", input.portType];
        else
            inputName = @"Unknown";
    }

    return inputName;
}

- (NSString *)sourceLocalizedTitle
{
    NSString *name = self.sourceName;
    if (name == nil) {
        return nil;
    } else if ([name hasPrefix:@"Unknown"]) {
        NSString *prefix = NSLocalizedString(@"RLAudioEngine.unknown.title", comment:@"audio source name");
        NSString *suffix = [name substringFromIndex:7];
        return [NSString stringWithFormat:@"%@%@", prefix, suffix];
    } else {
        if (gmu_ios_is_usb_c_available() && ([name isEqualToString:@"SrcWiredInput"] || [name isEqualToString:@"SrcUSBAudio"])) {
            name = @"SrcUSBCAudio";
        }
        NSString *key = [NSString stringWithFormat:@"RLAudioEngine.%@.title", name];
        return NSLocalizedString(key, comment:@"audio source name");
    }
}

- (NSString *)destName
{
    AVAudioSession *session = [AVAudioSession sharedInstance];

    NSString *outputName;
    NSArray<AVAudioSessionPortDescription *> *outputs = session.currentRoute.outputs;
    AVAudioSessionPortDescription *output = outputs.count ? outputs[0] : nil;
    if (!output)
        return nil;

    if ([output.portType isEqualToString:AVAudioSessionPortLineOut])
        outputName = @"DestLineOut";
    else if ([output.portType isEqualToString:AVAudioSessionPortHeadphones])
        outputName = @"DestHeadphones";
    else if ([output.portType isEqualToString:AVAudioSessionPortBluetoothA2DP])
        outputName = @"DestBluetoothA2DP";
    else if ([output.portType isEqualToString:AVAudioSessionPortBuiltInReceiver])
        outputName = @"DestBuiltInReceiver";
    else if ([output.portType isEqualToString:AVAudioSessionPortBuiltInSpeaker])
        outputName = @"DestBuiltInSpeaker";
    else if ([output.portType isEqualToString:AVAudioSessionPortHDMI])
        outputName = @"DestHDMI";
    else if ([output.portType isEqualToString:AVAudioSessionPortAirPlay])
        outputName = @"DestAirPlay";
    else if ([output.portType isEqualToString:AVAudioSessionPortBluetoothLE])
        outputName = @"DestBluetoothLE";
    else if ([output.portType isEqualToString:AVAudioSessionPortBluetoothHFP])
        outputName = @"DestBluetoothHFP";
    else if ([output.portType isEqualToString:AVAudioSessionPortUSBAudio])
        outputName = @"DestUSBAudio";
    else if ([output.portType isEqualToString:AVAudioSessionPortCarAudio])
        outputName = @"DestCarAudio";
    else  {
        if ([output.portType isEqualToString:AVAudioSessionPortVirtual])
            outputName = @"DestVirtual";
        else if ([output.portType isEqualToString:AVAudioSessionPortPCI])
            outputName = @"DestPCI";
        else if ([output.portType isEqualToString:AVAudioSessionPortFireWire])
            outputName = @"DestFireWire";
        else if ([output.portType isEqualToString:AVAudioSessionPortDisplayPort])
            outputName = @"DestDisplayPort";
        else if ([output.portType isEqualToString:AVAudioSessionPortAVB])
            outputName = @"DestAVB";
        else if ([output.portType isEqualToString:AVAudioSessionPortThunderbolt])
            outputName = @"DestThunderbolt";
        else if (output.portType != nil)
            outputName = [NSString stringWithFormat:@"Unknown (%@)", output.portType];
        else
            outputName = @"Unknown";
    }

    return outputName;
}

- (NSString *)destLocalizedTitle
{
    NSString *name = self.destName;
    if (name == nil) {
        return nil;
    } else if ([name hasPrefix:@"Unknown"]) {
        NSString *prefix = NSLocalizedString(@"RLAudioEngine.unknown.title", comment:@"audio destination name");
        NSString *suffix = [name substringFromIndex:7];
        return [NSString stringWithFormat:@"%@%@", prefix, suffix];
    } else {
        if (gmu_ios_is_usb_c_available() && [name isEqualToString:@"USBAudio"]) {
            name = @"USBCAudio";
        }
        NSString *key = [NSString stringWithFormat:@"RLAudioEngine.%@.title", name];
        return NSLocalizedString(key, comment:@"audio destination name");
    }
    // pc_debug check what is displayed for Lightning
}

- (void)setStateAndNotify:(RLAudioEngineState)state
{
    if (_state == state)
        return;
    _state = state;

    @synchronized (_observers) {
        for (NSInteger i = _observers.count - 1; i >= 0; i--) {
            id<RLAudioEngineObserver> observer = (__bridge id<RLAudioEngineObserver>)[_observers pointerAtIndex:i];
            if (observer) {
                [observer audioEngineStateDidChange:_state];
            }
        }
    }
}

- (void)checkAndRecoverEngineErrors
{
    int count = 0;
    for (;;) {
        enum aengine_state state = aengine_get_state(_engine);
        if (state == AENGINE_STATE_IDLE)
            return;
        if ((state == AENGINE_STATE_RUNNING) || (state == AENGINE_STATE_PAUSED)) {
            [self setStateAndNotify: RLAudioEngineStateRunning];
            return;
        }
        char *error_msg = aengine_get_error_message(_engine);
        NSString *error_msg_string = [NSString stringWithUTF8String: error_msg];
        [_logger info: @"engine in error: %s" stringValue: error_msg_string];
        if (count == 0) {
            _incidentId = NSUUID.UUID.UUIDString;
            NSString *sourceName = self.sourceName;
            NSString *destName = self.destName;
            [_logger info: @"WARNING: problem during audio activation: %s" stringValue: error_msg_string];
            NSDictionary *props = @{@"action": @"BadAudioActivation",
                                    @"error": @(error_msg),
                                    @"level": @"firstDetection",
                                    @"incidentId": _incidentId,
                                    @"sourceName": sourceName ? sourceName : @"None",
                                    @"destName": destName ? destName : @"None",
                                    @"audioRoute": self.currentAudioRoute};
            [Analytics.sharedInstance reportEventWithName:@"Debug"
                                               properties:props];
        }
        if (count == 5) {
            NSString *sourceName = self.sourceName;
            NSString *destName = self.destName;
            NSDictionary *props = @{@"action": @"BadAudioActivation",
                                    @"error": @(error_msg),
                                    @"level": @"fatal",
                                    @"incidentId": _incidentId,
                                    @"sourceName": sourceName ? sourceName : @"None",
                                    @"destName": destName ? destName : @"None",
                                    @"audioRoute": self.currentAudioRoute};
            // reporting for analytics moved into MixerModel
            // [Analytics.sharedInstance reportEventWithName:@"Debug"
            //                                    properties:props];
            [_logger info: @"unable to run the audio engine: %s" stringValue: error_msg_string];
            _interruptionReason = [[AudioInterruptionReason alloc] initWithType:AudioInterruptionTypeError name:@(error_msg) debugInfo:props];
            [self setStateAndNotify: RLAudioEngineStateInterrupted];
            free(error_msg);
            return;
        }
        free(error_msg);
        count++;
        gmu_sleep_ms(100);

        [self updateAudioConfiguration:false];
        aengine_recover(_engine);
    }
}

- (void)setPreferredNumberOfChannels
{
    NSError *err;
    AVAudioSession *session = [AVAudioSession sharedInstance];

    err = nil;
    [session setPreferredOutputNumberOfChannels:GMU_MIN(2, session.maximumOutputNumberOfChannels) error:&err];
    if (err)
        [_logger info: @"WARNING: setPreferredOutputNumberOfChannels returns error '%s'" stringValue: err.description];
    err = nil;
    [session setPreferredInputNumberOfChannels:GMU_MIN(2, session.maximumInputNumberOfChannels) error:&err];
    if (err)
        [_logger info: @"WARNING: setPreferredInputNumberOfChannels returns error '%s'" stringValue: err.description];
}

- (void)setAudioSessionDataSource
{
    /*
     * Here we keep the input port selected by iOS. For that port, we select
     * the data source that best matches the needed source type.
     * The, for the selected data source, we select the polar pattern that
     * bet matches the use case.
     */

    AVAudioSession *session = [AVAudioSession sharedInstance];

    // normally there is only one port currently in use, but we assume there are many
    for (AVAudioSessionPortDescription *port in session.currentRoute.inputs) {

        // search the data source

        AVAudioSessionDataSourceDescription *dataSource = nil;
        NSArray<AVAudioSessionDataSourceDescription *> *dataSources = port.dataSources;

        switch (_sourceType) {
            case RLAudioEngineSourceTypeBackCamera:
                for (AVAudioSessionDataSourceDescription *ds in dataSources) {
                    if ([ds.orientation isEqualToString:AVAudioSessionOrientationBack]) {
                        dataSource = ds;
                        goto found;
                    }
                }
                break;
            case RLAudioEngineSourceTypeFrontCamera:
                for (AVAudioSessionDataSourceDescription *ds in dataSources) {
                    if ([ds.orientation isEqualToString:AVAudioSessionOrientationFront]) {
                        dataSource = ds;
                        goto found;
                    }
                }
                break;
            default:
                for (AVAudioSessionDataSourceDescription *ds in dataSources) {
                    if ([ds.orientation isEqualToString:AVAudioSessionOrientationBottom]) {
                        dataSource = ds;
                        goto found;
                    }
                }
                for (AVAudioSessionDataSourceDescription *ds in dataSources) {
                    if (![ds.orientation isEqualToString:AVAudioSessionOrientationBack] &&
                        ![ds.orientation isEqualToString:AVAudioSessionOrientationFront]
                    ) {
                        dataSource = ds;
                        goto found;
                    }
                }
                if (dataSources.count) {
                    dataSource = dataSources[0];
                    goto found;
                }
                break;
        }

        return;

    found:;

        // search the polar pattern

        NSString *polarPattern = nil;

        switch (_sourceType) {
            case RLAudioEngineSourceTypeBackCamera:
            case RLAudioEngineSourceTypeFrontCamera:
                if ([dataSource.supportedPolarPatterns containsObject:AVAudioSessionPolarPatternSubcardioid]) {
                    polarPattern = AVAudioSessionPolarPatternSubcardioid;
                    break;
                }
                if ([dataSource.supportedPolarPatterns containsObject:AVAudioSessionPolarPatternCardioid]) {
                    polarPattern = AVAudioSessionPolarPatternCardioid;
                    break;
                }
                if (dataSource.supportedPolarPatterns.count) {
                    polarPattern = dataSource.supportedPolarPatterns[0];
                    break;
                }
                break;
            default:
                if ([dataSource.supportedPolarPatterns containsObject:AVAudioSessionPolarPatternOmnidirectional]) {
                    polarPattern = AVAudioSessionPolarPatternOmnidirectional;
                    break;
                }
                if (dataSource.supportedPolarPatterns.count) {
                    polarPattern = dataSource.supportedPolarPatterns[0];
                    break;
                }
                break;
        }

        // select polar pattern and data source

        if (polarPattern)
            [dataSource setPreferredPolarPattern:polarPattern error:nil];
        if (dataSource)
            [port setPreferredDataSource:dataSource error:nil];
    }
}

- (void)setAudioSessionSampleRate:(int)sampleRate
{
    NSError *error = nil;
    AVAudioSession *as = [AVAudioSession sharedInstance];

    [as setPreferredSampleRate:sampleRate error:&error];
    if (error)
        [_logger info: @"setPreferredSampleRate error: %s" stringValue: error.description];

    [as setPreferredIOBufferDuration:1024.0 / (double)sampleRate error:&error];
    if (error)
        [_logger info: @"setPreferredIOBufferDuration error: %s" stringValue: error.description];
}

- (void)updateAudioConfiguration:(BOOL)force
{
    NSError *err = nil;
    bool reconfigure;
    bool updateHfpOption = false;
    bool voiceChat = false;
    bool destMute = false;

    if (!_running)
        return;

    // logic
    AVAudioSession *session = [AVAudioSession sharedInstance];
    NSArray<AVAudioSessionPortDescription *> *inputs = session.currentRoute.inputs;
    AVAudioSessionPortDescription *input = inputs.count ? inputs[0] : nil;

    NSArray<AVAudioSessionPortDescription *> *outputs = session.currentRoute.outputs;
    AVAudioSessionPortDescription *output = outputs.count ? outputs[0] : nil;

    // internal speaker
    if ([output.portType isEqualToString:AVAudioSessionPortBuiltInSpeaker] ||
        [output.portType isEqualToString:AVAudioSessionPortBuiltInReceiver]) {
        if ([input.portType isEqualToString:AVAudioSessionPortBuiltInMic]) {
            if (_voiceChatAllowed && _echoCancellationAllowed) {
                voiceChat = true;
            } else {
                destMute = true;
            }
        } else {
            destMute = true;
        }
    }

    // Airpods
    if ([input.portType isEqualToString:AVAudioSessionPortBluetoothHFP] ||
        [output.portType isEqualToString:AVAudioSessionPortBluetoothHFP]) {
        if (_voiceChatAllowed)
            voiceChat = true;
    }

    if (!_monitoring)
        destMute = true;

    if (_voiceChatAllowed != (bool)(session.categoryOptions & AVAudioSessionCategoryOptionAllowBluetooth))
        updateHfpOption = true;

    reconfigure = (_voiceChat != voiceChat) || updateHfpOption || force;
    _voiceChat = voiceChat;

    // change audio session if needed
    if (reconfigure) {
        if (aengine_get_state(_engine) != AENGINE_STATE_IDLE) // aengine is running
            aengine_pause(_engine); // need to stop audio unit before setting session inactive

        [session setActive:NO error:&err];

        [session setPreferredInput:nil error:nil];

        AVAudioSessionCategoryOptions options = AVAudioSessionCategoryOptionAllowBluetoothA2DP |
                                                AVAudioSessionCategoryOptionDefaultToSpeaker |
                                                AVAudioSessionCategoryOptionAllowAirPlay;
        if (_voiceChatAllowed) // allow as well HFP
            options |= AVAudioSessionCategoryOptionAllowBluetooth;

        [session setCategory:AVAudioSessionCategoryPlayAndRecord
                        mode:_voiceChat ? AVAudioSessionModeVoiceChat : AVAudioSessionModeVideoRecording
                     options:options
                       error:&err];
        if (err)
            [_logger info: @"error on set audio category: %s" stringValue: err.description];

        [self setAudioSessionSampleRate:_sampleRate];

        [session setActive:YES error:&err];
        if (err)
            [_logger info: @"error on set audio active: %s" stringValue: err.description];
    }

    [self setPreferredNumberOfChannels];
    [self setAudioSessionDataSource]; // select the mic matching the source type

    // update aengine
    _sourceStereo = (session.inputNumberOfChannels > 1);
    _destStereo = (session.outputNumberOfChannels > 1);

    aengine_begin_configuration(_engine);
    aengine_set_dest_muted(_engine, destMute);
    _destMuted = destMute;
    aengine_set_source_stereo(_engine, _sourceStereo);
    aengine_set_dest_stereo(_engine, _destStereo);
    aengine_set_voice_processing(_engine, _voiceChat);
    if (force)
        aengine_force_reconfiguration(_engine);
    aengine_commit_configuration(_engine);

    if (aengine_get_state(_engine) == AENGINE_STATE_PAUSED) {
        aengine_resume(_engine);
    }

    [self printCurrentAudioRoute];
}

- (NSString *)currentAudioRoute
{
    NSMutableString *o = [NSMutableString new];
    AVAudioSession *session = [AVAudioSession sharedInstance];

    [o appendFormat:@"Audio Category: %@ / %@ / %d\n", session.category, session.mode, (int)session.categoryOptions];

    [o appendFormat:@"Audio Output: %@\n", self.destName];
    [o appendFormat:@"  - props: channels=%d/%d/%d\n", (int)session.outputNumberOfChannels,
          (int)session.preferredOutputNumberOfChannels, (int)session.maximumOutputNumberOfChannels];
    [o appendFormat:@"Audio Input: %@ / %@ / %@ / %@\n", self.sourceName,
          session.preferredInput,
          session.inputDataSource.dataSourceName,
          session.inputDataSource.selectedPolarPattern];
    if (session.inputGainSettable)
        [o appendFormat:@"  - props: gain=%g channels=%d/%d/%d\n", session.inputGain,
              (int)session.inputNumberOfChannels, (int)session.preferredInputNumberOfChannels,
              (int)session.maximumInputNumberOfChannels];
    else
        [o appendFormat:@"  - props: gain=<not-settable> channels=%d/%d/%d\n",
              (int)session.inputNumberOfChannels, (int)session.preferredInputNumberOfChannels,
              (int)session.maximumInputNumberOfChannels];

    [o appendFormat:@"Audio Input Details:\n"];
    NSMutableSet *currentInputUIDs = [NSMutableSet new];
    for (AVAudioSessionPortDescription *port in session.currentRoute.inputs) {
        [currentInputUIDs addObject:port.UID];
    }
    for (AVAudioSessionPortDescription *port in session.availableInputs) {
        bool star = [currentInputUIDs containsObject:port.UID];
        [o appendFormat:@"  %c port: type=%@ name=%@ uid=%@\n", star ? '*' : '-', port.portType, port.portName, port.UID];
        for (AVAudioSessionDataSourceDescription *source in port.dataSources) {
            bool star = (source == port.selectedDataSource);
            [o appendFormat:@"      %c source: name=%@ orientation=%@\n",
                  star ? '*' : '-', source.dataSourceName, source.orientation];
            for (NSString *polarPattern in source.supportedPolarPatterns) {
                bool star = [polarPattern isEqualToString:source.selectedPolarPattern];
                [o appendFormat:@"          %c polar pattern: %@\n", star ? '*' : '-', polarPattern];
            }
        }
    }

    return o;
}

- (void)printCurrentAudioRoute
{
    [_logger info: @"Current Audio Route: {"];
    for (NSString *line in [self.currentAudioRoute componentsSeparatedByString: @"\n"]) {
        if (line.length > 0)
            [_logger info: @"    %s" stringValue: line];
    }
    [_logger info: @"}"];
}

- (void)audioRouteDidChangeTramp:(NSNotification *)notification
{
    /* old implementation
    if ([NSThread isMainThread]) {
        // it seems this is the path on iOS10
        [self audioRouteDidChange:notification];
    } else {
        // it seems this is the path on iOS9
        dispatch_async(dispatch_get_main_queue(), ^{
            [self audioRouteDidChange:notification];
        });
    }
    */

    /*
     * New implementation.
     * We are now waiting a little bit because we saw that the new audio device
     * sometimes needs a bit of time to become available.
     * We also ensure that, if a new notification comes while we are waiting,
     * the waiting notificaiton is ignored.
     */
    [_logger info: @"audio route change detected"];
    NSObject *instance = [NSObject new];
    _audioRouteChangeInstance = instance;
    dispatch_time_t t = dispatch_time(DISPATCH_TIME_NOW, 500000000ull);
    dispatch_after(t, dispatch_get_main_queue(), ^{
        if (self->_audioRouteChangeInstance == instance) {
            [self audioRouteDidChange:notification];
        }
    });
}

- (void)audioRouteDidChange:(NSNotification *)notification
{
    GMU_APL_ASSERT_MAIN_THREAD();

    // print route change reason
    bool forceConfigUpdate = false;
    NSString *reasonName;
    NSInteger reason = [[[notification userInfo] objectForKey:AVAudioSessionRouteChangeReasonKey] integerValue];
    switch (reason) {
        case AVAudioSessionRouteChangeReasonNoSuitableRouteForCategory:
            reasonName = @"The route changed because no suitable route is now available for the specified category.";
            forceConfigUpdate = true;
            break;
        case AVAudioSessionRouteChangeReasonWakeFromSleep:
            reasonName = @"The route changed when the device woke up from sleep.";
            forceConfigUpdate = true;
            break;
        case AVAudioSessionRouteChangeReasonOverride:
            reasonName = @"The output route was overridden by the app.";
            forceConfigUpdate = true;
            break;
        case AVAudioSessionRouteChangeReasonCategoryChange:
            reasonName = @"The category of the session object changed.";
            break;
        case AVAudioSessionRouteChangeReasonOldDeviceUnavailable:
            reasonName = @"The previous audio output path is no longer available.";
            forceConfigUpdate = true;
            break;
        case AVAudioSessionRouteChangeReasonNewDeviceAvailable:
            reasonName = @"A preferred new audio output path is now available.";
            forceConfigUpdate = true;
            break;
        case AVAudioSessionRouteChangeReasonUnknown:
            reasonName = @"The reason for the change is unknown.";
            break;
        case AVAudioSessionRouteChangeReasonRouteConfigurationChange:
            reasonName = @"The route configuration changed.";
            break;
        default:
            reasonName = [NSString stringWithFormat:@"The reason for the change is very unknown (%d).", (int)reason];
            break;
    }
    [_logger info: @"Audio Route Change: %s" stringValue: reasonName];

    // change config
    /*
     * During interruptions, we do not reconfigure the engine because this
     * would cause the engine to be stopped and restarted, which is not
     * allowed and generates fatal errors.
     * Interruptions happen when we receive a phone call. Then, when we answer
     * the call, the audio routing changes. This is where this check is
     * important.
     */
    if (_state != RLAudioEngineStateInterrupted) {
        [self updateAudioConfiguration: forceConfigUpdate];
        [self checkAndRecoverEngineErrors];
    }

    @synchronized (_observers) {
        for (NSInteger i = _observers.count - 1; i >= 0; i--) {
            id<RLAudioEngineObserver> observer = (__bridge id<RLAudioEngineObserver>)[_observers pointerAtIndex:i];
            if (observer) {
                [observer audioEngineSourceDidChange];
            }
        }
    }

    // start log timer
    [_logTimer invalidate];
    _logTimer = [NSTimer scheduledTimerWithTimeInterval:3 * 60 target:self selector:@selector(log) userInfo:nil repeats:NO];
}

- (void)audioInterruptionDidOccurTramp:(NSNotification *)notification
{
    if ([NSThread isMainThread]) {
        // it seems this is the path on both iOS9 and iOS10
        [self audioInterruptionDidOccur:notification];
    } else {
        // it seems this is never the path
        dispatch_async(dispatch_get_main_queue(), ^{
            [self audioInterruptionDidOccur:notification];
        });
    }
}

- (void)audioInterruptionDidOccur:(NSNotification *)notification
{
    GMU_APL_ASSERT_MAIN_THREAD();
    if ([[notification.userInfo valueForKey:AVAudioSessionInterruptionTypeKey] isEqualToNumber:[NSNumber numberWithInt:AVAudioSessionInterruptionTypeBegan]]) {
        /*
         * Interruption happens when we receive a phone call.
         */
        
        [_logger info: @"Audio Interruption began"];
        NSDictionary *props = @{@"action": @"AudioInterruption",
                                @"error": notification.userInfo.description};
        _interruptionReason = [[AudioInterruptionReason alloc] initWithType:AudioInterruptionTypeOsResourceClaim name:@"AudioInterruption" debugInfo:props];
        [self setStateAndNotify: RLAudioEngineStateInterrupted];
    } else {
        [_logger info: @"Audio Interruption ended"];

        /*
         * Apply configuration changes skipped during interruption.
         * Also force reconfiguration in order to restart the audio engine.
         * In fact, after a phone call, the audio engine is in a strange state.
         */
        [self updateAudioConfiguration:true];
        [self checkAndRecoverEngineErrors];
    }
}

- (void)setSampleRate:(int)sampleRate
{
    if (_sampleRate == sampleRate)
        return;

    _sampleRate = sampleRate;

    if (_running) {
        aengine_stop(_engine);

        NSError *err = nil;
        AVAudioSession *as = [AVAudioSession sharedInstance];
        [as setActive:NO error:&err];
        [self setAudioSessionSampleRate:_sampleRate];
        [as setActive:YES error:&err];
        aengine_set_sample_rate(_engine, _sampleRate);

        aengine_start(_engine);
        [self checkAndRecoverEngineErrors];

        // start log timer
        [_logTimer invalidate];
        _logTimer = [NSTimer scheduledTimerWithTimeInterval:3 * 60 target:self selector:@selector(log) userInfo:nil repeats:NO];
    }
}

- (void)setVoiceChatAllowed:(BOOL)voiceChatAllowed
{
    if (_voiceChatAllowed == voiceChatAllowed)
        return;
    _voiceChatAllowed = voiceChatAllowed;
    [self updateAudioConfiguration:false];
    [self checkAndRecoverEngineErrors];
}

- (void)setEchoCancellationAllowed:(BOOL)echoCancellationAllowed
{
    if (_echoCancellationAllowed == echoCancellationAllowed)
        return;
    _echoCancellationAllowed = echoCancellationAllowed;
    [self updateAudioConfiguration:false];
    [self checkAndRecoverEngineErrors];
}

- (void)setMonitoring:(BOOL)monitoring
{
    if (_monitoring == monitoring)
        return;
    _monitoring = monitoring;
    [self updateAudioConfiguration:false];
    [self checkAndRecoverEngineErrors];
}

- (void)setSourceMuted:(BOOL)sourceMuted
{
    if (_sourceMuted == sourceMuted)
        return;
    _sourceMuted = sourceMuted;
    aengine_set_source_muted(_engine, sourceMuted);
}

- (void)setSourceType:(RLAudioEngineSourceType)sourceType
{
    if (_sourceType == sourceType)
        return;

    _sourceType = sourceType;

    [self setAudioSessionDataSource];
    [self updateAudioConfiguration:false];
    [self checkAndRecoverEngineErrors];
}

- (float)sourceClockDrift
{
    return aengine_get_source_clock_drift(_engine);
}

- (float)destClockDrift
{
    return aengine_get_dest_clock_drift(_engine);
}

- (void)log
{
    float d1 = self.sourceClockDrift;
    float d2 = self.destClockDrift;
    if (d1 != 0 || d2 != 0) {
        NSString *sourceName = self.sourceName;
        NSString *destName = self.destName;
        NSDictionary *props = @{@"action": @"ClockDrift",
                                @"platform": gmu_apl_get_platform(),
                                @"osVersion": UIDevice.currentDevice.systemVersion,
                                @"sourceName": sourceName ? sourceName : @"None",
                                @"destName": destName ? destName : @"None",
                                @"source": @(1000000.0 * (double)d1),
                                @"dest": @(1000000.0 * (double)d2),
                                @"sampleRate": @(AVAudioSession.sharedInstance.sampleRate)};
        [Analytics.sharedInstance reportEventWithName:@"Debug" properties:props];
    }
}

- (void)addObserver:(id<RLAudioEngineObserver>)observer
{
    @synchronized (_observers) {
        [_observers addPointer:(__bridge void * _Nullable)observer];
    }
}

- (void)removeObserver:(id<RLAudioEngineObserver>)observer
{
    @synchronized (_observers) {
        for (NSInteger i = 0; i < _observers.count; i++) {
            if ([_observers pointerAtIndex:i] == (__bridge void * _Nullable)observer) {
                [_observers removePointerAtIndex:i];
                break;
            }
        }
    }
}

- (void)start
{
    if (_running)
        return;
    _running = YES;

    AVAudioSession *as = [AVAudioSession sharedInstance];

    [self updateAudioConfiguration:true];

    aengine_begin_configuration(_engine);
    aengine_set_sample_rate(_engine, _sampleRate);
    aengine_set_clock_drift_compensation_enabled(_engine, cap_conf_audio_clock_drift_compensation());
    aengine_set_source_muted(_engine, _sourceMuted);
    aengine_commit_configuration(_engine);

    /* Create pool big enough for maximum _resyncDelay of 1.0 s (plus some margin) */
    int pool_size = 2 * (int)round(1.0 * _sampleRate / RLAudioEngineMinimumSegmentLength + 32);

    MFLOW_PCM_BUF_POOL *pool = mflow_pcm_buf_pool_create(pool_size, AENGINE_DEFAULT_BUF_SLEN);
    aengine_set_source_pool(_engine, pool);
    c3_release(&pool->z.z.z);

    aengine_start(_engine);
    [self checkAndRecoverEngineErrors];

    [self printCurrentAudioRoute];
    
    [_logger info: @"start {"];
    [_logger info: @"  sampleRate=%d" intValue: _sampleRate];
    [_logger info: @"  realHardwareSampleRate=%g" doubleValue: as.sampleRate];
    [_logger info: @"  ioBufferDuration=%g" doubleValue: as.IOBufferDuration];
    [_logger info: @ "}"];

    if ((double)as.sampleRate * (double)as.IOBufferDuration < (double)RLAudioEngineMinimumSegmentLength)
        [_logger info: @"WARNING: ioBufferDuration unexpectedly low"];
}

- (void)stop
{
    if (!_running)
        return;

    
    [_logger info: @"stop"];

    _running = NO;

    aengine_stop(_engine);

    aengine_set_source_pool(_engine, NULL);

    AVAudioSession *as = [AVAudioSession sharedInstance];
    NSError *err = nil;

    [as setActive:NO error:&err];
    if (err)
        [_logger info: @"deactivation error: %s" stringValue: err.description];

    [self setStateAndNotify: RLAudioEngineStateIdle];
}

/**
 * recover()
 * Execute this method if the audio engine is running but is in state RLAudioEngineStateInterrupted
 * The audio pipeline is not touched executing this method (no end , begin, nor ts reinitialisation) and stays active
 */
- (void)recover
{
    [self updateAudioConfiguration:true];
    [self checkAndRecoverEngineErrors];
}

- (void)addSourceOutput:(MFLOW_PCM_PUSHABLE *)output
{
    assert([NSThread isMainThread]);
    aengine_add_source_output_m(_engine, output, _mediator);
}

- (void)removeSourceOutput:(MFLOW_PCM_PUSHABLE *)output
{
    assert([NSThread isMainThread]);
    aengine_remove_source_output_m(_engine, output, _mediator);
}

/**
 * Current implementation is limited to one queue. If a vumeter is added with
 * a queue which is not the same as previous added vumeters, this methos does
 * nothing (ignores added vumeter).
 */
- (void)enableSourceToVUMeter:(MFLOW_VUMETER *)vumeter queue:(dispatch_queue_t)queue
{
    assert([NSThread isMainThread]);
    if (_source_vumeters.count == 0) {
        _source_pcm2vum = mflow_pcm_to_vumeter_create(queue, vumeter);
        _source_pcm2vum_queue = queue;
        aengine_add_source_output_m(_engine, &_source_pcm2vum->z, _mediator);
    } else if (queue == _source_pcm2vum_queue) {
        mflow_pcm_to_vumeter_add(_source_pcm2vum, vumeter);
    }
    c3_retain(&vumeter->z);
    GMU_LIST_ADD(_source_vumeters, vumeter);
}

- (void)disableSourceToVUMeter:(MFLOW_VUMETER *)vumeter
{
    assert([NSThread isMainThread]);
    for (int i = 0; i < _source_vumeters.count; i++) {
        if (_source_vumeters.items[i] == vumeter) {
            GMU_LIST_REMOVE(_source_vumeters, i);
            mflow_pcm_to_vumeter_remove(_source_pcm2vum, vumeter, NULL, NULL);
            c3_release(&vumeter->z);

            if (_source_vumeters.count == 0) {
                aengine_remove_source_output_m(_engine, &_source_pcm2vum->z, _mediator);
                c3_release(&_source_pcm2vum->z.z);
                _source_pcm2vum = NULL;
                _source_pcm2vum_queue = NULL;
            }
            break;
        }
    }
}

@end
