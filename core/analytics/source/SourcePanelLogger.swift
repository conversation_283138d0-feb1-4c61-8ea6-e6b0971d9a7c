//
//  SourcePanelLogger.swift
//  Switcher-Pro
//
//  Created by <PERSON> on 7/24/25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation
import UIKit

protocol SourcePanelLoggerProtocol {
    var isInitialized: Bool { get }
    func initialize(broadcastId: String)
    func addActivation(id: String, type: SourcePanelLogType)
    func addDeactivation(id: String, type: SourcePanelLogType)
    func addActivation(id: String, type: SourcePanelLogType, capture: UIImage?)
    func addDeactivation(id: String, type: SourcePanelLogType, capture: UIImage?)
    func clear()
    func reportLogs(broadcastDurationSeconds: Int32)
    func estimateUploadTime() -> Int64
    func resetUploadTracking()
}



class SourcePanelLoggerMock: SourcePanelLoggerProtocol {
    var isInitialized: Bool { false }
    func initialize(broadcastId: String) { }
    func addActivation(id: String, type: SourcePanelLogType) { }
    func addDeactivation(id: String, type: SourcePanelLogType) { }
    func addActivation(id: String, type: SourcePanelLogType, capture: UIImage?) { }
    func addDeactivation(id: String, type: SourcePanelLogType, capture: UIImage?) { }
    func clear() { }
    func reportLogs(broadcastDurationSeconds: Int32) {
        // Mock implementation - no action needed
    }
    func estimateUploadTime() -> Int64 { return 0 }
    func resetUploadTracking() { }
}

/// The `SourcePanelLogger` is designed to log the events of the sources and their usages during a broadcast.
/// A dictionary is kept to track the logs and ensure that we are not keeping redundant data in the logger.
class SourcePanelLogger: SourcePanelLoggerProtocol {
    // MARK: - Constants
    private static let broadcastLengthThreshold: Int32 = 60 * 40 // 40 minutes in seconds

    // MARK: - Properties
    private var broadcastId: String = ""
    private var logDictionary: [String: SourcePanelLog] = [:]

    // Capture management
    private var captureManager: SourceCaptureManager?
    private var shouldCaptureImages: Bool = false

    // Upload state management
    private var isUploadInProgress: Bool = false
    private var uploadTask: Task<Void, Never>?

    private let logger = LsLogger(subsystem: "swi.analytics", category: "SourcePanelLogger")

    var isInitialized: Bool { !broadcastId.isEmpty }

    // MARK: - Private functions

    /// Create a new log for the source. If the source has already been used. Append it to existing source logs,
    /// else create a new entry in the dictionary.
    ///
    /// - Parameters:
    ///     - id: the source ID
    ///     - type: what kind of source was used
    ///     - isActive: true if the source is enterring the production, false if it is leaving
    ///     - captureId: optional capture filename for this event
    private func log(id: String, type: SourcePanelLogType, isActive: Bool, captureId: String? = nil) {
        if isInitialized {
            // if  ids are equal append count
            let timestamp = SourcePanelTimestamp(isActive: isActive, captureId: captureId)
            if var logs = logDictionary[id] {
                logs.stamps.append(timestamp)
                // Add capture ID to the log if provided
                if let captureId = captureId, !logs.captureIds.contains(captureId) {
                    logs.captureIds.append(captureId)
                }
                logDictionary[id] = logs
            } else {
                let captureIds = captureId != nil ? [captureId!] : []
                let log = SourcePanelLog(sourceId: id, type: type, stamps: [timestamp], captureIds: captureIds)
                logDictionary[id] = log
            }
        }
    }

    // MARK: - Public Functions
    /// Initialize a new set of logs for the given broadcast ID
    ///
    /// - Parameters:
    ///     - id: the broadcast ID
    func initialize(broadcastId: String) {
        // Check if source logging feature is enabled
        guard isSourceLoggingFeatureEnabled() else {
            logger.info("Source logging feature disabled - skipping initialization")
            return
        }

        // Cancel any running upload from previous production
        if isUploadInProgress {
            logger.warning("Cancelling previous upload for new production: \(self.broadcastId) -> \(broadcastId)")
            uploadTask?.cancel()
            uploadTask = nil
            isUploadInProgress = false

            // Clean up previous capture manager
            captureManager?.cleanup()
        }

        self.broadcastId = broadcastId
        self.logDictionary = [:]

        // Initialize capture manager and check if captures should be taken
        self.captureManager = SourceCaptureManager(broadcastId: broadcastId)
        self.shouldCaptureImages = captureManager?.shouldCaptureImages() ?? false

        if shouldCaptureImages {
            logger.info("Image capture enabled for broadcast: \(broadcastId)")
        } else {
            logger.info("Image capture disabled for broadcast: \(broadcastId)")
        }
    }

    /// Clear the logs and broadcast ID
    func clear() {
        logDictionary = [:]
        broadcastId = ""

        // Clean up capture manager
        captureManager?.cleanup()
        captureManager = nil
        shouldCaptureImages = false

        // Note: Don't clear upload state here as this may be called during upload
        // Upload state is managed separately in initialize() and reportLogs()
    }

    /// Check if there are captures to upload
    func hasCapturesToUpload() -> Bool {
        return captureManager?.getCaptureFiles().isEmpty == false
    }

    /// Get estimated number of captures that will be uploaded
    func estimatedCaptureCount() -> Int {
        return captureManager?.getCaptureFiles().count ?? 0
    }

    /// Estimate the time needed to upload captures in microseconds
    func estimateUploadTime() -> Int64 {
        // Direct synchronous call - no thread switching
        return captureManager?.estimateUploadTime() ?? 0
    }

    /// Reset upload tracking (called when starting a new production)
    func resetUploadTracking() {
        // Direct synchronous call - no thread switching
        captureManager?.resetUploadTracking()
    }

    /// Add a source activation event
    ///
    /// - Parameters:
    ///     - id: the source ID
    ///     - type: what kind of source was used
    func addActivation(id: String, type: SourcePanelLogType) {
        log(id: id, type: type, isActive: true)
    }

    /// Add a source deactivation event
    ///
    /// - Parameters:
    ///     - id: the source ID
    ///     - type: what kind of source was used
    func addDeactivation(id: String, type: SourcePanelLogType) {
        log(id: id, type: type, isActive: false)
    }

    /// Add a source activation event with capture
    ///
    /// - Parameters:
    ///     - id: the source ID
    ///     - type: what kind of source was used
    ///     - capture: the capture image to upload
    func addActivation(id: String, type: SourcePanelLogType, capture: UIImage?) {
        // Ensure we're on the main thread for logging and capture processing
        guard Thread.isMainThread else {
            DispatchQueue.main.async {
                self.addActivation(id: id, type: type, capture: capture)
            }
            return
        }

        log(id: id, type: type, isActive: true)
        saveCaptureIfNeeded(id: id, type: type, capture: capture, isActivation: true)
    }

    /// Add a source deactivation event with capture
    ///
    /// - Parameters:
    ///     - id: the source ID
    ///     - type: what kind of source was used
    ///     - capture: the capture image to upload
    func addDeactivation(id: String, type: SourcePanelLogType, capture: UIImage?) {
        // Ensure we're on the main thread for logging and capture processing
        guard Thread.isMainThread else {
            DispatchQueue.main.async {
                self.addDeactivation(id: id, type: type, capture: capture)
            }
            return
        }

        log(id: id, type: type, isActive: false)
        saveCaptureIfNeeded(id: id, type: type, capture: capture, isActivation: false)
    }

    /// Send the Source Panel Logs to Analytics and upload captures, but only if the broadcast is more
    /// than 40 minutes long. Clears the logs whether or not data is reported
    ///
    /// - Parameters:
    ///     - broadcastDurationSeconds: Length of the broadcast in seconds
    func reportLogs(broadcastDurationSeconds: Int32) {
        // We only want to reports logs that are from broadcasts longer than the threshold, or if the user is in Dev mode
        if isInitialized && (Self.broadcastLengthThreshold <= broadcastDurationSeconds || ConfigurationDevelopmentManager.shared.isDevProfile) {
            // Mark upload as in progress
            isUploadInProgress = true

            uploadTask = Task.detached(priority: .utility) {
                // Report analytics logs on main thread
                await MainActor.run {
                    let logs: [SourcePanelLog] = self.logDictionary.map { $1 }
                    Analytics.shared.reportEvent(
                        event: SourcePanelLogEvent.assetLogs(broadcastId: self.broadcastId,
                                                             logs: logs)
                    )
                    // Debug log output
                    self.logger.info("Source Panel Logs: \(logs)")
                }

                // Upload captures on background thread (non-critical operation)
                if let captureManager = self.captureManager {
                    do {
                        try await captureManager.uploadCaptures { _ in
                            // Progress tracking is handled internally by CaptureManager
                        }
                        self.logger.info("Capture upload completed successfully")
                    } catch {
                        // Log non-fatal error to Crashlytics but don't interrupt production finalization
                        self.logger.warning("Non-critical capture upload failed for broadcast: \(self.broadcastId) \(error.localizedDescription)")
                    }
                }

                // Clear state after upload completes on main thread
                await MainActor.run {
                    self.isUploadInProgress = false
                    self.uploadTask = nil
                    self.clear()
                }
            }
        } else {
            clear()
        }
    }

    // MARK: - Thumbnail Upload Logic

    /// Save capture locally if needed based on source type and conditions
    /// This is non-critical functionality that should never interrupt production
    private func saveCaptureIfNeeded(id: String, type: SourcePanelLogType, capture: UIImage?, isActivation: Bool) {
        // Ensure we're on the main thread for capture processing
        guard Thread.isMainThread else {
            DispatchQueue.main.async {
                self.saveCaptureIfNeeded(id: id, type: type, capture: capture, isActivation: isActivation)
            }
            return
        }

        guard isInitialized,
              shouldCaptureImages,
              let capture = capture,
              let captureManager = captureManager else {
            return
        }

        // Determine if we should save based on source type
        let shouldSave = shouldSaveCapture(for: type, sourceId: id, isActivation: isActivation)

        if shouldSave {
            // Perform capture save on background thread to avoid blocking production
            Task.detached(priority: .utility) {
                do {
                    if let filename = try await captureManager.saveCaptureSafely(capture, sourceId: id, sourceType: type, isActivation: isActivation) {
                        // Update log with capture filename on main thread
                        await MainActor.run {
                            self.log(id: id, type: type, isActive: isActivation, captureId: filename)
                        }
                    }
                } catch {
                    // Log non-fatal warning but don't interrupt production
                    self.logger.warning("Non-critical capture save failed for \(type.id)_\(id): \(error.localizedDescription)")
                }
            }
        }
    }

    /// Determine if capture should be saved based on source type and rules
    private func shouldSaveCapture(for type: SourcePanelLogType, sourceId: String, isActivation: Bool) -> Bool {
        switch type {
        case .timer, .graphic, .multiView, .image, .video:
            // Static sources: save only once per source (on first activation)
            return !hasStaticSourceBeenLogged(sourceId: sourceId, type: type)

        case .liveInput, .screenShare, .remoteGuest:
            // Dynamic sources: save for each activation and deactivation
            return true
        }
    }

    /// Check if a static source has already been logged with captures
    private func hasStaticSourceBeenLogged(sourceId: String, type: SourcePanelLogType) -> Bool {
        guard let log = logDictionary[sourceId] else { return false }
        return log.type == type.id && !log.captureIds.isEmpty
    }

    /// Check if source logging feature is enabled
    private func isSourceLoggingFeatureEnabled() -> Bool {
        // Always enabled in dev profile for testing
        if ConfigurationDevelopmentManager.shared.isDevProfile {
            return true
        }

        // Check if user has the sourcelogs feature claim
        return SSNUserAccount.shared.userInfo?.isSourceLoggingEnabled ?? false
    }

}
