//
//  SourcePanelLog.swift
//  Cap-iOS
//
//  Created by <PERSON> on 7/25/25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation

struct SourcePanelLog: Codable {
    // MARK: - Properties
    let sourceId: String
    let isRemote: Bool
    let isOverlay: Bool
    let isMacroshape: Bool
    let type: String
    var stamps: [SourcePanelTimestamp]
    var captureIds: [String] // Array of uploaded capture IDs

    // MARK: - Initializers
    init(sourceId: String, type: SourcePanelLogType, stamps: [SourcePanelTimestamp], captureIds: [String] = []) {
        self.sourceId = sourceId
        self.isRemote = type.isRemote
        self.isOverlay = type.isOverlay
        self.isMacroshape = type.isMacroshape
        self.type = type.id
        self.stamps = stamps
        self.captureIds = captureIds
    }

    /// Generate capture filename from a timestamp entry
    ///
    /// - Parameters:
    ///     - timestamp: the timestamp entry containing ordinal and activation info
    /// - Returns: the capture filename in format: {ordinal}_{sourceType}_{actionType}_{channelId}.jpg
    func captureFilename(for timestamp: SourcePanelTimestamp) -> String? {
        guard let ordinal = timestamp.ordinal else { return nil }
        let actionType = timestamp.isActive ? "activation" : "deactivation"
        return "\(ordinal)_\(type)_\(actionType)_\(sourceId).jpg"
    }

    /// Generate capture directory path for a given date and broadcast ID
    ///
    /// - Parameters:
    ///     - date: the date for the directory structure
    ///     - broadcastId: the broadcast ID
    /// - Returns: the directory path: Documents/SourceCaptures/yyyy-MM-dd/broadcastId/
    static func captureDirectoryPath(for date: Date, broadcastId: String) -> URL {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let dateString = dateFormatter.string(from: date)

        return documentsPath
            .appendingPathComponent("SourceCaptures")
            .appendingPathComponent(dateString)
            .appendingPathComponent(broadcastId)
    }
}


// MARK: - SourcePanelTimestamp
struct SourcePanelTimestamp: Codable {
    // MARK: - Properties
    let time: Date
    let isActive: Bool
    let captureId: String? // ID of uploaded capture for this timestamp
    let ordinal: Int? // Chronological order number for capture filename

    // MARK: - Initializers
    init(time: Date = Date(), isActive: Bool, captureId: String? = nil, ordinal: Int? = nil) {
        self.time = time
        self.isActive = isActive
        self.captureId = captureId
        self.ordinal = ordinal
    }
}
