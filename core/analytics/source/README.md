# Source Analytics Module

This module provides comprehensive analytics and capture functionality for tracking source usage during broadcasts in the Switcher-Pro application. It logs source activations/deactivations and optionally captures visual snapshots for analysis.

## Overview

The Source Analytics module consists of six main components that work together to:
- Track when sources are activated and deactivated during broadcasts
- Capture visual snapshots of sources for analysis
- Upload captured data to Azure blob storage
- Provide analytics data for broadcast optimization

## Components

### 1. SourcePanelLogger (`SourcePanelLogger.swift`)

**Purpose**: Main orchestrator for source analytics logging and capture management.

**Key Features**:
- Tracks source activation/deactivation events during broadcasts
- Coordinates with SourceCaptureManager for capture collection
- Uploads analytics data and captures after broadcasts (only for broadcasts > 40 minutes or in Dev mode)
- Thread-safe operations with proper main thread handling for UI captures

**Key Methods**:
- `initialize(broadcastId:)` - Initialize logger for a new broadcast
- `addActivation(id:type:capture:)` - Log source activation with optional capture
- `addDeactivation(id:type:capture:)` - Log source deactivation with optional capture
- `reportLogs(broadcastDurationSeconds:)` - Upload analytics and captures at broadcast end
- `estimateUploadTime()` - Estimate time needed for capture uploads

**Configuration**:
- Only reports data for broadcasts longer than 40 minutes (or in Dev mode)
- Delegates capture decisions to SourceCaptureManager
- Manages upload state to prevent concurrent uploads

### 2. SourceCaptureManager (`SourceCaptureManager.swift`)

**Purpose**: Manages local capture storage during production and batch upload at broadcast end.

**Key Features**:
- Intelligent capture decision-making based on network, storage, and sampling conditions
- Manages capture sampling rates (10% default, 100% in Dev mode)
- Local file storage with automatic cleanup
- Batch upload optimization with progress tracking
- Storage management (50MB max, 10% minimum free storage)
- Network condition checking (10Mbps minimum upload speed)

**Capture Rules**:
- **Static sources** (timer, graphic, multiView, image, video): Captured once per source
- **Dynamic sources** (liveInput, screenShare, remoteGuest): Captured on each activation/deactivation

**Key Methods**:
- `shouldCaptureImages()` - Determines if captures should be taken based on conditions
- `saveCaptureSafely(_:sourceId:sourceType:isActivation:)` - Save capture during production
- `uploadAllCaptures()` - Batch upload all captures at broadcast end
- `cleanup()` - Clean up local storage after upload

### 3. SourceCaptureUploader (`SourceCaptureUploader.swift`)

**Purpose**: Handles individual capture upload to Azure blob storage using a two-step process.

**Upload Process**:
1. **Step 1**: Call API with metadata to get Azure upload URL
2. **Step 2**: Upload JPEG file to Azure using the provided URL

**Filename Format**: `{ordinal}_{sourceType}_{actionType}_{channelId}.jpg`
- Example: `1_liveInput_activation_camera1.jpg`

**Key Features**:
- Actor-based implementation for thread safety
- Automatic JPEG compression (80% quality)
- Temporary file management with cleanup
- Delegate pattern for upload completion handling

### 4. SourcePanelLog (`SourcePanelLog.swift`)

**Purpose**: Data structure representing a complete log entry for a source.

**Properties**:
- `sourceId`: Unique identifier for the source
- `isRemote`: Whether the source is remote
- `isOverlay`: Whether the source is an overlay
- `isMacroshape`: Whether the source is a macroshape graphic
- `type`: String representation of the source type
- `stamps`: Array of activation/deactivation timestamps
- `captureIds`: Array of uploaded capture filenames

### 5. SourcePanelLogType (`SourcePanelLogType.swift`)

**Purpose**: Enum defining all supported source types with their characteristics.

**Source Types**:
- `liveInput(isRemote: Bool)` - Live camera inputs
- `screenShare` - Screen sharing sources
- `remoteGuest` - Remote guest connections
- `multiView` - Multi-view compositions
- `image(isOverlay: Bool)` - Static images
- `video(isOverlay: Bool)` - Video files
- `graphic(isOverlay: Bool, isMacroshape: Bool)` - Graphics (lower thirds, text, macroshapes)
- `timer` - Timer graphics

**Properties**:
- `id`: String identifier for the type
- `isRemote`: Whether the source type is remote
- `isOverlay`: Whether the source type is an overlay
- `isMacroshape`: Whether the source type is a macroshape

### 6. SourcePanelLogEvent (`SourcePanelLogEvent.swift`)

**Purpose**: Analytics event wrapper for sending source logs to the analytics system.

**Event Type**:
- `assetLogs(broadcastId: String, logs: [SourcePanelLog])` - Contains all source logs for a broadcast

## Data Flow

```
1. Broadcast starts → SourcePanelLogger.initialize()
2. Source activated → addActivation() → SourceCaptureManager.saveCapture() (if conditions met)
3. Source deactivated → addDeactivation() → SourceCaptureManager.saveCapture() (if conditions met)
4. Broadcast ends → reportLogs() → Upload analytics + SourceCaptureManager.uploadAllCaptures()
5. Cleanup → Remove local files
```

## Configuration

### Sampling Rates
- **Production**: 10% default sampling rate
- **Development**: 100% sampling rate (when `ConfigurationDevelopmentManager.shared.isDevProfile` is true)

### Storage Limits
- **Maximum capture storage**: 50MB
- **Minimum free storage**: 10% of device storage
- **Required upload speed**: 10Mbps minimum

### Reporting Thresholds
- **Minimum broadcast length**: 40 minutes (bypassed in Dev mode)
- **Feature flag**: `sourcelogs` must be enabled

## Usage Example

```swift
// Initialize for a new broadcast
logger.initialize(broadcastId: "broadcast_123")

// Log source activation with capture
logger.addActivation(
    id: "camera1", 
    type: .liveInput(isRemote: false), 
    capture: cameraImage
)

// Log source deactivation
logger.addDeactivation(
    id: "camera1", 
    type: .liveInput(isRemote: false)
)

// At broadcast end, upload data
logger.reportLogs(broadcastDurationSeconds: 3600) // 1 hour broadcast
```

## Thread Safety

- All public methods are thread-safe
- UI captures are processed on the main thread
- File I/O and uploads occur on background threads
- Actor-based implementation for upload components

## Error Handling

- Non-critical capture failures don't interrupt production
- Failed uploads are logged but don't block broadcast completion
- Automatic cleanup prevents storage buildup on failures
- Graceful degradation when network/storage conditions are poor
