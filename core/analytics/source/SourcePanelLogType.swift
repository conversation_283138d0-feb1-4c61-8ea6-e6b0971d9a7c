//
//  SourcePanelLogType.swift
//  Cap-iOS
//
//  Created by <PERSON> on 7/25/25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation

enum SourcePanelLogType: Codable {
    // MARK: - Cases
    case liveInput(isRemote: Bool)
    case screenShare
    case remoteGuest
    case multiView
    case image(isOverlay: Bool)
    case video(isOverlay: Bool)
    case graphic(isOverlay: Bool, isMacroshape: Bool) // e.g. Lower third, Text, Macroshape
    case timer

    // MARK: - Properties
    var id: String {
        return switch self {
        case .liveInput: "liveInput"
        case .screenShare: "screenShare"
        case .remoteGuest: "remoteGuest"
        case .multiView: "multiView"
        case .image: "image"
        case .video: "video"
        case .graphic: "graphic"
        case .timer: "timer"
        }
    }

    var isOverlay: Bool {
        return switch self {
        case .liveInput, .screenShare, .remoteGuest, .multiView: false
        case .timer: true
        case .image(let isOverlay),
                .video(let isOverlay),
                .graphic(let isOverlay, _): isOverlay
        }
    }

    var isRemote: Bool {
        return switch self {
        case .liveInput(let isRemote): isRemote
        case .screenShare, .remoteGuest: true
        case .multiView, .image, .video, .graphic, .timer: false
        }
    }
    
    var isMacroshape: Bool {
        return switch self {
        case .graphic(_, let isMacroshape): isMacroshape
        default: false
        }
    }

    // MARK: - Static Methods

    /// Create SourcePanelLogType from string ID
    /// Note: This creates a default version of each type since we don't have the full context
    static func from(id: String) -> SourcePanelLogType? {
        switch id {
        case "liveInput": return .liveInput(isRemote: false)
        case "screenShare": return .screenShare
        case "remoteGuest": return .remoteGuest
        case "multiView": return .multiView
        case "image": return .image(isOverlay: false)
        case "video": return .video(isOverlay: false)
        case "graphic": return .graphic(isOverlay: false, isMacroshape: false)
        case "timer": return .timer
        default: return nil
        }
    }
}
