//
//  CloudSettings.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON> on 17.08.20.
//  Copyright © 2020 Switcher Inc. All rights reserved.
//

import Foundation
import InternalBridging

class CloudSettings {

    private enum State {
        case unfetched
        case fetching
        case fetched
        case failedToFetch
    }

    static let shared = CloudSettings()

    private let emulateSlowNetwork = false
    private var state = State.unfetched
    private var settings: SwitcherUrls?
    private var pendingCompletions = [() -> Void]()
    var task: Task<Void, any Error>?
    var timeoutTask: Task<Void, any Error>?

    private func firePendingCompletions() {
        let completions = pendingCompletions
        pendingCompletions.removeAll()
        if self.emulateSlowNetwork {
            DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) {
                for completion in completions {
                    completion()
                }
            }
        } else {
            for completion in completions {
                completion()
            }
        }
    }

    private func fetchAll(completion: @escaping () -> Void) {
        switch state {
        case .unfetched, .failedToFetch:
            pendingCompletions.append(completion)
            state = .fetching

            task = Task {
                do {
                    let result = try await UserAccountAPI.shared.fetchSettings()
                    try Task.checkCancellation()
                    timeoutTask?.cancel()
                    timeoutTask = nil
                    self.settings = result
                    self.state = .fetched
                } catch {
                    self.state = .failedToFetch
                }
                self.firePendingCompletions()
                task = nil
            }

            // Timeout
            timeoutTask = Task {
                try await Task.sleep(nanoseconds: 20_000_000_000)
                try Task.checkCancellation()
                self.task?.cancel()
                self.task = nil
                self.timeoutTask = nil
            }
        case .fetched:
            completion()
        case .fetching:
            pendingCompletions.append(completion)
        }
    }

    func prefetch() {
        fetchAll(completion: {})
    }

    func fetchCreateAccountUrl(completion: @escaping (_ url: URL) -> Void) {
        fetchAll {
            if let urlStr = self.settings?.createAccountUrl,
                let url = URL(string: urlStr) {
                completion(url)
            } else {
                completion(URL(string: "https://dashboard.switcherstudio.com/register-external")!)
            }
        }
    }

    func fetchScheduleCallUrl(completion: @escaping (_ url: String?) -> Void) {
        fetchAll { completion(self.settings?.scheduleCallUrl) }
    }

    func fetchGetStartedCatalogUrl(completion: @escaping (_ url: String?) -> Void) {
        fetchAll { completion(self.settings?.getStartedCatalogUrl) }
    }

    func fetchSourceLogsSamplingRate(completion: @escaping (_ samplingRate: Double) -> Void) {
        fetchAll {
            let samplingRate = self.settings?.sourceLogsSamplingRate ?? 0.1
            completion(samplingRate)
        }
    }
}
