//
//  SwiUserInfo.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON> on September 6, 2018.
//  Copyright © 2018 Switcher Inc. All rights reserved.
//

import Foundation
import InternalBridging

protocol SwiUserInfoDelegate: AnyObject {
    func userInfoRefreshDidBegin()
    func userInfoRefreshDidEnd(success: Bool)
    func subscriptionSourceChanged()
}

protocol SwiUserInfoProtocol {
    var projectId: String? { get }
    var hasRecordOnlyAutoSave: Bool { get }
}

@objc public class SwiUserInfo: NSObject, SwiUserInfoProtocol {
    public enum Role: String {
        case professional = "Professional"
        case trial = "Trial"
        case facebookCreator = "FbCreator"
        case orgAdmin = "OrgAdmin"
        case orgContributor = "OrgContributor"
    }

    public enum FeatureClaim: String {
        case nowatermark = "nowatermark"
        case cloudassets = "cloudassets"
        case insecurertmps = "insecurertmps"
        case alternativertmps = "alternativertmps"
        case shopify = "shopify"
        case inappsubscriptions = "inappsubscriptions"
        case chromakey = "chromakey"
        case eptz = "eptz"
        case recordOnlyAutoSave = "autosave"
        case gatedcontent = "gatedcontent"
        case cloudflarecaptions = "cloudflarecaptions"
        case clarity = "clarityios"
        case disabledspeedtest = "disabledspeedtest"
        case sourcelogs = "sourcelogs"
    }

    /**
     * A nil value means that the information has not been retrieved
     * from the server since the user logged in.
     */
    public private(set) var email: String? {
        get {
            return UserDefaults.standard.object(forKey: "swiUserInfo.email") as? String
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "swiUserInfo.email")
        }
    }

    /**
     * A nil value means that the information has not been retrieved
     * from the server since the user logged in.
     */
    public private(set) var userName: String? {
        get {
            return UserDefaults.standard.object(forKey: "swiUserInfo.name") as? String
        }
        set {
            if let userName = newValue {
                UserDefaults.standard.set(userName, forKey: "swiUserInfo.name")
            } else {
                UserDefaults.standard.removeObject(forKey: "swiUserInfo.name")
            }
        }
    }

    /**
     * A nil value means that the information has not been retrieved
     * from the server since the user logged in.
     */
    public private(set) var planName: String? {
        get {
            return UserDefaults.standard.object(forKey: "swiUserInfo.planName") as? String
        }
        set {
            if let planName = newValue {
                UserDefaults.standard.set(planName, forKey: "swiUserInfo.planName")
            } else {
                UserDefaults.standard.removeObject(forKey: "swiUserInfo.planName")
            }
        }
    }

    /**
     * A nil value means that the information has not been retrieved
     * from the server since the user logged in.
     */
    public private(set) var forceEmailConfirmation: Bool? {
        get {
            return UserDefaults.standard.object(forKey: "swiUserInfo.forceEmailConfirmation") as? Bool
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "swiUserInfo.forceEmailConfirmation")
        }
    }

    /**
     * A nil value means that the information has not been retrieved
     * from the server since the user logged in.
     */
    public private(set) var projectId: String? {
        get {
            return UserDefaults.standard.object(forKey: "swiUserInfo.projectId") as? String
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "swiUserInfo.projectId")
        }
    }

    public private(set) var activeUntil: Date? {
        get {
            return UserDefaults.standard.object(forKey: "swiUserInfo.activeUntil") as? Date
        }
        set {
            if let activeUntil = newValue {
                UserDefaults.standard.set(activeUntil, forKey: "swiUserInfo.activeUntil")
            } else {
                UserDefaults.standard.removeObject(forKey: "swiUserInfo.activeUntil")
            }
        }
    }

    public private(set) var isRecurring: Bool? {
        get {
            return UserDefaults.standard.object(forKey: "swiUserInfo.isRecurring") as? Bool
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "swiUserInfo.isRecurring")
        }
    }

    // Check status properties

    public private(set) var status: SubscriptionStatus? {
        get {
            if simulateSubscription {
                return .active
            }
            if let status = UserDefaults.standard.object(forKey: "swiUserInfo.status") as? String {
                return SubscriptionStatus(rawValue: status)
            }
            return .unknown
        }
        set {
            UserDefaults.standard.set(newValue?.rawValue, forKey: "swiUserInfo.status")
        }
    }

    public private(set) var subscriptionSource: SubscriptionSource? {
        get {
            if simulateSubscription {
                return .appstore
            }
            if let status = UserDefaults.standard.object(forKey: "swiUserInfo.subscriptionSource") as? String {
                return SubscriptionSource(rawValue: status)
            }
            return SubscriptionSource.unknown
        }
        set {
            UserDefaults.standard.set(newValue?.rawValue, forKey: "swiUserInfo.subscriptionSource")

            for delegate in delegates {
                delegate.subscriptionSourceChanged()
            }
        }
    }

    public var isDashboardUser: Bool {
        return subscriptionSource == .shopify || subscriptionSource == .stripe
    }

    public var isBusinessOrAbove: Bool {
        if let planName = planName?.lowercased() {
            return planName.contains("business") || planName.contains("merchant")
        }
        return false
    }

    public var isStatusActive: Bool {
        return status == .active
    }

    public var isStatusTrialing: Bool {
        return status == .trialing
    }

    public var isStatusCanceled: Bool {
        return status == .canceled || cancelAtPeriodEnd
    }

    public var isStatusEnded: Bool {
        return status == .ended
    }

    // To debug and display error message
    // overwrite this and return true
    public var isStatusError: Bool {
        return status == .incomplete ||
        status == .incomplete_expired ||
        status == .past_due ||
        status == .unpaid ||
        status == .unknown
    }

    public private(set) var cancelAtPeriodEnd: Bool {
        get {
            return UserDefaults.standard.bool(forKey: "swiUserInfo.cancelAtPeriodEnd")
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "swiUserInfo.cancelAtPeriodEnd")
        }
    }

    /**
     * A nil value means that the information has not been retrieved
     * from the server since the user logged in.
     */
    public private(set) var canReviewAppAt: Date? {
        get {
            return UserDefaults.standard.object(forKey: "swiUserInfo.canReviewAppAt") as? Date
        }
        set {
            if let canReviewAppAt = newValue {
                UserDefaults.standard.set(canReviewAppAt, forKey: "swiUserInfo.canReviewAppAt")
            } else {
                UserDefaults.standard.removeObject(forKey: "swiUserInfo.canReviewAppAt")
            }
        }
    }

    /**
     * A nil value means that the information has not been retrieved
     * from the server since the user logged in.
     */
    public private(set) var accountCreatedDate: Date? {
        get {
            return UserDefaults.standard.object(forKey: "swiUserInfo.accountCreatedDate") as? Date
        }
        set {
            if let accountCreatedDate = newValue {
                UserDefaults.standard.set(accountCreatedDate,
                                          forKey: "swiUserInfo.accountCreatedDate")
            } else {
                UserDefaults.standard.removeObject(forKey: "swiUserInfo.accountCreatedDate")
            }
        }
    }

    /**
     * A nil value means that the information has not been retrieved
     * from the server since the user logged in.
     */
    public private(set) var rawRoles: [String]? {
        get {
            return UserDefaults.standard.array(forKey: "swiUserInfo.roles") as? [String]
        }
        set {
            if let roles = newValue {
                UserDefaults.standard.set(roles, forKey: "swiUserInfo.roles")
            } else {
                UserDefaults.standard.removeObject(forKey: "swiUserInfo.roles")
            }
        }
    }

    /**
     * A nil value means that the information has not been retrieved
     * from the server since the user logged in.
     */
    public var roles: Set<Role>? {
        if let roleArray = rawRoles {
            var roles = Set<Role>(minimumCapacity: roleArray.count)
            for roleStr in roleArray {
                if let role = Role(rawValue: roleStr) {
                    roles.insert(role)
                }
            }
            return roles
        } else {
            return nil
        }
    }

    /**
     * A nil value means that the information has not been retrieved
     * from the server since the user logged in.
     */
    public private(set) var rawFeatureClaims: [String]? {
        get {
            return UserDefaults.standard.array(forKey: "swiUserInfo.featureClaims") as? [String]
        }
        set {
            if let claims = newValue {
                UserDefaults.standard.set(claims, forKey: "swiUserInfo.featureClaims")
            } else {
                UserDefaults.standard.removeObject(forKey: "swiUserInfo.featureClaims")
            }
        }
    }

    /**
     * A nil value means that the information has not been retrieved
     * from the server since the user logged in.
     */
    public var featureClaims: Set<FeatureClaim>? {
        if let claimArray = rawFeatureClaims {
            var claims = Set<FeatureClaim>(minimumCapacity: claimArray.count)
            for claimStr in claimArray {
                if let claim = FeatureClaim(rawValue: claimStr) {
                    claims.insert(claim)
                }
            }
            return claims
        } else {
            return nil
        }
    }

    /**
         * A nil value means that the information has not been retrieved
         * from the server since the user logged in.
         */
        public private(set) var featureLimitClaims: FeatureLimitClaims? {
            get {
                if let data = UserDefaults.standard.data(forKey: UserDefaultsHelper.featureLimitClaims) {
                    let decoder = JSONDecoder()
                    return try? decoder.decode(FeatureLimitClaims.self, from: data)
                }
                return nil
            }
            set {
                if let claims = newValue {
                    let encoder = JSONEncoder()
                    if let encoded = try? encoder.encode(claims) {
                        UserDefaults.standard.set(encoded, forKey: UserDefaultsHelper.featureLimitClaims)
                    }
                } else {
                    UserDefaults.standard.removeObject(forKey: UserDefaultsHelper.featureLimitClaims)
                }
            }
        }

    @objc public var simulateSubscription: Bool {
        get {
// only use in A1 and B1 builds
#if SWI_PRODUCT_DISPLAY_NAME_SUFFIX
            if let storedDate = UserDefaults.standard.object(forKey: "swiUserInfo.simulateSubscription") as? Date {
                if storedDate.addingTimeInterval(60.0 * 1.0) >= Date.now {
                    return true
                } else {
                    UserDefaults.standard.removeObject(forKey: "swiUserInfo.simulateSubscription")
                    return false
                }
            }
#endif
            return false
        }
        set {
// only use in A1 and B1 builds
#if SWI_PRODUCT_DISPLAY_NAME_SUFFIX
            if newValue {
                UserDefaults.standard.set(Date.now, forKey: "swiUserInfo.simulateSubscription")
            } else {
                UserDefaults.standard.removeObject(forKey: "swiUserInfo.simulateSubscription")
            }
#endif
        }
    }

    @objc public var isEligibleForTrial: Bool {
        return subscriptionSource == SubscriptionSource.none
    }

    @objc public var shouldPromptWithIAS: Bool {
        return [SubscriptionSource.none, SubscriptionSource.appstore].contains { $0 == subscriptionSource }
    }

    @objc public var hasWatermark: Bool {
        return !(featureClaims?.contains(.nowatermark) ?? false)
    }

    @objc public var isInTrialMode: Bool {
        return roles?.contains(.trial) ?? false
    }

    @objc public var isCloudAssetAllowed: Bool {
        return featureClaims?.contains(.cloudassets) ?? false
    }

    @objc public var hasInsecureRtmpsEnabled: Bool {
        return featureClaims?.contains(.insecurertmps) ?? false
    }

    @objc public var hasAlternativeRtmpsEnabled: Bool {
        return featureClaims?.contains(.alternativertmps) ?? false
    }

    @objc var hasShopifyIntegration: Bool {
        return featureClaims?.contains(.shopify) ?? false
    }

    @objc var showChromakey: Bool {
        return featureClaims?.contains(.chromakey) ?? false
    }

    @objc var showEPTZ: Bool {
        return featureClaims?.contains(.eptz) ?? false
    }

    @objc var hasRecordOnlyAutoSave: Bool {
        return featureClaims?.contains(.recordOnlyAutoSave) ?? false
    }

    @objc var hasCloudflareCaptions: Bool {
        return featureClaims?.contains(.cloudflarecaptions) ?? false
    }

    @objc var hasDisabledSpeedTest: Bool {
        return featureClaims?.contains(.disabledspeedtest) ?? false
    }

    @objc var isClarityEnabled: Bool {
        return featureClaims?.contains(.clarity) ?? false
    }

    @objc var isSourceLoggingEnabled: Bool {
        return featureClaims?.contains(.sourcelogs) ?? false
    }

    @objc public func refresh() {
        print("SwiUserInfo: start refreshing")
        if let refresh = refreshTask {
            print("SwiUserInfo: cancel and restart")
            refresh.cancel()
        } else {
            fireUserInfoRefreshDidBegin()
        }

        refreshTask = Task {
            do {
                let info = try await UserAccountAPI.shared.getUserInfo()
                self.email = info.user?.details?.email ?? "[unknown]"
                self.forceEmailConfirmation = info.metaData?.details?.forceEmailConfirmation ?? false
                self.rawRoles = info.claims?.details?.roles ?? []
                self.rawFeatureClaims = info.claims?.details?.featureClaims ?? []
                self.featureLimitClaims = info.claims?.details?.featureLimitClaims
                self.planName = info.plan?.details?.name ?? "Unknown Plan"
                self.projectId = info.metaData?.details?.projectId
                if let firstName = info.user?.details?.firstName,
                   let lastName = info.user?.details?.lastName {
                    self.userName = "\(firstName) \(lastName)"
                } else {
                    self.userName = nil
                }

                if let canReviewAppAt = info.metaData?.details?.canReviewAppAt {
                    self.canReviewAppAt = self.convertStringToDate(canReviewAppAt)
                    if self.canReviewAppAt ==  nil {
                        print("SwiUserInfo::refresh issue while converting canReviewAppAt into date")
                    }
                } else {
                    self.canReviewAppAt = nil
                }

                if let createdAt = info.user?.details?.createdAt {
                    self.accountCreatedDate = self.convertStringToDate(createdAt)
                    if self.accountCreatedDate ==  nil {
                        print("SwiUserInfo::refresh issue while converting accountCreatedDate into date")
                    }
                } else {
                    self.accountCreatedDate = nil
                }

                if let activeUntil = info.plan?.details?.activeUntil {
                    self.activeUntil = self.convertStringToDate(activeUntil)
                } else {
                    self.activeUntil = nil
                }

                self.isRecurring = info.plan?.details?.isRecurring ?? false
                self.cancelAtPeriodEnd = info.plan?.details?.cancelAtPeriodEnd ?? false
                self.status = info.subcriptionStatus ?? .unknown
                self.subscriptionSource = info.subcriptionSource ?? .unknown

                print(
                    "SwiUserInfo: end refreshing: status=\(200) success=\(true) forceEmailConfirmation=\(String(describing: self.forceEmailConfirmation))"
                )
                self.refreshTask = nil
                self.fireUserInfoRefreshDidEnd(success: true)
            } catch {
                print("SwiUserInfo: end refreshing: error=\(error)")
                self.refreshTask = nil
                self.fireUserInfoRefreshDidEnd(success: false)
            }
        }
    }

    @objc public func cancel() {
        print("SwiUserInfo: cancel")
        if let refresh = refreshTask {
            refresh.cancel()
            refreshTask = nil
            self.fireUserInfoRefreshDidEnd(success: false)
        }
    }

    // invoked by SSNUserAccount
    @objc public func _setLoginState(_ state: UserAccountState) {
        if state == .loggedOut {
            cancel()
            email = nil
            forceEmailConfirmation = nil
            canReviewAppAt = nil
            rawRoles = nil
            rawFeatureClaims = nil
        }
    }

    // MARK: - request

    private var refreshTask: Task<Void, any Error>?

    public var isRefreshing: Bool {
        return refreshTask != nil
    }

    /*private func devTweaks() {
        #if CONFIG_SWITCHER_B1
        if let devProps = CapConf.devConf(), let roleMap = devProps["roles"] as? [String: Bool] {
            for (key, value) in roleMap {
                if value && !rawRoles!.contains(key) {
                    rawRoles!.append(key)
                }
                if !value {
                    rawRoles!.removeAll(where: {$0 == key})
                }
            }
        }
        if let devProps = CapConf.devConf(), let claimMap = devProps["featureClaims"] as? [String: Bool] {
            for (key, value) in claimMap {
                if value && !rawFeatureClaims!.contains(key) {
                    rawFeatureClaims!.append(key)
                }
                if !value {
                    rawFeatureClaims!.removeAll(where: {$0 == key})
                }
            }
        }
        #endif
    }*/

    // MARK: - Date
    private func convertStringToDate(_ strDate: String) -> Date? {

        let formatterIso8601 = ISO8601DateFormatter()
        formatterIso8601.formatOptions = [.withInternetDateTime]

        let date: Date? = formatterIso8601.date(from: strDate)

        if date == nil {
            // set date formatter with milliseconds
            // sometimes date comes from server with milliseconds
            let formatterIso8601withFractionalSeconds = ISO8601DateFormatter()
            formatterIso8601withFractionalSeconds.formatOptions = [.withInternetDateTime, .withFractionalSeconds]

            // apply formatter with new date format to get date
            let dateFix: Date? = formatterIso8601withFractionalSeconds.date(from: strDate)
            return dateFix
        }

        return date
    }

    func isNewerThanJuly29th() -> Bool {
        return self.accountCreatedDate?.isJuly29OrLater() ?? true
    }

    // MARK: - delegate

    private var delegates = WeakDelegateList<any SwiUserInfoDelegate>()

    final func addDelegate(_ delegate: any SwiUserInfoDelegate) {
        delegates.add(delegate)
    }

    final func removeDelegate(_ delegate: any SwiUserInfoDelegate) {
        delegates.remove(delegate)
    }

    private func fireUserInfoRefreshDidBegin() {
        for delegate in delegates {
            delegate.userInfoRefreshDidBegin()
        }
    }

    private func fireUserInfoRefreshDidEnd(success: Bool) {
        for delegate in delegates {
            delegate.userInfoRefreshDidEnd(success: success)
        }
    }
}

public struct FeatureLimitClaims: Codable {
    var cloudVideosMaxCount: Int?
    var cloudAssetsMaxCount: Int?
    var cloudFlareSimulcastingMaxDestinations: Int?

    private enum CodingKeys: String, CodingKey {
        case cloudVideosMaxCount = "cloudvideos:maxcount"
        case cloudAssetsMaxCount = "cloudassets:maxcount"
        case cloudFlareSimulcastingMaxDestinations = "cloudflaresimulcasting:maxdestinations"
    }
}

public enum SubscriptionStatus: String {
    case trialing
    case active
    case canceled
    case ended
    case incomplete
    case incomplete_expired
    case past_due
    case unpaid
    case unknown
}

public enum SubscriptionSource: String {
    case stripe = "Stripe"
    case shopify = "Shopify"
    case appstore = "AppStore"
    case none = "None"
    case unknown = "Unknown"
}
