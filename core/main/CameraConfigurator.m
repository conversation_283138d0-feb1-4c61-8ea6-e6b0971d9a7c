//
//  CameraConfigurator.m
//  Switcher
//
//  Created by <PERSON><PERSON> on October 17, 2013.
//  Copyright (c) 2013 Switcher Inc. All rights reserved.
//

#import "InternalSwift.h"
#import "CameraConfigurator.h"
#import "peer.h"
#import "RecProfile.h"
#include <gmutil/gmutil.h>
#include "prod_cap.h"


#define ADJUSTMENT_NO_DOWNSCALE                             0
#define ADJUSTMENT_BEST_HD_SOURCE_SIZE                      1
#define ADJUSTMENT_BEST_HD_SOURCE_SIZE_AND_DOWNSCALE_RATIO  2
#define ADJUSTMENT_BEST_DOWNSCALE_RATIO                     3
#define ADJUSTMENT_FULLY_FLEXIBLE                           4


struct video_conf {
    src_conf_type_t  type;
    src_conf_pos_t   position;
    int              system_rotation;
    struct src_conf_sptz sptz;
    src_conf_size_t  frame_size;  // in camera reference orientation
    float            frame_rate;  // 0=variable, see SRC_CONF_V_FRAME_RATE_*
    src_conf_size_t  stream_size; // in camera reference orientation
    struct src_conf_priority priority;
};

static src_conf_size_t _hd720 = {1280, 720};
static src_conf_size_t _hd1080 = {1920, 1080};

static bool _has_i1_4_ratio(src_conf_size_t big, src_conf_size_t small)
{
    if (!big.width || !big.height || !small.width || !small.height)
        return false;

    if ((big.width % small.width) != 0)
        return false;

    if ((big.height % small.height) != 0)
        return false;

    int r1 = big.width / small.width;
    int r2 = big.height / small.height;
    return r1 >= 1 && r1 <= 4 && r1 == r2;
}

/**
 * Downscale the first argument by an integer ratio from 1 to 4 in order to be equal
 * or lower than the second argument.
 * If it fails, it return {0, 0}
 */
static src_conf_size_t _over_rounded_int_ratio_downscale(src_conf_size_t from, src_conf_size_t to)
{
    src_conf_size_t ret;
    if (!from.width || !from.height)
        goto fail;
    for (int i=1; i<=4; i++) {
        ret.width  = (int)ceil((double)from.width / (double)i);
        ret.height = (int)ceil((double)from.height / (double)i);
        if (ret.width <= to.width && ret.height <= to.height)
            return ret;
    }

fail:
    ret.width  = 0;
    ret.height = 0;
    return ret;
}

static int _size_comparator(void *ctx, const void *e1, const void *e2) {
    const src_conf_size_t *s1 = e1;
    const src_conf_size_t *s2 = e2;
    if (s1->height < s2->height)
        return -1;
    if (s1->height > s2->height)
        return 1;
    if (s1->width < s2->width)
        return -1;
    if (s1->width > s2->width)
        return 1;
    return 0;
}

static int _area_comparator(void *ctx, const void *e1, const void *e2) {
    const src_conf_size_t *s1 = e1;
    const src_conf_size_t *s2 = e2;
    if (s1->height * s1->width < s2->height * s2->width)
        return -1;
    if (s1->height * s1->width > s2->height * s2->width)
        return 1;
    return 0;
}

@implementation CameraConfigurator
{
    BOOL _highQualityMode;
    id<BCProfileLibraryObjcProtocol>_Nonnull _bcProfileLib;
    CLsLogger *_logger;
}

- (instancetype)initWithHighQualityMode:(BOOL)highQualityMode
                          outputProfile:(OutputProfile *)outputProfile
                           bcProfileLib:(id<BCProfileLibraryObjcProtocol>_Nonnull)bcProfileLib
{
    self = [super init];
    if (self) {
        _highQualityMode = highQualityMode;
        _outputProfile = outputProfile;
        _bcProfileLib = bcProfileLib;

        _logger = [[CLsLogger alloc] initWithSubsystem: @"swi.core" category: @"CameraConfigurator"];
    }
    return self;
}

- (CameraConfFraming)framingForEmulatingProdOrientationWithSystemRotation:(int)systemRotation
{
    /*
     * built-in cameras:
     * The system rotation always corresponds to the UI rotation.
     * If this rotation does not match the output aspect ratio, we need
     * to reframe the source with the sptz.
     * If the output is squere, we also need the sptz.
     */
    struct gmu_ratio_i32 far = _outputProfile.targetAspectRatio;
    if (far.numer == far.denom)
        return CameraConfFramingBasicSPTZ;
    BOOL isSourceLandscape = (systemRotation % 180) == 0;
    BOOL isOutputLandscape = far.numer > far.denom;
    if (isSourceLandscape != isOutputLandscape)
        return CameraConfFramingBasicSPTZ;
    return CameraConfFramingClassic;
}

/**
 * Return the max video size that the given camera cap allow for recording with
 * given extra latency.
 * If extraLatency is NaN, it is ignored.
 */
- (src_conf_size_t)_getMaxVideoSizeForRecording:(const struct src_conf_cam_props *)variant extraLatency:(float)extraLatency
{
    src_conf_size_t ret = { 0 };

    const struct src_conf_camera_mode *modes = variant->supported_modes;
    int count = variant->supported_modes_count;

    for (int i=0; i<count; i++) {
        struct src_conf_camera_mode mode = modes[i];
        if (!mode.rec)
            continue;
        if (!isnan(extraLatency) && mode.extra_latency_when_rec > extraLatency)
            continue;
        if (mode.size.width >= ret.width && mode.size.height >= ret.height)
            ret = mode.size;
    }

    return ret;
}

- (CameraConf *)_confForCamera:(Camera *)camera
                    cameraType:(src_conf_type_t)type
                cameraPosition:(src_conf_pos_t)pos
                systemRotation:(int)rot
                       framing:(CameraConfFraming)framing
                   resyncDelay:(float)resyncDelay
{
    int sample_rate;
    src_conf_size_t stream_size = { 0 };

    if (_highQualityMode) {
        sample_rate = 48000;
        stream_size.width = 960;
        stream_size.height = 540;
    } else {
        sample_rate = 44100;
        stream_size.width = 640;
        stream_size.height = 360;
    }

    // get camera variant

    SRC_CONF_SWITCH_CAP *cap = camera.confSwitchCap;
    const struct src_conf_cam_props *cam_props = src_conf_switch_cap_get_variant(cap, type, pos);

    // get values from the bp profile (if existing)

#ifdef CONFIG_SWITCHER
    BCProfileId *profileId = [_bcProfileLib currentProfileId];
    if (profileId) {
        SwitcherStreamSettings *profile = [_bcProfileLib profileWithId:profileId];
        if (profile) {
            NSNumber *n;
            int sr, w, h;
            n = profile.audioSampleRateNumber;
            sr = n.intValue;
            if (sr)
                sample_rate = sr;
            n = profile.videoFrameWidthNumber;
            w = n.intValue;
            n = profile.videoFrameHeightNumber;
            h = n.intValue;
            if (w && h) {
                stream_size.width = w;
                stream_size.height = h;
                stream_size = [_outputProfile sizeConformingToTargetAspectRatio:stream_size];
            }
        }
    } else {
        stream_size = [_bcProfileLib defaultProgramFrameSize];
        stream_size = [_outputProfile sizeConformingToTargetAspectRatio:stream_size];
    }
#endif

    /* limit the stream size to what the camera can provide */

    src_conf_size_t max_size = cap->max_hq_30fps_stream_size;
    if (max_size.width == 0 || max_size.height == 0) {
        max_size.width = cam_props->max_size_for_hd1080.width / 4;
        max_size.height = cam_props->max_size_for_hd1080.height / 4;
        if (max_size.width == 0 || max_size.height == 0) {
            max_size.width = 1920 / 4;
            max_size.height = 1080 / 4;
        }
    }

    /*
     * What we want here is to ensure that the camera does not send more data than what corresponds to
     * 'max_hq_30fps_stream_size' (wifth x height). Limitting the requested 'stream_size' is not the
     * right way because the camera will fit the source size into the stream size which might
     * correspond to less data than we expect. See bug fix story 27947 for more info.
     */
    max_size = [_outputProfile sizeConformingToTargetAspectRatio:max_size];

    if (stream_size.width > max_size.width || stream_size.height > max_size.height)
        stream_size = max_size;

    /* limit the stream size to what the platform is capable of */

    pcap_get_max_mixer_input_stream_video_size(&max_size.width, &max_size.height);
    max_size = [_outputProfile sizeConformingToTargetAspectRatio:max_size];

    if (stream_size.width > max_size.width || stream_size.height > max_size.height)
        stream_size = max_size;

    /* get the wished source frame size */

    src_conf_size_t src_size = src_conf_size_undefined();
    float src_frame_rate = 0; // variable frame rate

    if (RecProfile.sharedInstance.isocamMode == RecProfileIsocamModeFull) {
        // director mode
        src_conf_size_t size = RecProfile.sharedInstance.cameraFrameSize;
        if (src_conf_size_is_defined(size))
            src_size = [_outputProfile sizeConformingToTargetAspectRatio:size];
        else
            src_size = src_conf_size_auto();
        src_frame_rate = RecProfile.sharedInstance.cameraFrameRate;
    }

    // log wishes

    [_logger info: @"CameraConfiguratorHQ:"];
    NSString *cameraLog = [NSString stringWithFormat: @"  camera: %s (%s @ %s)",
                        NSStringFromClass(camera.class).UTF8String,
                        camera.sourceName.UTF8String,
                        camera.hostName.UTF8String];
    [_logger info: cameraLog];
    
    NSString *wishedStreamSizeLog = [NSString stringWithFormat: @"  wished stream size: %d x %d",
                           stream_size.width,
                           stream_size.height];
    [_logger info: wishedStreamSizeLog];
    
    if (!src_conf_size_is_defined(src_size))
        [_logger info: @"  wished source size: undefined"];
    else if (src_conf_size_is_auto(src_size))
        [_logger info: @"  wished source size: auto"];
    else {
        NSString *wishedSourceSizeLog = [NSString stringWithFormat: @"  wished source size: %d x %d",
                                         src_size.width,
                                         src_size.height];
        [_logger info: wishedSourceSizeLog];
    }
    [_logger info: @"  wished source frame rate: %f" doubleValue: src_frame_rate];
    [_logger info: @"  framing mode: %d" intValue: (int)framing];
    [_logger info: @"  cap:"];
    src_conf_switch_cap_dump(cap, "    ");

    struct video_conf vconf;

    switch (framing) {
        case CameraConfFramingClassic:
            vconf = [self _classicVideoConfForCamera:camera
                                          cameraType:type
                                      cameraPosition:pos
                                          sourceSize:src_size
                                      systemRotation:rot
                                           frameRate:src_frame_rate
                                          streamSize:stream_size
                                         resyncDelay:resyncDelay];
            break;
        case CameraConfFramingBasicSPTZ:
            vconf = [self _sptzBasedVideoConfForCamera:camera
                                            cameraType:type
                                        cameraPosition:pos
                                            sourceSize:src_size
                                        systemRotation:rot
                                             frameRate:src_frame_rate
                                            streamSize:stream_size];
            break;
        case CameraConfFramingVPTZ:
            vconf = [self _vptzBasedVideoConfForCamera:camera
                                            cameraType:type
                                        cameraPosition:pos
                                            sourceSize:src_size
                                        systemRotation:rot
                                             frameRate:src_frame_rate
                                            streamSize:stream_size];
            break;
    }

    // log results

    
    NSString *selectdSourceLog = [NSString stringWithFormat: @"  selected source: size=%dx%d fr=%g pos=%d srot=%d",
                                     vconf.frame_size.width,
                                     vconf.frame_size.height,
                                     vconf.frame_rate,
                                     pos,
                                     rot];
    [_logger info: selectdSourceLog];
    if (vconf.sptz.enabled) {
        NSString *sptzLog = [NSString stringWithFormat: @"    sptz: out_size=%dx%d vptz=%d",
                                      vconf.sptz.output_size.width,
                                      vconf.sptz.output_size.height,
                                      (int)vconf.sptz.virtual_ptz];
        [_logger info: sptzLog];
    }
    NSString *selectedStreamLog = [NSString stringWithFormat: @"  selected stream: size=%dx%d",
                         vconf.stream_size.width,
                         vconf.stream_size.height];
    [_logger info: selectedStreamLog];
    // consolidate all info

    SRC_CONF conf = src_conf_default;
    conf.type = vconf.type;
    conf.position = vconf.position;
    conf.system_rotation = vconf.system_rotation;
    conf.v_frame_rate = vconf.frame_rate;
    conf.v_frame_size = vconf.frame_size;
    conf.sptz = vconf.sptz;
    conf.priority = vconf.priority;
    conf.a_sample_rate = sample_rate;

    CameraConf *ret = [CameraConf new];
    ret.sourceConf = conf;
    if ((conf.system_rotation % 180) == 90) {
        struct src_conf_size tmp = {
            .width = vconf.stream_size.height,
            .height = vconf.stream_size.width,
        };
        ret.streamSize = tmp;
    } else {
        ret.streamSize = vconf.stream_size;
    }

    return ret;
}

/**
 * @param src_size    Source size required by the user, in image natural
 *                    orientation, can be auto or undefined.
 * @param stream_size Stream size required by the user, in image natural
 *                    orientation, always defined.
 */
- (struct video_conf)_classicVideoConfForCamera:(Camera *)camera
                                     cameraType:(src_conf_type_t)type
                                 cameraPosition:(src_conf_pos_t)position
                                     sourceSize:(src_conf_size_t)source_natural_size
                                 systemRotation:(int)system_rotation
                                      frameRate:(float)frame_rate
                                     streamSize:(src_conf_size_t)stream_natural_size
                                    resyncDelay:(float)resyncDelay
{
    assert(stream_natural_size.width > 0);
    assert(stream_natural_size.height > 0);

    float extraLatency = NAN;
    if (resyncDelay > 0)
        extraLatency = resyncDelay - camera.minimumResyncDelay;

    SRC_CONF_SWITCH_CAP *cap = camera.confSwitchCap;
    const struct src_conf_cam_props *cam_props = src_conf_switch_cap_get_variant(cap, type, position);

    // convert to camera reference orientation

    src_conf_size_t src_size = source_natural_size;
    src_conf_size_t stream_size = stream_natural_size;
    if ((system_rotation % 180) == 90) {
        src_size.width     = source_natural_size.height;
        src_size.height    = source_natural_size.width;
        stream_size.width  = stream_natural_size.height;
        stream_size.height = stream_natural_size.width;
    }

    /*
     * When old devices as the iPhone5 are used as camera, if we are in director
     * mode with the source size set to 720p and the stream size set to 540p or
     * more (typically 720p), source size will be kept at 720p and the camera
     * will limit the stream size to 540p, allowing integer downscale factors
     * only. This results in a stream size of 360p.
     * This use case is quite frequent and the user is always surprised about
     * the poor quality on the stream. So, in this particular case, we make an
     * exception: we allow the source size to be higher than requested.
     */

    if (!cap->vstream_downscale_r_available && stream_size.height == 540 && src_size.height == 720) {
        [_logger info: @"  apply exception for old devices, allowing source sizes up to 1080p"];
        src_size.width = stream_size.width * 2;
        src_size.height = stream_size.height * 2;
    }

    // prepare video conf

    struct video_conf conf = {0};
    conf.type = cam_props->type;
    conf.position = cam_props->position;
    conf.system_rotation = system_rotation;
    conf.frame_rate = frame_rate;

    // new rules

    if (!src_conf_size_is_defined(src_size) && !cap->downscale_r_available && cap->vstream_downscale_r_available) {

        // Not in director mode (no constraint on source) -> optimize stream
        [_logger info: @"apply new rule A"];

        /*
         * We first check if 720p and 1080p mode are an integer multiple of the
         * stream size.
         * If not, we chose the first available size above the stream size.
         */

        if (cam_props->hd720_available && _has_i1_4_ratio(_hd720, stream_size)) {
            conf.frame_size.width = 1280;
            conf.frame_size.height = 720;
        } else if (cam_props->hd1080_available && _has_i1_4_ratio(_hd1080, stream_size)) {
            conf.frame_size.width = 1920;
            conf.frame_size.height = 1080;
        } else {
            // get size list
            const struct src_conf_camera_mode *modes = cam_props->supported_modes;
            int count = cam_props->supported_modes_count;
            GMU_LST(src_conf_size_t) sorted_sizes;
            GMU_LST_INIT(sorted_sizes);
            GMU_LST_RESIZE(sorted_sizes, count);
            for (int i=0; i<count; i++)
                GMU_LST_ADD(sorted_sizes, modes[i].size);

            // sort list from smaller to bigger
            GMU_LST_QSORT(sorted_sizes, NULL, _area_comparator);

            for (int i=0; i<count; i++) {
                NSString *sizeLog = [NSString stringWithFormat: @"size=%dx%d",
                                     sorted_sizes.items[i].width,
                                     sorted_sizes.items[i].height];
                [_logger info: sizeLog];
            }

            src_conf_size_t found = { 0 };

            for (int i=0; i<count; i++) {
                src_conf_size_t size = sorted_sizes.items[i];

                // TODO: ignore sizes bigger than what this device can support

                // ignore sizes not matching the aspect ratio
                if (size.width * stream_size.height != size.height * stream_size.width)
                    continue;

                if (size.width >= stream_size.width && size.height >= stream_size.height) {
                    found.width = size.width;
                    found.height = size.height;
                    break;
                }
            }

            if (!found.width || !found.height) {
                // We didn't find any size matching the frame aspect ratio.
                // We now try with any aspect ratio.

                for (int i=0; i<count; i++) {
                    src_conf_size_t size = sorted_sizes.items[i];

                    // TODO: ignore sizes bigger than what this device can support

                    found.width = size.width;
                    found.height = size.height;
                    if (size.width >= stream_size.width && size.height >= stream_size.height)
                        break;
                }
            }

            if (!found.width || !found.height) {
                found.width = 1280;
                found.height = 720;
            }

            GMU_LST_DEINIT(sorted_sizes);

            conf.frame_size.width = found.width;
            conf.frame_size.height = found.height;
        }

        conf.stream_size = stream_size;
        return conf;
    }

    if (src_conf_size_is_defined(src_size) && !src_conf_size_is_auto(src_size) &&
        cap->downscale_r_available && cap->vstream_downscale_r_available)
    {
        // Director mode with fixed source size and both 'r' for downscaling are present
        conf.frame_size = src_size;
        src_conf_size_t max_rec_size = [self _getMaxVideoSizeForRecording:cam_props extraLatency:extraLatency];
        if (conf.frame_size.width > max_rec_size.width)
            conf.frame_size.width = max_rec_size.width;
        if (conf.frame_size.height > max_rec_size.height)
            conf.frame_size.height = max_rec_size.height;
        conf.priority.size_minus = 1;
        conf.stream_size = stream_size;
        return conf;
    }

    if (src_conf_size_is_auto(src_size) && cap->downscale_r_available && cap->vstream_downscale_r_available) {
        // Director mode with automatic source size and both 'r' are present -> choose highest possible source size
        conf.frame_size = [self _getMaxVideoSizeForRecording:cam_props extraLatency:extraLatency];
        if (conf.frame_size.width > 1920)
            conf.frame_size.width = 1920;
        if (conf.frame_size.height > 1080)
            conf.frame_size.height = 1080;
        conf.priority.size_minus = 1;
        conf.stream_size = stream_size;
        return conf;
    }

    // old rules - find adjustment method

    int adjustment;

    if (src_conf_size_is_auto(src_size)) {
        // Director mode with automatic source size and one of both 'r' are present
        adjustment = ADJUSTMENT_BEST_HD_SOURCE_SIZE; // *1, *1b, *2
    } else if (src_conf_size_is_defined(src_size)) {
        // Director mode with fixed source size and only one of both 'r' is present
        if (cap->vstream_downscale_r_available)
            adjustment = ADJUSTMENT_FULLY_FLEXIBLE; // *4
        else
            adjustment = ADJUSTMENT_BEST_DOWNSCALE_RATIO; // *3
    } else {
        // Not in director mode source size doesn't matter (no recording)
        if (cap->downscale_r_available) {
            // vstream_downscale_r_available present or not present
            adjustment = ADJUSTMENT_NO_DOWNSCALE; // *0
        } else {
            // vstream_downscale_r_available not present
            adjustment = ADJUSTMENT_BEST_HD_SOURCE_SIZE_AND_DOWNSCALE_RATIO; // *2
        }
    }

    // old rules - apply adjustment on both source frame size and stream frame size

    switch (adjustment) {

        case ADJUSTMENT_NO_DOWNSCALE:
            [_logger info: @"apply old rule ADJUSTMENT_NO_DOWNSCALE"];
            
            conf.frame_size.width = stream_size.width;
            conf.frame_size.height = stream_size.height;
            break;

        case ADJUSTMENT_BEST_HD_SOURCE_SIZE: {
            [_logger info: @"apply old rule ADJUSTMENT_BEST_HD_SOURCE_SIZE"];

            // We are in director mode, auto frame size

            if (cap->downscale_r_available) {
                // We are free to chose any source size. This is typically the case with Capture.
                // So we choose the highest source size that is a multiple of stream size.
                // We do not move above Full HD.
                bool found = 0;
                for (int i=4; i>= 1; i--) {
                    int width = stream_size.width * i;
                    int height = stream_size.height * i;
                    if (width  <= 1920 && width  < cam_props->max_size.width &&
                        height <= 1080 && height < cam_props->max_size.height
                    ) {
                        conf.frame_size.width  = width;
                        conf.frame_size.height = height;
                        found = true;
                        break;
                    }
                }
                if (found)
                    break;
            }

            // We check if the highest source frame size fits. We do not go above Full HD.
            // If not, we fall back to the classic selection method.

            src_conf_size_t max_hd = cam_props->max_size_for_hd1080;
            if (!max_hd.width || !max_hd.height) {
                max_hd.width = 1920;
                max_hd.height = 1080;
            }

            if (max_hd.width && max_hd.height && _has_i1_4_ratio(max_hd, stream_size)) {
                conf.frame_size.width = max_hd.width;
                conf.frame_size.height = max_hd.height;
                break;
            }

            if (cam_props->hd1080_available && _has_i1_4_ratio(_hd1080, stream_size)) {
                conf.frame_size.width = 1920;
                conf.frame_size.height = 1080;
                break;
            }

            // no break here
        }
        case ADJUSTMENT_BEST_HD_SOURCE_SIZE_AND_DOWNSCALE_RATIO: {
            [_logger info: @"apply old rule ADJUSTMENT_BEST_HD_SOURCE_SIZE_AND_DOWNSCALE_RATIO"];

            // We choose the source frame size and the downscale ratio in order to be
            // as close as possible to the desired stream size, but staying below it.
            // If possible, we prefer the lowest source frame size.

            if (cam_props->hd720_available && _has_i1_4_ratio(_hd720, stream_size)) {
                conf.frame_size.width = 1280;
                conf.frame_size.height = 720;
                break;
            }

            if (cam_props->hd1080_available && _has_i1_4_ratio(_hd1080, stream_size)) {
                conf.frame_size.width = 1920;
                conf.frame_size.height = 1080;
                break;
            }

            // Search the size that, once downscaled, is closer (by staying below)
            // to the stream size.
            // First try a solution matching the stream aspect ratio.
            // NEW SOLUTION SINCE DECEMBER 2017

            // fallback value
            conf.frame_size = cam_props->max_size_for_hd1080;
            if (!conf.frame_size.width || !conf.frame_size.height) {
                conf.frame_size.width = 1920;
                conf.frame_size.height = 1080;
            }

            // get size list
            const struct src_conf_camera_mode *modes = cam_props->supported_modes;
            int count = cam_props->supported_modes_count;

            // sort the list from smallest to biggest size
            GMU_LST(src_conf_size_t) sorted_sizes;
            GMU_LST_INIT(sorted_sizes);
            GMU_LST_RESIZE(sorted_sizes, count);
            for (int i=0; i<count; i++)
                GMU_LST_ADD(sorted_sizes, modes[i].size);
            GMU_LST_QSORT(sorted_sizes, NULL, _size_comparator);

            for (int i=0; i<count; i++) {
                NSString *msg = [NSString stringWithFormat: @"size=%dx%d",
                 sorted_sizes.items[i].width,
                 sorted_sizes.items[i].height];
                [_logger info: msg];
            }

            src_conf_size_t highest = {0};

            for (int i=0; i<count; i++) {
                src_conf_size_t size = sorted_sizes.items[i];

                // TODO: ignore sizes bigger than what this device can support

                // ignore sizes not matching the aspect ratio
                if (size.width * stream_size.height != size.height * stream_size.width)
                    continue;

                src_conf_size_t res = _over_rounded_int_ratio_downscale(size, stream_size);
                if (res.width && res.height && res.width > highest.width && res.height > highest.height) {
                    conf.frame_size.width = size.width;
                    conf.frame_size.height = size.height;
                    highest.width = res.width;
                    highest.height = res.height;
                }
            }

            if (!highest.width || !highest.height) {
                // We didn't find any size matching the frame aspect ratio.
                // We now try with any aspect ratio.

                /*
                 * In this case 'ADJUSTMENT_BEST_HD_SOURCE_SIZE_AND_DOWNSCALE_RATIO' the downscale ratio (1 - 4)
                 * is used for the algorithm since 'downscale_r_available' and 'vstream_downscale_r_available'
                 * are both false.
                 * There are still cases where 'vstream_downscale_r_available' is true. This can happen when we
                 * fall through directly from case 'ADJUSTMENT_BEST_HD_SOURCE_SIZE'. This case was not accounted
                 * for. So the following test for vstream_downscale_r_available == true was added. This is clearly
                 * a fix. To make this nicely more profound chganges affecting the different strategies would be
                 * required.
                 */
                if (cap->vstream_downscale_r_available) {
                    break;
                }

                for (int i=0; i<count; i++) {
                    src_conf_size_t size = sorted_sizes.items[i];

                    // TODO: ignore sizes bigger than what this device can support

                    src_conf_size_t res = _over_rounded_int_ratio_downscale(size, stream_size);
                    if (res.width && res.height && res.width > highest.width && res.height > highest.height) {
                        conf.frame_size.width = size.width;
                        conf.frame_size.height = size.height;
                        highest.width = res.width;
                        highest.height = res.height;
                    }
                }
            }

            GMU_LST_DEINIT(sorted_sizes);

            /*

            OLD SOLUTION USED FOR YEARS

            conf.frame_size = [self _getFrameSizeForHD1080:cap cameraPosition:conf.position];
            if (!conf.frame_size.width || !conf.frame_size.height) {
                conf.frame_size.width = 1920;
                conf.frame_size.height = 1080;
            }

            src_conf_size_t res = _over_rounded_int_ratio_downscale(conf.frame_size, stream_size);
            if (res.width && res.height && res.width > highest.width && res.height > highest.height) {
                highest.width = res.width;
                highest.height = res.height;
            }

            if ([self _doesCameraSupport720p:cap cameraPosition:conf.position]) {
                src_conf_size_t res = _over_rounded_int_ratio_downscale(_hd720, stream_size);
                if (res.width && res.height && res.width > highest.width && res.height > highest.height) {
                    conf.frame_size.width = 1280;
                    conf.frame_size.height = 720;
                    highest.width = res.width;
                    highest.height = res.height;
                }
            }

            if ([self _doesCameraSupport1080p:cap cameraPosition:conf.position]) {
                src_conf_size_t res = _over_rounded_int_ratio_downscale(_hd1080, stream_size);
                if (res.width && res.height && res.width > highest.width && res.height > highest.height) {
                    conf.frame_size.width = 1920;
                    conf.frame_size.height = 1080;
                    highest.width = res.width;
                    highest.height = res.height;
                }
            }
            */

            if (highest.width && highest.height) {
                stream_size.width = highest.width;
                stream_size.height = highest.height;
            } else {
                stream_size.width = (int)ceil((double)conf.frame_size.width / 4.0);
                stream_size.height = (int)ceil((double)conf.frame_size.height / 4.0);
            }

            break;
        }
        case ADJUSTMENT_BEST_DOWNSCALE_RATIO: {
            [_logger info: @"apply old rule ADJUSTMENT_BEST_DOWNSCALE_RATIO"];

            // We are in director mode, so we keep the wanted source size and
            // we search the best ratio to be equal or below the needed stream size.

            conf.frame_size = src_size;
            if (!conf.frame_size.width || !conf.frame_size.height) {
                conf.frame_size.width = 1920;
                conf.frame_size.height = 1080;
            }

            if (_has_i1_4_ratio(conf.frame_size, stream_size))
                break;

            src_conf_size_t res = _over_rounded_int_ratio_downscale(conf.frame_size, stream_size);
            if (res.width && res.height) {
                stream_size.width = res.width;
                stream_size.height = res.height;
            } else {
                stream_size.width = (int)ceil((double)conf.frame_size.width / 4.0);
                stream_size.height = (int)ceil((double)conf.frame_size.height / 4.0);
            }

            break;
        }
        case ADJUSTMENT_FULLY_FLEXIBLE:
            [_logger info: @"apply old rule ADJUSTMENT_FULLY_FLEXIBLE"];

            conf.frame_size = src_size;
            if (!conf.frame_size.width || !conf.frame_size.height) {
                conf.frame_size.width = 1920;
                conf.frame_size.height = 1080;
            }

            src_conf_size_t max_rec_size = [self _getMaxVideoSizeForRecording:cam_props extraLatency:extraLatency];
            if (conf.frame_size.width > max_rec_size.width)
                conf.frame_size.width = max_rec_size.width;
            if (conf.frame_size.height > max_rec_size.height)
                conf.frame_size.height = max_rec_size.height;

            break;
    }

//    ls_log_info(_logger(), "  selected classic adjustment method: %d", adjustment);

    conf.stream_size = stream_size;

    return conf;
}

/**
 * In this scenario, sptz is available and we can choose any source size.
 * We choose the smaller source size that is equal or bigger than both the
 * requested source size and the requested stream sizes.
 * We then let the source pick the source it wants, which implicitely will be
 * equal or higher.
 * The stream size is returned unaltered and the camera will handle this as the
 * maximum stream size.
 * @param source_size Source size required by the user, in image natural
 *                    orientation, can be auto or undefined.
 * @param stream_size Stream size required by the user, in image natural
 *                    orientation, always defined.
 */
- (struct video_conf)_sptzBasedVideoConfForCamera:(Camera *)camera
                                       cameraType:(src_conf_type_t)type
                                   cameraPosition:(src_conf_pos_t)position
                                       sourceSize:(src_conf_size_t)source_size
                                   systemRotation:(int)system_rotation
                                        frameRate:(float)frame_rate
                                       streamSize:(src_conf_size_t)stream_size
{
    SRC_CONF_SWITCH_CAP *cap = camera.confSwitchCap;
    const struct src_conf_cam_props *cam_props = src_conf_switch_cap_get_variant(cap, type, position);

    // convert source size to camera reference orientation

    src_conf_size_t min_ref_size1 = source_size;
    if ((system_rotation % 180) == 90) {
        int tmp = min_ref_size1.width;
        min_ref_size1.width = min_ref_size1.height;
        min_ref_size1.height = tmp;
    }

    // convert stream size to camera reference orientation

    src_conf_size_t min_ref_size2 = stream_size;
    if ((system_rotation % 180) == 90) {
        int tmp = min_ref_size2.width;
        min_ref_size2.width = min_ref_size2.height;
        min_ref_size2.height = tmp;
    }

    // do not work below Full HD (warning: ignore system rotation here)

    src_conf_size_t min_ref_size3 = cam_props->max_size_for_hd1080;
    if (!min_ref_size3.width || !min_ref_size3.height) {
        min_ref_size3.width = 1920;
        min_ref_size3.height = 1080;
    };

    // ensure selected source size is bigger than requested source and stream sizes

    src_conf_size_t sel_src_size = min_ref_size1;
    if (sel_src_size.width < min_ref_size2.width)
        sel_src_size.width = min_ref_size2.width;
    if (sel_src_size.height < min_ref_size2.height)
        sel_src_size.height = min_ref_size2.height;
    if (sel_src_size.width < min_ref_size3.width)
        sel_src_size.width = min_ref_size3.width;
    if (sel_src_size.height < min_ref_size3.height)
        sel_src_size.height = min_ref_size3.height;

    // ensure that sptz output size is bigger than requested source and stream sizes

    src_conf_size_t sptz_out_size = stream_size;
    if (sptz_out_size.width < source_size.width)
        sptz_out_size.width = source_size.width;
    if (sptz_out_size.height < source_size.height)
        sptz_out_size.height = source_size.height;

    // consolidate info

    src_conf_size_t cam_ref_stream_size;
    if ((system_rotation % 180) == 90) {
        cam_ref_stream_size.width = stream_size.height;
        cam_ref_stream_size.height = stream_size.width;
    } else {
        cam_ref_stream_size = stream_size;
    }

    struct video_conf conf = {
        .type = cam_props->type,
        .position = cam_props->position,
        .system_rotation = system_rotation,
        .frame_size = sel_src_size,
        .frame_rate = frame_rate,
        .stream_size = cam_ref_stream_size,
        .sptz = {
            .enabled = true,
            .output_size = sptz_out_size,
        },
        .priority = {
            .size_plus = 1,
        },
    };

    return conf;
}

/**
 * vptz mode is a sptz reframing with a motion controller allowing pan, tilt and zoom to be moved by the user.
 * In this mode we just configure the camera in its maximum frame size available.
 * We use the user defined source size as sptz/vptz output size.
 */
- (struct video_conf)_vptzBasedVideoConfForCamera:(Camera *)camera
                                       cameraType:(src_conf_type_t)type
                                   cameraPosition:(src_conf_pos_t)position
                                       sourceSize:(src_conf_size_t)source_size
                                   systemRotation:(int)system_rotation
                                        frameRate:(float)frame_rate
                                       streamSize:(src_conf_size_t)stream_size
{
    SRC_CONF_SWITCH_CAP *cap = camera.confSwitchCap;
    const struct src_conf_cam_props *cam_props = src_conf_switch_cap_get_variant(cap, type, position);

    src_conf_size_t cam_ref_stream_size;
    if ((system_rotation % 180) == 90) {
        cam_ref_stream_size.width = stream_size.height;
        cam_ref_stream_size.height = stream_size.width;
    } else {
        cam_ref_stream_size = stream_size;
    }

    src_conf_size_t output_size;
    if (!src_conf_size_is_defined(source_size)) {
        output_size = stream_size;
    } else if (src_conf_size_is_auto(source_size)) {
        if ((system_rotation % 180) == 90) {
            output_size.width = 1080;
            output_size.height = 1920;
        } else {
            output_size.width = 1920;
            output_size.height = 1080;
        }
    } else {
        output_size = source_size;
    }

    struct video_conf conf = {
        .type = cam_props->type,
        .position = cam_props->position,
        .system_rotation = system_rotation,
        .frame_size = cam_props->max_size,
        .frame_rate = frame_rate,
        .stream_size = cam_ref_stream_size,
        .sptz = {
            .enabled = true,
            .output_size = output_size,
            .virtual_ptz = true,
        },
    };

    return conf;
}

- (CameraConf *)_confForAudio:(Camera *)camera
{
    SRC_CONF conf = src_conf_default;
    conf.type = SRC_CONF_TYPE_UNDEFINED;
    conf.position = SRC_CONF_POS_UNDEFINED;
    conf.v_enabled = false;
    conf.a_enabled = true;
    conf.a_sample_rate = _highQualityMode ? 48000 : 44100;

    // get values from the bp profile (is existing)

#ifdef CONFIG_SWITCHER
    BCProfileId *profileId = [_bcProfileLib currentProfileId];
    if (profileId) {
        SwitcherStreamSettings *profile = [_bcProfileLib profileWithId:profileId];
        if (profile) {
            NSNumber *n = profile.audioSampleRateNumber;
            int sr = n.intValue;
            if (sr)
                conf.a_sample_rate = sr;
        }
    }
#endif

    CameraConf *ret = [CameraConf new];
    ret.sourceConf = conf;
    return ret;
}

- (BOOL)configureCamera:(Camera *)camera
             cameraType:(src_conf_type_t)type
         cameraPosition:(src_conf_pos_t)pos
         systemRotation:(int)rot
                framing:(CameraConfFraming)framing
            resyncDelay:(float)resyncDelay
            forceSwitch:(BOOL)forceSwitch
{
    CameraConf *newConf = nil;

    if ([camera isKindOfClass:SeemoCamera.class]) {
        SRC_CONF_SWITCH_CAP *cap = camera.confSwitchCap;
        assert(cap->variants_count == 1);
        src_conf_size_t size = cap->variants[0].max_size;
        NSString *seemoMsg = [NSString stringWithFormat: @"apply conf to SeeMo camera: size=%dx%d", size.width, size.height];
        [_logger info:seemoMsg];
        SRC_CONF conf = { 0 };
        conf.v_enabled = true;
        conf.v_frame_size = size;
        conf.system_rotation  = rot;
        newConf = [CameraConf new];
        newConf.sourceConf = conf;
        newConf.streamSize = size;
    } else if (camera.hasVideo) {
        newConf = [self _confForCamera:camera
                            cameraType:type
                        cameraPosition:pos
                        systemRotation:rot
                               framing:framing
                           resyncDelay:resyncDelay];
    } else if (camera.hasAudio) {
        newConf = [self _confForAudio:camera];
    }

    if (!src_conf_size_equal(camera.streamFrameSize, newConf.streamSize)) {
        camera.streamFrameSize = newConf.streamSize;
        /*
         * Conf switch is not always needed here but we force it in order to
         * force the stream size to be updated as well.
         */
        forceSwitch = YES;
    }

    bool streamLowLatencyMode = resyncDelay <= 0.3f;
    if (camera.streamLowLatencyMode != streamLowLatencyMode) {
        camera.streamLowLatencyMode = streamLowLatencyMode;
        forceSwitch = YES;
    }

    // disabled because currently done by channel manager
    // camera.resyncDelay = resyncDelay;

    camera.streamQualityMode = _highQualityMode ? STREAM_QMODE_HQ : STREAM_QMODE_NORMAL;

    SRC_CONF *current_conf = camera.conf;
    SRC_CONF new_conf = newConf.sourceConf;
    if (!current_conf || !src_conf_equal(current_conf, &new_conf))
        forceSwitch = YES;

    if (forceSwitch)
        [camera confSwitch:&new_conf];

    return forceSwitch;
}

- (AudioConf)getAudioConf
{
    AudioConf ret = { 0 };
    ret.sampleRate = _highQualityMode ? 48000 : 44100;
    ret.stereo = true;

#ifdef CONFIG_SWITCHER
    BCProfileId *profileId = [_bcProfileLib currentProfileId];
    if (profileId) {
        SwitcherStreamSettings *profile = [_bcProfileLib profileWithId:profileId];
        if (profile) {
            NSNumber *n;

            n = profile.audioSampleRateNumber;
            int sr = n.intValue;
            if (sr)
                ret.sampleRate = sr;

            n = profile.audioChannelCountNumber;
            int channelCount = n.intValue;

            if (channelCount)
                ret.stereo = channelCount >= 2;
        }
    }
#endif
    return ret;
}

- (BOOL)configureAudioMixer:(AudioMixer *)mixer
{
    BOOL changed = NO;

    AudioConf conf = [self getAudioConf];

    if (mixer.stereo != conf.stereo) {
        mixer.stereo = conf.stereo;
        changed = YES;
    }

    return changed;
}

- (BOOL)configureAudioEngine:(RLAudioEngine *)engine
{
    BOOL changed = NO;

    AudioConf conf = [self getAudioConf];

    if (engine.sampleRate != conf.sampleRate) {
        engine.sampleRate = conf.sampleRate;
        changed = YES;
    }

    return changed;
}

@end

@implementation CameraConf

@end
