//
//  CapConf.swift
//  Cap-iOS
//
//  Created by <PERSON> on 29/03/2023.
//  Copyright © 2023 Switcher Inc. All rights reserved.
//

import Foundation

typealias CapConfProfileIdentifier = String

struct CapConfRoot: Codable {
    var activeProfile: CapConfProfileIdentifier
    var profiles: [CapConfProfile]

    static let defaultB1Root = CapConfRoot(
        activeProfile: CapConfProfile.productionProfile.id,
        profiles: [.productionProfile, .developmentProfile]
    )

    static let emptyRoot = CapConfRoot(activeProfile: "", profiles: [])
}

struct CapConfProfile: Codable, Identifiable {

    var id: CapConfProfileIdentifier
    var ssnApiPassword: String?
    var ssnApiUrl: String?
    var ssnApiUser: String?
    var ssnApiSecret: String?
    var ssnApiClientId: String?
    var showTouches: Bool?
    var srtDebug: Bool?
    var cameraStreamBitrate: Int?
    var mwv: MwvStruct?
    var rn: ReactNativeStruct?
    var simulatePuchase: Bool?
    var dashboardUrl: String?

    static let devApi = "silversunnapi-develop"
    static let devServer = "https://\(devApi).azurewebsites.net/"
    static let prodServer = "https://api.switcherstudio.com/"

    static let productionProfile = CapConfProfile(id: "Prod")
    static let developmentProfile = CapConfProfile(id: "Dev", ssnApiUrl: devServer)

    init(id: String,
         ssnApiUrl: String? = nil,
         ssnApiUser: String? = nil,
         ssnApiPassword: String? = nil,
         showTouches: Bool? = nil,
         simulatePurchase: Bool = false,
         rn: ReactNativeStruct? = nil) {
        self.id = id
        self.ssnApiUrl = ssnApiUrl
        self.ssnApiUser = ssnApiUser
        self.ssnApiPassword = ssnApiPassword
        self.simulatePuchase = simulatePurchase
        self.showTouches = showTouches
        self.rn = rn
    }

    public var copy: Self? {
        guard let data = try? JSONEncoder().encode(self) else { return nil }
        return try? JSONDecoder().decode(Self.self, from: data)
    }

    func getPlayerUrl(playerId: String) -> String {
        return "https://player\(ssnApiUrl == nil ? "" : "-develop").switcherstudio.com/watch?p=\(playerId)"
    }

    func getPlayerUrl(broadcastId: String) -> String {
        return "https://player\(ssnApiUrl == nil ? "" : "-develop").switcherstudio.com/watch?b=\(broadcastId)"
    }

    func getSpeedTestUrl() -> String {
        return "https://dashboard\(ssnApiUrl == nil ? "" : "-develop").switcherstudio.com/speedtest"
    }
}

struct ReactNativeStruct: Codable {
    var cloudyBundle: CloudyBundle?
    var labTools: [LabTool]?
}

struct LabTool: Codable {
    var appName: String
    var url: String
    var askUrl: Bool
    var enabled: Bool?
    var visible: Bool?
}

struct CloudyBundle: Codable {
    var apiVersion: Int
    var url: String
}

struct MwvStruct: Codable {
    var cacheEnabled: Bool?
}

class ConfigurationDevelopmentManager {
    static let shared = ConfigurationDevelopmentManager()

    private static let devUUID = "DB689531-F9C2-41DA-BADA-5EE76DF27216"
    private static let devPrefix = "_rldev_"

    var root: CapConfRoot = CapConfRoot.emptyRoot

    init() {
        loadJson()
    }

    lazy var url: URL = {
        guard let documentDirectory = try? FileManager.default.url(
            for: .documentDirectory,
            in: .userDomainMask,
            appropriateFor: nil,
            create: false
        ) else {
            FatalLogger.shared.logFatalError()
        }
        return documentDirectory.appendingPathComponent("\(Self.devPrefix)\(Self.devUUID)")
            .appendingPathExtension("json")
    }()

    var activeProfile: CapConfProfile? {
        return root.profiles.first(where: {$0.id == root.activeProfile})
    }

    /// Returns true if the current active profile is using the development API
    var isDevProfile: Bool {
        return activeProfile?.ssnApiUrl?.contains(CapConfProfile.devApi) == true
    }

    func loadJson() {
        let fileManager = FileManager.default
        if fileManager.fileExists(atPath: url.path) {
            if let data = try? Data(contentsOf: url) {
                let decoder = JSONDecoder()
                if let capConf = try? decoder.decode(CapConfRoot.self, from: data) {
                    self.root = capConf
                } else if let profile = profileFromOldFile() {
                    self.root = CapConfRoot(activeProfile: "Imported", profiles: [profile])
                } else {
                    print("error while parsing rldev file")
                }
            } else {
                print("error while reading rldev file")
            }
        }

        // in B1 if we don't have rldev file, we add 2 profiles by default for production and development
        if self.root.profiles.count == 0 {
            self.root = CapConfRoot.defaultB1Root
        }

    }

    private func profileFromOldFile() -> CapConfProfile? {
        guard let oldCapConf = CapConf.devConf() else {
            return nil
        }
        // if we cannot decode the JSON, maybe we are in the old format. So, we try to load the profile in the old format
        let ssnApiUrl = oldCapConf["ssnApiUrl"] as? String
        let ssnApiUser = oldCapConf["ssnApiUser"] as? String
        let ssnApiPassword = oldCapConf["ssnApiPassword"] as? String
        let ssnApiSecret = oldCapConf["ssnApiSecret"] as? String
        let ssnApiClientId = oldCapConf["ssnApiClientId"] as? String
        let touchpose = oldCapConf["touchpose"] as? Bool
        let srtDebug = oldCapConf["srtDebug"] as? Bool
        let cameraStreamBitrate = oldCapConf["cameraStreamBitrate"] as? Int
        if ssnApiUrl != nil || ssnApiUser != nil {
            var profile = CapConfProfile(
                id: "Imported",
                ssnApiUrl: ssnApiUrl,
                ssnApiUser: ssnApiUser,
                ssnApiPassword: ssnApiPassword
            )
            profile.ssnApiSecret = ssnApiSecret
            profile.ssnApiClientId = ssnApiClientId
            profile.showTouches = touchpose
            profile.srtDebug = srtDebug
            profile.cameraStreamBitrate = cameraStreamBitrate
            return profile
        }
        return nil
    }

    func saveJSON() {
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        if let encodedData = try? encoder.encode(root) {
            do {
                try encodedData.write(to: url)
            } catch {
                print("Failed to write JSON data: \(error.localizedDescription)")
            }
        }
    }

    func addProfile(profile: CapConfProfile) {
        var profiles = root.profiles
        profiles.append(profile)
        root.profiles = profiles
        saveJSON()
    }

    func updateProfile(id: String, profile: CapConfProfile) {
        var profiles = root.profiles
        profiles.removeAll(where: {$0.id == id})
        profiles.append(profile)
        root.profiles = profiles
        saveJSON()
    }

    func removeProfile(profileId: CapConfProfileIdentifier) {
        var profiles = root.profiles
        profiles.removeAll(where: {$0.id == profileId})
        root.profiles = profiles
        saveJSON()
    }

}

@objc class CapConfActiveProfileObjcWrapper: NSObject {

    let manager: ConfigurationDevelopmentManager

    override init() {
        self.manager = ConfigurationDevelopmentManager.shared
        super.init()
    }

    @objc var ssnApiUrl: String? {
        return manager.activeProfile?.ssnApiUrl
    }

    @objc var isSwitcherOfficialApi: Bool {
        guard let api = ssnApiUrl, let apiHost = URL(string: api)?.host else {
            return false
        }

        guard let devHost = URL(string: CapConfProfile.devServer)?.host else {
            return false
        }

        guard let prodHost = URL(string: CapConfProfile.prodServer)?.host else {
            return false
        }

        return apiHost == prodHost || apiHost == devHost

    }

    @objc var ssnApiSecret: String? {
        return manager.activeProfile?.ssnApiSecret
    }

    @objc var ssnApiClientId: String? {
        return manager.activeProfile?.ssnApiClientId
    }

    @objc var cameraStreamBitrate: NSNumber? {
        if let number = manager.activeProfile?.cameraStreamBitrate {
            return NSNumber(integerLiteral: number)
        }
        return nil
    }

    @objc var showTouches: Bool {
        return manager.activeProfile?.showTouches ?? false
    }

}

extension CapConfActiveProfileObjcWrapper {
    func getSecret() -> String {
        if let secret = self.ssnApiSecret {
            return secret
        }
        if let ssnApiUrl = self.ssnApiUrl, !self.isSwitcherOfficialApi {
            return "undefined secret"
        }

        var buf = [UInt8](repeating: 0, count: magic.count)
        for i in 0..<magic.count {
            // Ensure we don't go out of bounds
            if i + 64 < magic.count {
                buf[i] = magic[i] &+ 96 &- magic[i + 64]
            }
        }

        // Create a String from the null-terminated UTF8 buffer
        if let str = String(bytes: buf.prefix { $0 != 0 }, encoding: .utf8) {
            return str
        }

        return ""
    }
}

// this is a not so triky way of storing the secret
#if CONFIG_SWITCHER_BC
private let magic: [UInt8] = [
    0x39, 0x6f, 0x54, 0xb1, 0x23, 0xc6, 0x99, 0xd0, 0x6c, 0xce, 0xbb, 0xa1, 0xd7, 0xfe, 0x3c, 0x47,
    0x81, 0x5b, 0x05, 0xc1, 0x38, 0x06, 0x05, 0x3d, 0x52, 0x18, 0xf5, 0x8a, 0x96, 0xa9, 0xcd, 0x32,
    0xab, 0x8a, 0x39, 0x7d, 0xc0, 0x88, 0x78, 0x5d, 0x3a, 0xda, 0x20, 0xdc, 0x1e, 0x9a, 0x7a, 0x42,
    0x5c, 0xe3, 0xaf, 0xa6, 0x29, 0x7b, 0x6f, 0xc0, 0xd6, 0xf7, 0x1e, 0xe0, 0x50, 0xbd, 0x43, 0x3c,
    0x25, 0x67, 0x45, 0x9e, 0x1e, 0x06, 0x82, 0xc8, 0x7d, 0x0e, 0xd0, 0xa0, 0xc9, 0x3e, 0x3a, 0x42,
    0xc1, 0x47, 0xf3, 0xac, 0x25, 0xf2, 0x00, 0x39, 0x92, 0x0d, 0xf0, 0x85, 0x86, 0xe9, 0xba, 0x2d,
    0xa6, 0x7f, 0x27, 0x78, 0xac, 0xc8, 0x69, 0x57, 0x7a, 0xcb, 0x0c, 0xd4, 0x4b, 0x88, 0x67, 0xa2,
    0xfe, 0x17, 0x38, 0xb3, 0xfa, 0xbc, 0x94, 0x86, 0x0f, 0xb8, 0x81, 0x86, 0x9d, 0xc2, 0x70, 0x95
]
#elseif CONFIG_SWITCHER_GO
private let magic: [UInt8] = [
    0x4e, 0x63, 0xac, 0x22, 0x1d, 0xb4, 0xca, 0x4f, 0xe2, 0x3e, 0x9b, 0x45, 0x1a, 0x7f, 0x69, 0x97,
    0x79, 0x58, 0xa1, 0x02, 0xa1, 0x01, 0x3d, 0xb1, 0x75, 0x97, 0x12, 0x0a, 0xe6, 0xaa, 0x44, 0x6a,
    0xdb, 0x20, 0x2d, 0x7f, 0xd8, 0x86, 0x6e, 0x5b, 0x2a, 0x4b, 0xf1, 0xad, 0xb3, 0x50, 0xd9, 0x72,
    0x50, 0xbe, 0x66, 0x3a, 0x9a, 0xf4, 0x19, 0xc8, 0xac, 0x6c, 0x13, 0x1c, 0x9f, 0x27, 0xd8, 0x60,
    0x41, 0x90, 0xa5, 0x21, 0x39, 0xab, 0xb8, 0x3b, 0x0f, 0x67, 0xfb, 0xd5, 0x45, 0x8c, 0x20, 0xf9,
    0xe6, 0x1f, 0x33, 0xc8, 0x8f, 0x31, 0x64, 0xc0, 0x0d, 0x2c, 0x9c, 0x84, 0xa1, 0x80, 0x17, 0xbd,
    0x75, 0x1f, 0x85, 0x41, 0xc4, 0xc3, 0x54, 0x5f, 0x77, 0xc8, 0x16, 0x26, 0xc0, 0xf0, 0xf7, 0xa2,
    0xe0, 0xc9, 0xfc, 0x1a, 0xe2, 0x36, 0x24, 0x09, 0xab, 0x63, 0x9b, 0x7c, 0xa3, 0x49, 0xd1, 0xbf
]
#else
private let magic: [UInt8] = [
    0x6c, 0xa6, 0x2b, 0x0b, 0xe9, 0xfa, 0x0f, 0xef, 0x48, 0x69, 0xe4, 0xa9, 0x65, 0x3f, 0x85, 0x05,
    0x48, 0x1f, 0xc5, 0x6d, 0x98, 0xc7, 0xa4, 0x18, 0x79, 0x70, 0x24, 0x5d, 0xf6, 0x6b, 0xee, 0x30,
    0xa9, 0xc8, 0x0e, 0x19, 0xb7, 0x58, 0x3d, 0x06, 0xd7, 0x05, 0x95, 0x2c, 0x48, 0x5b, 0x78, 0xed,
    0xd9, 0xfd, 0x96, 0xf5, 0x27, 0x6d, 0x43, 0x78, 0x57, 0x6c, 0x60, 0xab, 0x73, 0xe6, 0xe3, 0x2e,
    0x83, 0x92, 0x6b, 0x02, 0xd6, 0x3a, 0x0e, 0x2f, 0x35, 0x64, 0xe3, 0xa6, 0x53, 0x36, 0x71, 0x45,
    0x34, 0x10, 0x05, 0x5f, 0xc8, 0x07, 0xd4, 0x0a, 0x74, 0xa4, 0x64, 0x58, 0xe0, 0x66, 0xdc, 0x17,
    0x9a, 0xba, 0x09, 0x59, 0xac, 0x4a, 0x2e, 0xef, 0xc4, 0x45, 0x81, 0x24, 0x3f, 0x48, 0xb7, 0x4d,
    0x04, 0x7b, 0xf7, 0xaf, 0xe6, 0x69, 0x28, 0x79, 0x18, 0x8d, 0xa1, 0xad, 0x4f, 0xf3, 0xb5, 0xab
]
#endif
