//
//  Artwork.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON> on 04.03.22.
//  Copyright © 2022 Switcher Inc. All rights reserved.
//

import Foundation

class Artwork: CoreElement, StoredElement {
    private var _vPropHolder: VPropHolder
    func setVPropHolder(_ vPropHolder: VPropHolder) {
        _vPropHolder = vPropHolder
    }

    @objc var vPropHolderName: String {
        _vPropHolder.name
    }

    var preferredXmlFileName: String?
    @objc var xmlFileUrl: URL?
    var inputUrls: [URL]?

    private let _editorDef: PropBundleEditorDef
    override var editorDef: PropBundleEditorDef {
        return _editorDef
    }

    var mid: String?
    var originalMid: String?
    var rank: MultilevelRank?
    var sectionProp: SimpleProp<String?>? {
        return baseProps[.Base.sectionRef] as? SimpleProp<String?>
    }
    var section: String? {
        get {
            return sectionProp?.value
        }
        set {
            sectionProp?.value = newValue
        }
    }
    var supportedAspectRatios: AspectRatioCompatibilitySet?
    var thumbnail: ArtworkThumbnail?
    var macroShapeRoot: MacroShapeRoot?
    var isMarcoShape: Bool { macroShapeRoot != nil }

    override var vPropHolder: VPropHolder? {
        return _vPropHolder
    }

    var isScene: Bool {
        return (inputUrls?.count ?? 0) > 0
    }
    
    var sourcePanelType: SourcePanelLogType {
        if self.hasTimers {
            return .timer
        }
        return .graphic(isOverlay: self.isOverlay, isMacroshape: self.isMarcoShape)
    }

    var imageURL: URL? {
        get {
            let propIndex = _vPropHolder.index(of: "image")
            if propIndex < 0 {
                return nil
            }
            if let urlString: String = _vPropHolder.prop(at: propIndex) {
                return URL(string: urlString)
            }
            return nil
        }
        set {
            let propIndex = _vPropHolder.index(of: "image")
            if propIndex < 0 {
                return
            }
            _vPropHolder.beginChange()
            _vPropHolder.setProp(at: propIndex, value: .str(newValue?.absoluteString))
            _vPropHolder.endChange()
        }
    }

    var defaultXmlFileName: String {
        if _vPropHolder.name == "shape-composition" {
            return "Text.mmart"
        } else {
            return "\(_vPropHolder.name.capitalized).mmart"
        }
    }

    /**
     * Warning: The meaning of the returned enum depends on overlay mode and timer loop mode.
     */
    override var autoDismissCapability: AutoDismissCapability {
        if timersProps.count == 1 {
            let timerConf = timersProps[0].value
            return timerConf.loop || timerConf.isStopwatch ? .basedOnDelay : .basedOnTimer
        }
        return .basedOnDelay
    }

    var isOverlay: Bool {
        if let inputUrls = inputUrls, inputUrls.count > 0 {
            return inputUrls.contains(SourcePlaceholderUrl.overlayBackground)
        } else {
            let type = _vPropHolder.vMaker?.type
            return type == .overlaidGen || type == .overlaidInput
        }
    }

    var isTitle: Bool {
        return !isOverlay && vPropHolder?.vMaker is VMakerShapeComposition
    }

    var isLogo: Bool {
        return _vPropHolder.vMaker?.name == "logo"
    }

    override var loop: Bool {
        get {
            return timers.contains(where: {$0.loop})
        }
    }

    init(vPropHolder: VPropHolder, editorDef: PropBundleEditorDef? = nil) {
        _vPropHolder = vPropHolder
        supportedAspectRatios = AspectRatioCompatibilitySet()
        supportedAspectRatios!.insert(GmuRatioI32.sixteenByNine)
        if let e = editorDef {
            self._editorDef = e
        } else {
            self._editorDef = PropBundleEditorDef(bundle: vPropHolder.propBundle)
        }
        super.init()
        baseProps[.Base.sectionRef] = SimpleProp<String?>(preferredValue: nil)
    }

    convenience init?(name: String) {
        guard let vMaker = vMaker(artworkName: name) else {
            return nil
        }
        guard !vMaker.needsATemplate else {
            return nil
        }
        self.init(vPropHolder: VPropHolder(vMaker: vMaker, targetAspectRatio: OutputProfile.shared.targetAspectRatio))
    }

    convenience init(macroShapeRoot: MacroShapeRoot,
                     vPropHolder: VPropHolder? = nil,
                     baseProps: SimplePropList) {
        self.init(vPropHolder: vPropHolder ??
                  macroShapeRoot.createPropHolder(targetAspectRatio: OutputProfile.shared.targetAspectRatio))
        self.macroShapeRoot = macroShapeRoot
        self.baseProps = baseProps
    }

    convenience init(solidColor rgba: UInt32) {
        self.init(name: "gen")!
        _vPropHolder.beginChange()
        let propIndex = _vPropHolder.index(of: "bg_color")
        if propIndex >= 0 {
            _vPropHolder.setProp(at: propIndex, value: .i32( Int32.init(bitPattern: rgba)))
        }
        _vPropHolder.endChange()
    }

    convenience init?(fromXMLString xml: String) {
        let data = StoredElementData()
        do {
            try data.read(string: xml)
        } catch {
            return nil
        }
        self.init(fromData: data, origin: nil)
    }

    @objc
    convenience init?(fromXMLFileUrl url: URL) {
        let data = StoredElementData()
        do {
            try data.read(url: url)
        } catch {
            return nil
        }
        self.init(fromData: data, origin: url)
    }

    convenience init?(fromData data: StoredElementData, origin originUrl: URL?) {
        guard data.fileType == .mmart else {
            return nil
        }
        guard let conf = data.conf as? StoredElementData.SceneConf else {
            return nil
        }
        guard let vPropHolder = data.vPropHolder else {
            return nil
        }

        self.init(vPropHolder: vPropHolder, editorDef: data.editor)
        xmlFileUrl = originUrl
        mid = data.mid
        originalMid = data.originalMid ?? data.mid
        applyOnProgram = data.applyOnProgram
        topmost = data.topmost
        rank = data.rank
        section = data.section
        supportedAspectRatios = data.supportedAspectRatios
        thumbnail = data.thumbnails.first
        inputUrls = conf.inputUrls.count > 0 ? conf.inputUrls : nil
        for timer in data.timers {
            let prop = SimpleProp(preferredValue: timerConfPreferredValue)
            prop.value = timer
            if let dataChannelId = timer.dataChannelId {
                baseProps[PropRef(name: dataChannelId)] = prop
            } else {
                baseProps[.Base.timerConfRef(dataChannel: timer.dataChannel)] = prop
            }
            timersProps.append(prop)
        }
        macroShapeRoot = data.macroShapeRoot
        devData = data.devData
        isAutoDismissEnabled = data.isAutoDismissEnabled ?? false
        autoDismissDelay = data.autoDismissDelay.isFinite ? data.autoDismissDelay : autoDismissDelayDefault
        transition = data.transition
    }

    convenience init(from original: Artwork) {
        let vPropHolder = VPropHolder(propBundle: original.vPropHolder!.propBundle)

        self.init(vPropHolder: vPropHolder, editorDef: original.editorDef)

        self.applyOnProgram = original.applyOnProgram
        self.topmost = original.topmost

        // remove all derived props
        _vPropHolder.beginChange()
        for i in 0 ..< _vPropHolder.propCount {
            let def = _vPropHolder.propDef(at: i)
            if def?.flags.contains(.derived) == true {
                _vPropHolder.setPropToDefault(at: i)
            }
        }
        _vPropHolder.endChange()

        self.inputUrls = original.inputUrls
        self.section = original.section
        self.supportedAspectRatios = original.supportedAspectRatios
    }

    /**
     * Can be called by any thread but is NOT multi-thread safe!
     * If the mid is nil, a new mid is assigned.
     * If the xmlFileUrl is nil, a new file name is automatically assigned.
     */
    @objc func save() {
        if mid == nil {
            mid = MediaUtil.createMid()
        }
        if originalMid == nil {
            originalMid = mid
        }

        let conf = StoredElementData.SceneConf()
        conf.inputUrls = inputUrls ?? []

        let data = StoredElementData()
        data.fileType = .mmart
        data.mid = mid
        data.originalMid = originalMid
        data.dependencies = subAssets
        data.rank = rank
        data.section = section
        data.supportedAspectRatios = supportedAspectRatios
        data.applyOnProgram = applyOnProgram
        data.topmost = topmost
        data.vPropHolder = _vPropHolder
        data.thumbnails = thumbnail != nil ? [thumbnail!] : []
        data.conf = conf
        data.devData = devData
        data.editor = editorDef
        data.transition = transition
        if let macroShapeRoot = macroShapeRoot {
            macroShapeRoot.setTimers(timers)
            data.macroShapeRoot = macroShapeRoot
        } else {
            data.timers = timers
        }
        data.isAutoDismissEnabled = (isAutoDismissEnabled && isOverlay) ? true : nil
        data.autoDismissDelay = autoDismissCapability == .basedOnDelay ? autoDismissDelay : .nan

        Task { @MainActor in
            if let url = xmlFileUrl {
                data.write(to: url, overwrite: true)
            } else {
                let fn: String
                if let preferred = preferredXmlFileName {
                    fn = preferred
                } else {
                    fn = defaultXmlFileName
                }
                xmlFileUrl = data.write(preferredName: fn)
            }

            thumbnail = thumbnail?.inFileThumbnail(with: xmlFileUrl!)
        }
    }
}

private func vMaker(artworkName name: String) -> VMaker? {
    if name == "image" {
        return VMakerSource(type: .gen)
    } else if name == "logo" {
        return VMakerSource(type: .overlaidGen)
    } else if name == "shape-composition" || name == "macro-shape" {
        return VMakerShapeComposition()
    } else if name == "grid" {
        return VMakerGeneric(filterDef: VFilterGridDef())
    } else if name == "gen" {
        return VMakerGeneric(filterDef: VGenSolidDef())
    } else if name == ".checker" {
        return VMakerGeneric(filterDef: VGenCheckerDef())
    } else if name == ".abc-placeholder" {
        return VMakerText()
    } else {
        return nil
    }
}
