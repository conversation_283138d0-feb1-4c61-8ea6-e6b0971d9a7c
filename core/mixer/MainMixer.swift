//
//  MainMixer.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON> on 18.02.20.
//  Copyright © 2020 Switcher Inc. All rights reserved.
//

import Foundation
import InternalBridging

enum M2Domain: Int, CaseIterable {
    case thumbnail
    case preview
    case program
    case previewSceneInput // also for auxiliary overlay inputs
    case programSceneInput // also for auxiliary overlay inputs
    case anonimous
    case programLeavingOverlay
}

@MainActor
protocol MainMixerProtocol: AnyObject {
    var channelManager: any M2ChannelManagerProtocol { get }
    var channelCatalog: M2ChannelCatalog { get }
    var delegates: M2Delegates { get }
    var lastSpeedStatus: String { get }
    var lastSpeedResult: Double { get }
    var resyncDelay: Float { get }
    var sourcePanelLogger: any SourcePanelLoggerProtocol { get }

    func startProd()
    func stopProd()
    func addMarkersRecorder(_ recorder: any MarkersRecorderProtocol)
    func removeMarkersRecorder(_ recorder: any MarkersRecorderProtocol)
}

@MainActor
@objc public class MainMixer: NSObject, MainMixerProtocol {

    let vContext: VContext
    let renderTarget: VSize
    let videoChatSceneBuilder: M2VideoChatSceneBuilder
    let videoChatIdentityOverlaysBuilder: M2VideoChatIdentityOverlaysBuilder
    final let pilot: M2Pilot
    let multiviewBuilder: M2MultiviewBuilder
    let channelManager: any M2ChannelManagerProtocol
    let sourceOverlayManager: M2SourceOverlayManager
    let autoAddToProgram: M2AutoAddToProgram
    let sourcePreviewManager: M2SourcePreviewManager
    let scenePreviewManager: M2ScenePreviewManager
    @objc final let channelCatalog: M2ChannelCatalog
    let previewManager: M2ComboManager
    let programManager: M2ComboManager
    @objc final let delegates = M2Delegates()
    let staticChannelCollection: M2StaticChannelCollection
    @objc final let util: M2Util
    let dataHub = DHDataHub()
    let sourcePanelLogger: any SourcePanelLoggerProtocol = SourcePanelLogger()

    weak var actionManager: ElementCollectionActionManager?

    let uploadSpeed = UploadSpeedService()
    private var uploadSpeedTask: Task<Void, Never>?
    // we just copy these properties to expose in objective-c
    var lastSpeedStatus: String {
        "\(uploadSpeed.status)"
    }
    var lastSpeedResult: Double {
        uploadSpeed.result ?? -1.0
    }

    var isPreviewAvailable = false {
        didSet {
            if isPreviewAvailable != oldValue {
                pilot.isPreviewAvailable = isPreviewAvailable
            }
        }
    }

    init(vContext: VContext, renderTarget: VSize) {
        self.vContext = vContext
        self.renderTarget = renderTarget
        channelManager = M2ChannelManager(
            vContext: vContext,
            renderTarget: renderTarget,
            delegates: delegates,
            dataHub: dataHub
        )
        staticChannelCollection = M2StaticChannelCollection(vContext: vContext, renderTarget: renderTarget)
        videoChatSceneBuilder = M2VideoChatSceneBuilder()
        videoChatIdentityOverlaysBuilder = M2VideoChatIdentityOverlaysBuilder()
        sourceOverlayManager = M2SourceOverlayManager(
            videoChatSceneBuilder: videoChatSceneBuilder,
            videoChatIdentityOverlaysBuilder: videoChatIdentityOverlaysBuilder
        )
        channelCatalog = M2ChannelCatalog()
        util = M2Util(catalog: channelCatalog)
        previewManager = M2ComboManager(use: .preview, channelManager: channelManager)
        programManager = M2ComboManager(use: .program, channelManager: channelManager)
        multiviewBuilder = M2MultiviewBuilder(previewManager: previewManager)
        autoAddToProgram = M2AutoAddToProgram(
            programManager: programManager,
            sourceOverlayManager: sourceOverlayManager
        )
        sourcePreviewManager = M2SourcePreviewManager(channelCatalog: channelCatalog)
        scenePreviewManager = M2ScenePreviewManager(channelCatalog: channelCatalog)
        pilot = M2Pilot(
            channelManager: channelManager,
            previewManager: previewManager,
            programManager: programManager,
            multiviewBuilder: multiviewBuilder,
            channelCatalog: channelCatalog,
            sourcePreviewManager: sourcePreviewManager,
            scenePreviewManager: scenePreviewManager,
            sourceOverlayManager: sourceOverlayManager,
            videoChatSceneBuilder: videoChatSceneBuilder,
            videoChatIdentityOverlaysBuilder: videoChatIdentityOverlaysBuilder
        )

        super.init()

        // all the class hierarchy is in place, we can not start creating channels
        staticChannelCollection.start(self)
        channelCatalog.start(staticChannelCollection)
        videoChatSceneBuilder.start(self)
        videoChatIdentityOverlaysBuilder.start(self)
        previewManager.start(staticChannelCollection)
        programManager.start(staticChannelCollection)
        sourcePreviewManager.start(staticChannelCollection)

        runSpeedTest()

    }

    var resyncDelay: Float = 0 {
        didSet {
            if resyncDelay != oldValue {
                channelManager._resyncDelay = resyncDelay
                journalRecorder?.resyncDelay = resyncDelay
            }
        }
    }

    func runSpeedTest() {
        self.cancelSpeedTest()
        self.uploadSpeedTask = Task { @MainActor in
            try? await Task.sleep(nanoseconds: 5000_000_000)
            if SwitcherState.shared.prodState == .idle {
                await self.uploadSpeed.run()
            }
            self.uploadSpeedTask = nil
        }
    }

    func cancelSpeedTest() {
        self.uploadSpeedTask?.cancel()
        self.uploadSpeedTask = nil
    }

    func setPreviewOutput(_ vFilter: VFilter, channel: Int) {
        let output = MixerVideoOutput(target: vFilter, channel: channel)
        previewManager.output = output
    }

    func setProgramOutput(_ vFilter: VFilter, channel: Int) {
        let output = MixerVideoOutput(target: vFilter, channel: channel)
        output.watermark = watermark
        output.assetStatsAreEnabled = true
        output.journalWriter = journalRecorder?.jwriter
        programManager.output = output
    }

    var watermark: M2MixerWatermark? {
        didSet {
            watermark?.load(in: vContext, renderTarget: renderTarget)
            programManager.output?.watermark = watermark
        }
    }

    var isWatermarkVisible: Bool {
        if let filter = programManager.output?.watermarkFilter {
            if let f = filter as? VFilterBanner {
                return f.isVisible
            }
        }
        return false
    }

    final func addChannel(_ coreElement: CoreElement, liveMediaProvider: Camera? = nil) -> M2Channel? {
        guard let ch = M2Channel(mainMixer: self, coreElement: coreElement, liveMediaProvider: liveMediaProvider) else {
            return nil
        }
        pilot.addChannel(ch)
        return ch
    }

    @objc(addLiveMediaProvider:) @discardableResult
    final func addLiveMediaProvider(_ liveMediaProvider: Camera) -> M2Channel? {
        let liveSource = LiveSource(hasVideo: liveMediaProvider.providesVideo)
        return addChannel(liveSource, liveMediaProvider: liveMediaProvider)
    }

    func startProd() {
        if let filter = programManager.output?.watermarkFilter {
            if let f = filter as? VFilterBanner {
                f.productionInProgress = true
            }
        }
    }

    func stopProd() {
        if let filter = programManager.output?.watermarkFilter {
            if let f = filter as? VFilterBanner {
                f.productionInProgress = false
            }
        }
    }

    private var journalRecorder: M2JournalRecorder?

    @objc final func startRec(conf: M2RecConf, name: String, gid: String) -> Int64 {
        guard conf.rec_conf.magic == REC_CONF_MAGIC else { FatalLogger.shared.logFatalError() }
        let startTime = gmu_mtime_us() // we cannot apply _jRefTS here because remote camera recording really starts now
        if conf.options.contains(.remoteCameraOnly) {
            journalRecorder = nil
        } else {
            let recName = name + " " + MediaUtil.localizedTextForJournal + ".mmjrnl"
            let folderUrl = MediaLibrary.shared.mediaDirUrl
            let url = folderUrl.appendingPathComponent(recName, isDirectory: false)
            journalRecorder = M2JournalRecorder(url: url, gid: gid, targetAspectRatio: conf.targetAspectRatio)
            journalRecorder!.resyncDelay = resyncDelay
            journalRecorder!.start(time: startTime)
            channelManager.emitCurrentPlayedSourcesState(to: journalRecorder!, refts: startTime)
            let audioSwitches = channelManager.createCurrentAudioState()
            let cur_tree = programManager.output?.tree
            journalRecorder?.jwriter?.writeViewFirst(video: cur_tree, audioSwitches: audioSwitches)
        }
        channelManager.startRec(
            conf: conf,
            name: name,
            gid: gid,
            startTime: startTime,
            journalRecorder: journalRecorder
        )
        programManager.output?.journalWriter = journalRecorder?.jwriter
        if let jrec = journalRecorder {
            dataHub.addOutput(jrec)
        }
        return startTime
    }

    @objc final func stopRec() {
        let stopTime = gmu_mtime_us() // we do not use _jRefTS here because we didn't for the start
        if let jrec = journalRecorder {
            dataHub.removeOutput(jrec)
        }
        programManager.output?.journalWriter = nil
        channelManager.stopRec()
        journalRecorder?.stop(time: stopTime)
        journalRecorder = nil
    }

    /**
     * This method should be called before releasing.
     * It unselects and detaches all channels. However, the user must first dispose
     * all cameras.
     */
    @objc func dispose(completion: (() -> Void)?) {
        pilot.dispose(completion: completion)
    }

    func addMarkersRecorder(_ recorder: any MarkersRecorderProtocol) {
        guard let recorder = recorder as? MarkersRecorder else {
            return
        }

        let result = dataHub.addChannel(
            channelId: DHChannelId.markers,
            dataNature: .event,
            dataType: MMIndexMarkerData.self
        )
        if result != .channelDefinitionMismatch {
            dataHub.addOutput(to: recorder, channelId: DHChannelId.markers)
        }
    }

    func removeMarkersRecorder(_ recorder: any MarkersRecorderProtocol) {
        guard let recorder = recorder as? MarkersRecorder else {
            return
        }

        if dataHub.hasChannel(channelId: DHChannelId.markers) {
            dataHub.removeOutput(to: recorder, channelId: DHChannelId.markers)
        }
    }
}

// Extension for Objective-C compatibility
extension MainMixer {
    @objc func getChannelManager() -> M2ChannelManager {
        return channelManager as! M2ChannelManager
    }
}
