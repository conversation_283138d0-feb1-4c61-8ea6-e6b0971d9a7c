//
//  MixerVideoOutput.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON> on 23.03.20.
//  Copyright © 2020 Switcher Inc. All rights reserved.
//

import Foundation
import InternalBridging

@MainActor
@objc public class MixerVideoOutput: NSObject {

    private let treeDefBuilder: TreeDefBuilder

    @objc let targetFilter: VFilter
    @objc public let targetChannel: Int

    private(set) var tree: VMakerNodeDef?
    @objc public private(set) var duration: Float = 0
    private(set) var watermarkFilter: VFilter?

    var journalWriter: JournalWriter?

    var watermark: M2MixerWatermark? {
        didSet {
            if let wm = watermark {
                watermarkFilter = wm.createFilter(vOutput: targetFilter.vOut)
            } else {
                watermarkFilter = nil
            }
        }
    }

    var assetStatsAreEnabled = false

    init(target: VFilter, channel: Int) {
        targetFilter = target
        targetChannel = channel
        self.treeDefBuilder = TreeDefBuilder(targetAspectRatio: OutputProfile.shared.targetAspectRatio)
    }

    deinit {
        guard tree == nil else { FatalLogger.shared.logFatalError() }
        guard watermarkFilter == nil else { FatalLogger.shared.logFatalError() }
    }

    func dispose() {
        targetFilter.setInput(channel: targetChannel, input: nil)
        tree = nil
        duration = 0
        watermarkFilter = nil
    }

    final func apply(combo: M2Combo, transition: M2Channel?, multiview: M2Channel?, timestamps: Timestamps) {
        var tranNode: VMakerNodeDef?
        if let transition,
            let propHolder = transition.propHolder?.vPropHolder {
            let propBundle = propHolder.propBundle
            let tran = VMakerNodeDef(inputCount: 0)
            tran.propBundle = propBundle
            tran.sourceAssignment = transition.sourceAssignment
            tranNode = tran
        }
        apply(combo: combo, tran: tranNode, rootTran: false, multiview: multiview, timestamps: timestamps)
    }

    // no transition for entering and exiting multiviews
    final func apply(combo: M2Combo, fadeDuration: Float, multiview: M2Channel?) {
        guard journalWriter == nil else { FatalLogger.shared.logFatalError() }
        let tran: VMakerNodeDef?
        if fadeDuration == 0 {
            tran = nil
            // force no transition at all, even on multiviews
            tree = nil
        } else {
            tran = treeDefBuilder.createCrossFadeDef(duration: fadeDuration)
        }
        apply(combo: combo, tran: tran, rootTran: true, multiview: multiview, timestamps: nil)
    }

    private func apply(combo: M2Combo,
                       tran: VMakerNodeDef?,
                       rootTran: Bool,
                       multiview: M2Channel?,
                       timestamps: Timestamps?) {

        if assetStatsAreEnabled {
            AssetStats.shared.reportProgramFeedChange(combo: combo, multiview: multiview)
        }

        let newTree = treeDefBuilder.createTreeDef(combo: combo, multiview: multiview)
        journalWriter?.writeViewSwitch(JournalSwitch(refts: timestamps?.refts ?? 0,
                                                     filterTree: newTree,
                                                     transition: tran,
                                                     audioSwitches: []))

        if let wm = watermarkFilter {
            targetFilter.setInput(channel: targetChannel, input: wm)
            duration = VMakerNodeDef.setupFilterTree(filterDest: wm,
                                                     channel: 0,
                                                     currentNode: tree,
                                                     newNode: newTree,
                                                     transitionNode: tran,
                                                     rootTran: rootTran)
        } else {
            duration = VMakerNodeDef.setupFilterTree(filterDest: targetFilter,
                                                     channel: targetChannel,
                                                     currentNode: tree,
                                                     newNode: newTree,
                                                     transitionNode: tran,
                                                     rootTran: rootTran)
        }

        tree = newTree
    }

    final func updateProperties(_ vprop_bundle: VPropBundle) {
        guard journalWriter == nil else { FatalLogger.shared.logFatalError() }
        updateProperties(vprop_bundle, timestamps: nil)
    }

    final func updateProperties(_ propBundle: VPropBundle, timestamps: Timestamps?) {
        if let tree {
            var rootFilter = targetFilter
            var rootChannel = targetChannel
            if let wm = watermarkFilter {
                rootFilter = wm
                rootChannel = 0
            }
            let maker = propBundle.vMaker!
            let result = tree.findFilterByVmaker(filterDest: rootFilter, channel: rootChannel, vmaker: maker)
            if let node = result.node, let filter = result.filter {
                let oldProps = node.propBundle!
                let newProps = propBundle
                if newProps != oldProps {
                    node.propBundle = newProps
                    filter.setProps(props: newProps)
                    journalWriter?.writeViewSwitch(JournalSwitch(refts: timestamps?.refts ?? 0,
                                                                 filterTree: tree,
                                                                 transition: nil,
                                                                 audioSwitches: []))
                }
            }
        }
    }
}
