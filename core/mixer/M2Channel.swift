//
//  M2Channel.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON> on 27.03.21.
//  Copyright © 2021 Switcher Inc. All rights reserved.
//

import Foundation
import Combine

@MainActor
protocol M2ChannelPreparationIntent: AnyObject {

    /**
     * Invoked once the channel is attached and all resources loaded.
     * The intent is responsible to unbind itself from the channel.
     */
    func channelDidSucceedPreparing(_ channel: M2Channel)

    /**
     * Invoked when the channel cannot be attached or resourcs cannot be loaded.
     * The intent is NOT automatically unbound from the channel.
     * The intent is responsible to unbind itself from the channel.
     */
    func channelDidFailPreparing(_ channel: M2Channel)
}

enum LiveEmployment {
    case none
    case active    // on program
    case leaving   // leaving program
    case suspended // on preview or being prepared
}

@MainActor
protocol LiveEmploymentWatcher: AnyObject {
    func liveEmploymentChanged(value: LiveEmployment)
}

enum M2ChannelAttachmentState {
    case detached
    case attaching
    case attachingToMixer
    case attached
    case detaching
    case detachingFromMixer
    case error
}

class DualityWrapper_M2ChannelAttachmentState {

    private var protectedState_Current: M2ChannelAttachmentState
    private var protectedState_Target: M2ChannelAttachmentState

    init(state: M2ChannelAttachmentState) {
        self.protectedState_Current = state
        self.protectedState_Target = state
    }

    var current: M2ChannelAttachmentState {
        get {
            return self.protectedState_Current
        }

        set {
            self.protectedState_Current = newValue
        }
    }

    var target: M2ChannelAttachmentState {
        get {
            return self.protectedState_Target
        }

        set {
            self.protectedState_Target = newValue
        }
    }
}

enum M2ChannelAttachmentType {
    case none
    case constant
    case onDemand
}

@MainActor
public class M2Channel: NSObject, PropHolderDelegate {

    let id = UUID().uuidString

    private lazy var logger = LsLogger(subsystem: "swi.core", category: "M2Channel")

    private weak var channelManager: (any M2ChannelManagerProtocol)?
    private(set) weak var mainMixer: MainMixer?

    /**
     * This property is set when the channel is visible outside the MainMixer.
     * This is the case after the channel appears (after channelDidAppear is fired)
     * and before it disappears (before channelWillDisappear is fired).
     */
    final var isVisible = false

    let propHolder: PropHolder?

    let coreElement: CoreElement

    final var storedElement: (any StoredElement)? {
        return coreElement as? (any StoredElement)
    }

    final var artwork: Artwork? {
        return coreElement as? Artwork
    }

    final var liveSource: LiveSource? {
        return coreElement as? LiveSource
    }

    final var playedSource: PlayedSource? {
        return coreElement as? PlayedSource
    }

    @objc final var camera: Camera? {
        return liveMediaProvider
    }

    final var effect: Effect? {
        return coreElement as? Effect
    }

    final var mid: String? {
        return artwork?.mid ?? playedSource?.mid
    }

    final var originalMid: String? {
        return artwork?.originalMid ?? playedSource?.originalMid
    }

    var sourcePanelType: SourcePanelLogType {
        if effect != nil {
            return .multiView
        } else if let artwork {
            return artwork.sourcePanelType
        } else if let camera {
            return .liveInput(isRemote: camera.isRemote)
        } else if let playedSource {
            return .video(isOverlay: playedSource.isOverlay)
        } else {
            FatalLogger.shared.logFatalError("SourcePanelLogType not implemented")
        }
    }

    /*
     * for some reasons, there are some channels in channelManager but all channels are not visible in the UI
     * So this function return true if the channel is visible in the mixer
     * (from the SelectSourceView or ElementsGroupView)
     */
    final var shouldBeVisibleInGroups: Bool {
        if coreElement.isHidden {
            return false
        }
        if isEffect {
            let type = effect!.type
            if type != .dynamicMultiview && type != .scene {
                return false
            }
        }
        return isVisible
    }

    let mediaProvider: MediaProvider?
    let playedMediaProvider: PlayedMediaProvider?
    let liveMediaProvider: Camera?

    private var audioFadeOutWatcher: FadeOutWatcher?
    private var audioFadeOutCancellable: (any Cancellable)?

    init?(mainMixer: MainMixer, coreElement: CoreElement, liveMediaProvider: Camera?) {
        self.mainMixer = mainMixer
        self.channelManager = mainMixer.channelManager
        self.coreElement = coreElement
        self.isEffect = coreElement is Effect
        self.isLiveSource = coreElement is LiveSource
        self.isPlayedSource = coreElement is PlayedSource
        let artwork = coreElement as? Artwork
        self.isArtwork = artwork != nil
        self.isTitle = artwork?.isTitle ?? false

        if let liveSource = coreElement as? LiveSource {
            liveSource.sptz = liveMediaProvider!.localSptz
            self.liveMediaProvider = liveMediaProvider!
            self.playedMediaProvider = nil
            self.mediaProvider = liveMediaProvider!
        } else if let playedSource = coreElement as? PlayedSource {
            self.liveMediaProvider = nil
            self.playedMediaProvider = PlayedMediaProvider()
            self.playedMediaProvider!.playedMediaUrl = playedSource.playedMediaUrl
            self.playedMediaProvider!.playedMediaConf = playedSource.playedMediaConf
            self.mediaProvider = playedMediaProvider
        } else {
            self.liveMediaProvider = nil
            self.playedMediaProvider = nil
            self.mediaProvider = nil
        }

        if let mediaProvider = self.mediaProvider {
            self.isAudioOnly = mediaProvider.providesAudio && !mediaProvider.providesVideo
        } else {
            self.isAudioOnly = false
        }
        let type = coreElement.vPropHolder?.vMaker?.type ?? .none
        let inputUrls: [URL]?
        if let artwork = coreElement as? Artwork {
            isScene = artwork.isScene
            inputUrls = artwork.inputUrls
        } else if let effect = coreElement as? Effect {
            isScene = effect.type == .scene
            inputUrls = effect.inputUrls
        } else {
            isScene = false
            inputUrls = nil
        }
        if isScene {
            isOverlay = inputUrls!.contains(SourcePlaceholderUrl.overlayBackground)
        } else {
            isOverlay = type == .overlaidGen || type == .overlaidInput
        }
        if let vPropHolder = coreElement.vPropHolder {
            self.propHolder = PropHolder(vPropHolder: vPropHolder)
        } else {
            self.propHolder = nil
        }

        if self.coreElement is PlayedSource {
            attachmentType = .onDemand
        } else if self.coreElement is LiveSource {
            attachmentType = .constant
        } else {
            attachmentType = .none
        }

        super.init()

        camera?.angle = 0
        camera?.liveOff()
        camera?.resyncDelay = mainMixer.channelManager._resyncDelay

        isRecording = camera?.isRecording ?? false
        recStartTimeAvailable = camera?.isRecordingStartTimeAvailable ?? false
        recStartTime = camera?.recordingStartTime ?? 0
        recMID = camera?.recordingMID

        if let vmaker = coreElement.vPropHolder?.vMaker as? VMakerSource {
            let type = vmaker.type
            if type == .input || type == .overlaidInput {
                vmaker.videoSrcChannel = index
            }
        }

        if attachmentType == .constant {
            attachmentState.target = .attached
            attachment_runStateMachine()
            if attachmentState.current == .error {
                return nil
            }
        }

        propHolder?.addDelegate(self)

        if let notifiactionNameCString = kCHANNEL_FADED_NOTIFICATION {
            let notifiactionNameString = String(cString: notifiactionNameCString)
            let notificationName = NSNotification.Name(notifiactionNameString)

            NotificationCenter.default.addObserver(self, selector: #selector(fadeoutNotification),
                                                   name: notificationName, object: nil)
        }

        // If it's a PlayedSource, start the fade out before the end of the playback is reached
        if let playedSource = playedSource,
           let playedMediaProvider = playedMediaProvider {
            if playedSource.playbackMode == PLAYBACK_MODE_PLAY_ONCE {
                self.audioFadeOutWatcher = FadeOutWatcher(playbackProgressWatcher: playedMediaProvider.progressWatcher,
                                                          fadeOutDuration: Double(playedSource.audioFadeOutDuration))
            } else if playedSource.playbackMode == PLAYBACK_MODE_LOOP_COUNT {
                self.audioFadeOutWatcher = FadeOutWatcher(playbackProgressWatcher: playedMediaProvider.progressWatcher,
                                                          fadeOutDuration: Double(playedSource.audioFadeOutDuration),
                                                          mode: .loopCount(playedSource.loopCount))
            } else if playedSource.playbackMode == PLAYBACK_MODE_DISPLAY_DURATION {
                self.audioFadeOutWatcher = FadeOutWatcher(playbackProgressWatcher: playedMediaProvider.progressWatcher,
                                                          fadeOutDuration: Double(playedSource.audioFadeOutDuration),
                                                          mode: .customDuration(Double(playedSource.loopDuration)))
            }

            if let audioFadeOutWatcher = audioFadeOutWatcher {
                self.audioFadeOutCancellable = audioFadeOutWatcher.shouldFadeOutPublisher
                    .sink { [weak self] shouldFadeOut in
                        if shouldFadeOut {
                            self?.startAudioFadeOutIfNeeded()
                        }
                    }
            }
        }
    }

    deinit {
        reportIfOutsideMainThread(criticality: .low)
    }

    @objc public func dispose() {
        mainMixer?.pilot.removeChannel(self)
    }

    @objc func fadeoutNotification(_ notification: Notification) {
        guard let channelKeyCString = kCHANNEL_KEY else {
            return
        }

        let channelKeyString = String(cString: channelKeyCString)
        let channelKey = NSNotification.Name(channelKeyString)

        if let userInfo = notification.userInfo,
           let channelNumber = userInfo[channelKey] as? Int,
           channelNumber == index {
            DispatchQueue.main.async { [weak self] in
                guard let self else {
                    return
                }

                self.isAudioFadeOutInProgress = false

                if self.attachmentState.current == .detaching {
                    self.detachFromMixer()
                }
            }
        }
    }

    var isLivePaused: Bool {
        guard let playedMediaProvider else {
            return false
        }

        return playedMediaProvider.isLivePaused()
    }

    func livePause() {
        guard let src = playedSource,
              let playedMediaProvider else {
            return
        }

        playedMediaProvider.livePause()

        let mediaTime = playedMediaProvider.progressWatcher.player?.currentPlayTime
        let mediaURL = playedMediaProvider.playedMediaUrl

        if let journalRecorder = channelManager!.journalRecorder,
           _isPlaybackSavedInJournal,
           let mainMixer,
           let mediaTime,
           let mediaURL {
            let timestamps = mainMixer.programManager.channelManager.getTimestamps()

            if let jwriter = journalRecorder.jwriter {
                jwriter.writePrerecordedMediaPause(
                    channel: index,
                    url: mediaURL,
                    mts: Int64(mediaTime * 1000000.0),
                    refts: timestamps.refts,
                    hasAudio: src.hasAudio,
                    hasVideo: src.hasVideo)
            }
        }
    }

    func liveResume() {
        guard let src = playedSource else {
            return
        }

        guard let playedMediaProvider else {
            return
        }

        playedMediaProvider.liveResume()

        let mediaTime = playedMediaProvider.progressWatcher.player?.currentPlayTime

        if let journalRecorder = channelManager!.journalRecorder,
           _isPlaybackSavedInJournal,
           let mainMixer,
           let mediaTime {
            let timestamps = mainMixer.programManager.channelManager.getTimestamps()

            if let jwriter = journalRecorder.jwriter {
                jwriter.writePrerecordedMediaStart(
                    channel: index,
                    mts: Int64(mediaTime * 1000000.0),
                    refts: timestamps.refts,
                    src: src,
                    resume: true)
            }
        }
    }

    // MARK: - Channel Type

    enum PlaceholderType {
        case none
        case black
        case checker
        case missingSourceIndicator
    }

    final let isEffect: Bool
    final let isLiveSource: Bool
    final let isPlayedSource: Bool
    final let isArtwork: Bool
    final let isTitle: Bool
    final let isAudioOnly: Bool
    private(set) final var isOverlay: Bool
    final var placeholderType: PlaceholderType = .none
    final let isScene: Bool

    private func updateType() {
        let type = coreElement.vPropHolder?.vMaker?.type ?? .none
        isOverlay = type == .overlaidGen || type == .overlaidInput
    }

    /**
     * When a channel mutates from source to overlay, we need to act as if the channel disappears and then reappears.
     * To perform this mutation, first call `beginTypeMutation()`, then update the properties which change the type,
     * then call `endTypeMutation()`.
     */
    final func beginTypeMutation() {
        guard let mainMixer else { return }

        mainMixer.pilot.channelWillDisappear(self)
        mainMixer.delegates.fireChannelWillDisappear(self)
        mainMixer.pilot.channelDidDisappear(self)
    }

    final func endTypeMutation() {
        guard let mainMixer else { return }

        updateType()
        mainMixer.pilot.channelWillAppear(self)
        mainMixer.delegates.fireChannelDidAppear(self)
        mainMixer.pilot.channelDidAppear(self)
    }

    final var inputUrls: [URL]? {
        get {
            if let effect = coreElement as? Effect {
                return effect.inputUrls
            } else if let artwork = coreElement as? Artwork {
                return artwork.inputUrls
            } else {
                return nil
            }
        }
        set {
            if let effect = coreElement as? Effect {
                effect.inputUrls = newValue
            } else if let artwork = coreElement as? Artwork {
                artwork.inputUrls = newValue
            }
        }
    }

    // MARK: - PropHolder and Resource Loading (PropHolderDelegate)

    nonisolated public func propHolderValueDidChange(propHolder: PropHolder, propIndex: Int, sender: AnyObject?) {
    }

    nonisolated public func propHolderApplyPropChanges(propHolder: PropHolder) {
        Task { @MainActor in
            mainMixer?.pilot.channelPropsDidUpdate(self)
            preparation_runStateMachine()
        }
    }

    // MARK: - Binding & Preparation

    private var preparationIntents = [any M2ChannelPreparationIntent]()
    private var preparationState: PreparationState = .unprepared

    /**
     * Being bound does not mean being preparing. You can have a bounded channel
     * that is ready.
     */
    final var isBound: Bool {
        return !preparationIntents.isEmpty
    }

    /**
     * This method start preparing the channel. Preparation includes attaching
     * the channel to the audio/video source and loading all resources.
     * When the preparation is finished, the given intent is notified about the
     * result.
     * When you bind an intent, you are responsible to unbind it as well.
     */
    final func bind(_ intent: any M2ChannelPreparationIntent) {
        if self.placeholderType != .none {
            intent.channelDidSucceedPreparing(self)
            return
        }

        let wasBoundAlready = !preparationIntents.isEmpty

        preparationIntents.append(intent)

        if !wasBoundAlready && isVisible {
            mainMixer?.delegates.notifyEmploymentChange(self)
        }

        switch preparationState {
        case .ready:
            intent.channelDidSucceedPreparing(self)
        case .failed:
            intent.channelDidFailPreparing(self)
        default:
            preparation_runStateMachine()
        }
    }

    /**
     * Unbind an intent bound with bind(). It's totally fine to unbind while the
     * preparation is not finished. In this case, the preparation is aborted
     * (except if other intents are still bound) and the unbound intent is not
     * notified at all.
     */
    final func unbind(_ intent: any M2ChannelPreparationIntent, timestamps: Timestamps? = nil) {
        if let index = preparationIntents.firstIndex(where: { $0 === intent }) {
            preparationIntents.remove(at: index)
            if preparationIntents.isEmpty {
                if isVisible {
                    mainMixer?.delegates.notifyEmploymentChange(self)
                }
                preparationState = .unprepared
                updateLiveEmploymentIfNeeded(timestamps: timestamps)
                relaxIfNotUsed()
            }
        }
    }

    private func preparation_runStateMachine() {
        switch preparationState {
        case .unprepared:
            if !preparationIntents.isEmpty {
                // preparation starts here - it can happen even when the element is still employed
                initializeOnDemandDataSources()
                preparation_startAttaching()
            }
        case .attaching:
            let currentAttachmentState = self.attachmentState.current
            if currentAttachmentState == .attached {
                preparation_startLoading()
            } else if currentAttachmentState == .detaching || currentAttachmentState == .detached {
                preparation_failed()
            }
        case .loading:
            if !propHolder!.isInProgress {
                // work started by startFirstProcessResolveLoadProcedure() ends here
                preparation_succeeded()
            }
        case .ready:
            assert(!preparationIntents.isEmpty)
        case .failed:
            assert(!preparationIntents.isEmpty)
        }
    }

    private func preparation_startAttaching() {
        let currentAttachmentState = self.attachmentState.current

        switch currentAttachmentState {
        case .detached, .detaching, .detachingFromMixer:
            if attachmentType == .onDemand {
                preparationState = .attaching
                attachmentState.target = .attached
                attachment_runStateMachine()
                if attachmentState.current == .error {
                    preparation_failed()
                }
            } else {
                preparation_startLoading()
            }
        case .attaching, .attachingToMixer:
            preparationState = .attaching
        case .attached:
            preparation_startLoading()
        case .error:
            preparation_failed()
        }
    }

    private func preparation_startLoading() {
        guard let channelManager else { return }

        if let vContext = self.channelManager?.vContext {
            if let propHolder = self.propHolder {
                if propHolder.needsFirstProcessResolveLoadProcedure,
                   let vContext = vContext as? VContext {
                    preparationState = .loading
                    propHolder.startFirstProcessResolveLoadProcedure(
                        vContext: vContext,
                        loadingConstraints: channelManager.renderTarget)
                } else if propHolder.isInProgress {
                    preparationState = .loading
                } else {
                    preparation_succeeded()
                }
            } else {
                preparation_succeeded()
            }
        } else {
            preparation_failed()
        }
    }

    private func preparation_succeeded() {
        // preparation ends here
        preparationState = .ready
        for intent in preparationIntents {
            intent.channelDidSucceedPreparing(self)
        }
    }

    private func preparation_failed() {
        preparationState = .failed
        for intent in preparationIntents {
            intent.channelDidFailPreparing(self)
        }
    }

    private func _didAttach() {
        preparation_runStateMachine()
        attachment_runStateMachine()
        if attachmentState.current == .attached && liveEmployment == .active && !isPlaying {
            isPlaying = true
            mediaProvider?.liveOn()
        }
    }

    private func _didDetach() {
        preparation_runStateMachine()
        attachment_runStateMachine()
        updateLiveEmploymentIfNeeded(timestamps: nil)
    }

    private func relaxIfNotUsed() {
        if !isEmployed && preparationIntents.isEmpty {
            relax()
        }
    }

    /**
     * Channels are prepared, then employed, and finally relaxed.
     * They are relaxed once they are not emplyed anymore and are not being prepared for any further emplyment.
     * This method is invoked to relay the channel. It should undo what done during preparation.
     */
    private func relax() {
        if attachmentType == .onDemand && attachmentState.current != .detached {
            attachmentState.target = .detached
            attachment_runStateMachine()
        }
    }

    // MARK: - Data Sources

    private(set) var sourceAssignment: VSourceAssignment?
    private var areOnDemandDataSourcesAssigned = false
    var timers: [TimerDataSource] = []
    var timerDataChannelIds: [String] = []
    var timerProgressWatcher = TimerProgressWatcher()

    /**
     * This method is invoked every time the element is prepared, i.e. each
     * time we want to bring it in the preview or program feed.
     * This can happen even when the element is currently employed. In fact,
     * if you remove an overlay from screen and bring it back while fading out,
     * this element stays employed but is prepared again.
     * The first invokation happen before any on-demande attachment. So, here
     * it's the right place to create players.
     */
    private func initializeOnDemandDataSources() {
        if !areOnDemandDataSourcesAssigned {
            areOnDemandDataSourcesAssigned = true

            // Clean-up existing data sources (for thumbnail)
            self.sourceAssignment = nil

            for timerConf in self.artwork?.timers ?? self.effect?.timers ?? [] {
                let dataHub = self.mainMixer!.dataHub
                var channelId: String?
                var isGlobal = false
                if let globalId = timerConf.dataChannelId, globalId.hasPrefix("\(DHChannelId.global).") {
                    let result = dataHub.addChannel(
                        channelId: globalId,
                        dataNature: .state,
                        dataType: TimerDataPoint.self)
                    if result == .channelCreatedSuccessfully {
                        let pushable: DHAnyDataPushable<TimerDataPoint> = dataHub.pushable(channelId: globalId)
                        let ts = max(gmu_mtime_us(), pushable.begin())
                        pushable.push(TimerDataPoint(), ts: ts)
                        pushable.end()
                    }
                    if result != .channelDefinitionMismatch {
                        channelId = globalId
                        isGlobal = true
                    }
                }
                if channelId == nil {
                    let customId = "\(DHChannelId.custom).\(UUID().uuidString)"
                    let result = dataHub.addChannel(
                        channelId: customId,
                        dataNature: .state,
                        dataType: TimerDataPoint.self)
                    if result != .channelDefinitionMismatch {
                        channelId = customId
                    }
                }
                if let id = channelId {
                    let provider = dataHub.ambiguousProvider(channelId: id)
                    if let sourceAssignment = self.sourceAssignment {
                        sourceAssignment.dataSources.append(VSourceAssignment.DataSource(id: id, provider: provider))
                    } else {
                        self.sourceAssignment = VSourceAssignment(dataSources: [VSourceAssignment.DataSource(
                            id: id,
                            provider: provider)])
                    }
                    if !isGlobal {
                        let timer = TimerDataSource(conf: timerConf, output: dataHub.pushable(channelId: id))
                        self.timers.append(timer)
                    }
                    timerDataChannelIds.append(id)
                }
            }

            if isOverlay && playedSource == nil {
                var needsAutoDismissTimer: Bool = false
                var autoDismissDuration: Double = 0.0
                if coreElement.isAutoDismissEnabled &&
                    coreElement.autoDismissCapability == .basedOnDelay {
                    needsAutoDismissTimer = true
                    autoDismissDuration = coreElement.autoDismissDelay
                    logger.info("Set auto-dismiss based on delay: \(autoDismissDuration)s")
                }
                if needsAutoDismissTimer {
                    /*
                     * We create a timer just to display progress and manage auto dismiss.
                     * This timer is not connected to the data hub and there is no data channel ID.
                     */
                    let conf = TimerConf(start: autoDismissDuration,
                                         stop: 0,
                                         dataChannel: 0,
                                         thumbnailProgress: 0)
                    let timer = TimerDataSource(conf: conf, output: timerProgressWatcher.eraseToAnyDataPushable())
                    self.autoDismissTimer = timer
                }
            }

        }

        if !timerProgressWatcher.isVisible {
            for timer in timers {
                timer.reset()
            }
            autoDismissTimer?.reset()
        }
    }

    /**
     * This method is invoked to setup the thumbnail for live views.
     */
    func initializeThumbnailDataSources() {
        for timerConf in self.artwork?.timers ?? self.effect?.timers ?? [] {
            let dataHub = self.mainMixer!.dataHub
            let customId = "\(DHChannelId.thumbnail).\(UUID().uuidString)"
            _ = dataHub.addChannel(channelId: customId, dataNature: .state, dataType: TimerDataPoint.self)

            // Define timer values for thumbnail
            let demoTime_us: Int64
            let demoRefTime_us: Int64
            let maxTimerValue: Double = max(timerConf.start, timerConf.stop)
            if maxTimerValue == .infinity {
                demoTime_us = 0
                demoRefTime_us = 0
            } else {
                demoTime_us =
                Int64((timerConf.start + Double(timerConf.thumbnailProgress) * (timerConf.stop - timerConf.start)) *
                      1_000_000.0)
                demoRefTime_us = Int64(maxTimerValue * 1_000_000.0)
            }
            let dp = TimerDataPoint(val: demoTime_us, min: 0, max: demoRefTime_us)
            let pushable: DHAnyDataPushable<TimerDataPoint> = dataHub.pushable(channelId: customId)
            let ts = max(gmu_mtime_us(), pushable.begin())
            pushable.push(dp, ts: ts)
            pushable.end()

            // Assign data source
            let provider = dataHub.ambiguousProvider(channelId: customId)
            if let sourceAssignment = self.sourceAssignment {
                sourceAssignment.dataSources.append(VSourceAssignment.DataSource(id: customId, provider: provider))
            } else {
                self.sourceAssignment = VSourceAssignment(dataSources: [VSourceAssignment.DataSource(
                    id: customId,
                    provider: provider)])
            }
        }
    }

    // MARK: - Employment

    func employmentDescription() -> String {
        var ret = ""
        for domain in M2Domain.allCases {
            let count = employCounts[domain]
            let desc = "\(domain)=\(count)"
            if ret.isEmpty {
                ret = desc
            } else {
                ret += " " + desc
            }
        }
        return ret
    }

    private(set) var employCounts = DomainToIntMap()

    final func isEmployed(in domain: M2Domain) -> Bool {
        return employCounts[domain] > 0
    }

    final var isEmployed: Bool {
        return !employCounts.values.allSatisfy({ $0 == 0 })
    }

    final var willFailPreparation: Bool {
        return attachmentType == .onDemand && !isEmployed && !isBound
    }

    final func employ(in domain: M2Domain, timestamps: Timestamps? = nil) {
        // Videos in thumbnail shouldn't block a channel so we don't employ them
        guard !(isPlayedSource && domain == .thumbnail) else {
            return
        }

        let capture = getThumbnail()
        mainMixer?.sourcePanelLogger.addActivation(id: id, type: self.sourcePanelType, capture: capture)
        let count = employCounts[domain]
        let firstEmployment = count == 0
        employCounts[domain] = count + 1
        if firstEmployment {
            if isVisible {
                mainMixer?.delegates.notifyEmploymentChange(self)
            }
            if domain == .thumbnail,
               let ph = propHolder,
               let channelManager,
               ph.needsFirstProcessResolveLoadProcedure,
               let vContext = channelManager.vContext as? VContext {
                propHolder!.startFirstProcessResolveLoadProcedure(
                    vContext: vContext,
                    loadingConstraints: channelManager.renderTarget)
            }
            // program employment could have changed
            updateLiveEmploymentIfNeeded(timestamps: timestamps)
            // tally could have changed
            updateTallyIfNeeded()
        }
    }

    final func unemploy(in domain: M2Domain, timestamps: Timestamps? = nil) {
        guard !(isPlayedSource && domain == .thumbnail) else {
            return
        }
        let capture = getThumbnail()
        mainMixer?.sourcePanelLogger.addDeactivation(id: id, type: self.sourcePanelType, capture: capture)
        let count = employCounts[domain]
        assert(count >= 1)
        let lastEmployment = count == 1
        employCounts[domain] = count - 1
        if lastEmployment {
            if isVisible {
                mainMixer?.delegates.notifyEmploymentChange(self)
            }
            // program employment could have changed
            updateLiveEmploymentIfNeeded(timestamps: timestamps)
            // tally could have changed
            updateTallyIfNeeded()
            // maybe this was the last emplyment for all domains
            relaxIfNotUsed()
        }
    }

    private var liveEmploymentDelegates = WeakDelegateList<any LiveEmploymentWatcher>()

    final func addLiveEmploymentDelegate(_ delegate: any LiveEmploymentWatcher) {
        liveEmploymentDelegates.add(delegate)
    }

    final func removeLiveEmploymentDelegate(_ delegate: any LiveEmploymentWatcher) {
        liveEmploymentDelegates.remove(delegate)
    }

    private func fireLiveEmploymentDidChange() {
        for delegate in liveEmploymentDelegates {
            delegate.liveEmploymentChanged(value: self.liveEmployment)
        }
    }

    private(set) var liveEmployment: LiveEmployment = .none {
        didSet {
            fireLiveEmploymentDidChange()
        }
    }

    var isLive: Bool {
        liveEmployment == .active || (liveEmployment == .leaving && !isOverlay && !isAudioOnly)
    }

    private func updateLiveEmploymentIfNeeded(timestamps: Timestamps?) {
        let oldValue = liveEmployment
        let newValue: LiveEmployment
        if isEmployed(in: .program) || isEmployed(in: .programSceneInput) {
            newValue = .active
        } else if isEmployed(in: .programLeavingOverlay) { // TODO: should be also for non-overlays which are leaving
            newValue = .leaving
        } else if isEmployed(in: .preview) || isEmployed(in: .previewSceneInput) || !preparationIntents.isEmpty {
            /*
             * When swaping between preview and program, for some very short time the channel can be fully unemployed.
             * This happens when the channel was live and goes on preview.
             * Using preparationIntents here allows us to consider the channel "suspended" during this short
             * unemployed time.
             */
            newValue = .suspended
        } else if let src = playedSource, src.hasAudio {
            // Set the state to leaving if the audio needs to fade out
            let currentAttachmentState = attachmentState.current
            if currentAttachmentState == .attached ||
                currentAttachmentState == .detaching ||
                currentAttachmentState == .detachingFromMixer {
                newValue = .leaving
            } else {
                newValue = .none
            }
        } else {
            newValue = .none
        }

        if newValue != oldValue {
            liveEmployment = newValue
            liveEmploymentDidChange(oldValue: oldValue, newValue: newValue, timestamps: timestamps)
        }
    }

    private var tallyState: MediaProviderTallyState {
        if isEmployed(in: .program) || isEmployed(in: .programSceneInput) || isEmployed(in: .programLeavingOverlay) {
            return .live
        } else if isEmployed(in: .preview) || isEmployed(in: .previewSceneInput) {
            return .preview
        } else {
            return .off
        }
    }

    private var previousTallyState: MediaProviderTallyState = .undefined

    private func applyTallyState(state: MediaProviderTallyState) {
        switch state {
        case .live:
            mediaProvider?.liveOn()
        case .preview:
            mediaProvider?.livePrev()
        default:
            mediaProvider?.liveOff()
        }
    }

    private func updateTallyIfNeeded() {
        // Not needed for played sources because it's handled in liveEmploymentDidChange
        if playedSource == nil {
            let newState = tallyState
            if newState != previousTallyState {
                previousTallyState = newState
                applyTallyState(state: newState)
            }
        }
    }

    var diagDescription: String {
        return "isVisible=\(isVisible) isLiveSource=\(camera != nil) isOverlay=\(isOverlay) isScene=\(isScene) " +
        "isEffect=\(isEffect) applyOnProgram=\(coreElement.applyOnProgram) isBound=\(isBound) " +
        "preparation=\(preparationState)"
    }

    // MARK: - Playback Progress

    private var autoDismissTimer: TimerDataSource?

    var progressWatcher: (any PlaybackProgressWatcher)? {
        if let playedMediaProvider = self.playedMediaProvider {
            return playedMediaProvider.progressWatcher
        } else {
            return timerProgressWatcher
        }
    }

    var fadeOutDuration: Double {
        if isAudioOnly {
            return 0
        } else {
            return Double(propHolder?.vPropHolder.duration ?? 0.0)
        }
    }

    var autoFadeOutWatcher: FadeOutWatcher? {
        if isAudioOnly || isOverlay {
            if let artwork = self.artwork,
               artwork.isAutoDismissEnabled,
               artwork.autoDismissCapability == .basedOnDelay || artwork.autoDismissCapability == .basedOnTimer {
                return FadeOutWatcher(playbackProgressWatcher: timerProgressWatcher, fadeOutDuration: fadeOutDuration)
            } else if let playedSource = self.playedSource,
                      let playedMediaProvider = playedMediaProvider {
                if playedSource.playbackMode == PLAYBACK_MODE_PLAY_ONCE &&
                    playedSource.isAutoDismissEnabled {
                    return FadeOutWatcher(
                        playbackProgressWatcher: playedMediaProvider.progressWatcher,
                        fadeOutDuration: fadeOutDuration)
                } else if playedSource.playbackMode == PLAYBACK_MODE_LOOP_COUNT {
                    return FadeOutWatcher(playbackProgressWatcher: playedMediaProvider.progressWatcher,
                                          fadeOutDuration: fadeOutDuration,
                                          mode: .loopCount(playedSource.loopCount))
                } else if playedSource.playbackMode == PLAYBACK_MODE_DISPLAY_DURATION {
                    return FadeOutWatcher(playbackProgressWatcher: playedMediaProvider.progressWatcher,
                                          fadeOutDuration: fadeOutDuration,
                                          mode: .customDuration(Double(playedSource.loopDuration)))
                }
            } else if let effect = self.effect,
                      effect.isAutoDismissEnabled {
                return FadeOutWatcher(playbackProgressWatcher: timerProgressWatcher, fadeOutDuration: fadeOutDuration)
            }
        }
        return nil
    }

    // MARK: - MISC

    private var isSourceEnabled = false
    private var isVideoEnabled = false

    @objc final private(set) var isRecording = false
    private var recStartTimeAvailable = false
    private var recMID: String?
    private var recConf = M2RecConf()

    private var onDetached: (() -> Void)?
    private var cameraPosition: src_conf_pos_t?
    private var isAudioPlaying = false // source is providing audio
    private var isAudioFadeOutInProgress = false
    private var appliedAudioActive = false
    private var appliedAudioGain: Float = 0.0
    private var appliedAudioRouting: AudioRouting?

    @objc private(set) var index = -1

    // "A" or "V"
    var type: String {
        if index >= M2ChannelAllocator.PCH_FROM && index < M2ChannelAllocator.PCH_TO {
            return "P"
        }
        if index >= M2ChannelAllocator.VCH_FROM && index < M2ChannelAllocator.VCH_TO {
            return "V"
        }
        if index >= M2ChannelAllocator.ACH_FROM && index < M2ChannelAllocator.ACH_TO {
            return "A"
        }
        return "X"
    }

    // 1, 2, 3, ...
    var number: Int {
        if index >= M2ChannelAllocator.PCH_FROM && index < M2ChannelAllocator.PCH_TO {
            return index - M2ChannelAllocator.PCH_FROM + 1
        }
        if index >= M2ChannelAllocator.VCH_FROM && index < M2ChannelAllocator.VCH_TO {
            return index - M2ChannelAllocator.VCH_FROM + 1
        }
        if index >= M2ChannelAllocator.ACH_FROM && index < M2ChannelAllocator.ACH_TO {
            return index - M2ChannelAllocator.ACH_FROM + 1
        }
        return 0
    }

    // "A1", "A2", .. or "V1", "V2", ...
    var _name: String {
        let number = self.number
        if number > 0 {
            return "\(type)\(number)"
        } else {
            return type
        }
    }

    private(set) var isSwitchingConf = false

    @objc var isAudioRef: Bool {
        return channelManager?.audioRef == self
    }

    var isRemote: Bool {
        return camera?.isRemote ?? false
    }

    private(set) var isRecStartTimeAvailable = false
    private(set) var recStartTime: Int64 = 0
    @objc private(set) var recPart = 0

    let attachmentType: M2ChannelAttachmentType

    // for played sources
    private(set) var isPlaying = false
    var _isPlaybackSavedInJournal = false
    private(set) var m2ref: Int64 = 0

    // internal
    private var attachmentState = DualityWrapper_M2ChannelAttachmentState(state: .detached)

    func _deinit() {
        channelManager = nil
    }

    private func attachment_runStateMachine() {
        guard attachmentState.current != attachmentState.target else {
            return
        }

        // Target can only be attached or detached.
        switch attachmentState.target {
        case .detached:
            if attachmentState.current == .attached || attachmentState.current == .error {
                startDetaching()
            }
        case .attached:
            if attachmentState.current == .detached {
                startAttaching()
            } else if attachmentState.current == .detaching {
                // Shortcut to re-attach a detaching channel without waiting for the fade out
                attachToMixer()
            }
        default:
            FatalLogger.shared.logFatalError()
        }
    }

    /**
     * Start attaching the channel to the audio and video mixers.
     */
    private func startAttaching() {
        guard attachmentState.current == .detached,
              let manager = self.channelManager else {
            return
        }
        attachmentState.current = .attaching

        assert(index < 0)

        var newIndex = manager.channelAllocator.allocIndexForChannel(
            type: attachmentType,
            audio: mediaProvider?.providesAudio ?? false,
            video: mediaProvider?.providesVideo ?? false)
        if newIndex < 0 {
            newIndex = -1
        }

        index = newIndex
        if let vmaker = coreElement.vPropHolder?.vMaker as? VMakerSource {
            let type = vmaker.type
            if type == .input || type == .overlaidInput {
                vmaker.videoSrcChannel = index
            }
        }

        camera?.angle = Int32(newIndex + 1)

        if newIndex < 0 {
            attachmentState.current = .error
            return
        }

        manager._cnl_array[newIndex] = self

        attachToMixer()
    }

    func attachToMixer() {
        guard attachmentState.current == .attaching || attachmentState.current == .detaching,
              let manager = self.channelManager  else {
            return
        }

        attachmentState.current = .attachingToMixer
        manager._enableSourceToMixer(channel: self) { [weak self] in
            guard let self,
                  attachmentState.current == .attachingToMixer else {
                return
            }

            self.isAudioPlaying = self.playedSource == nil
            self.attachmentState.current = .attached
            self.applyAudioStateChanges(manager.getTimestamps())
            self._didAttach()
        }
    }

    /**
     * Start detaching the channel from the audio and video mixers.
     */
    func startDetaching() {
        guard attachmentState.current == .attached || attachmentState.current == .error,
              let manager = self.channelManager else {
            return
        }
        attachmentState.current = .detaching

        isAudioPlaying = false
        applyAudioStateChanges(manager.getTimestamps())
        if !isAudioFadeOutInProgress {
            detachFromMixer()
        }
        // else: detachFromMixer() called by fade out end notification
    }

    func detachFromMixer() {
        guard attachmentState.current == .detaching,
              let manager = self.channelManager else {
            return
        }

        attachmentState.current = .detachingFromMixer
        manager._disableSource(toMixer: self) { [weak self] in
            guard let self,
                  attachmentState.current == .detachingFromMixer else {
                return
            }

            /*
             If the channel has an error or does not need an index the value will be negative,
             therefore a false index and crash
             */
            if self.index != -1 {
                manager.channelAllocator.freeIndex(self.index)
                if manager.channelAllocator.assignedChannelCount == 0 {
                    manager._onNoMoreAttachedChannels()
                }
                manager._cnl_array[self.index] = nil
                self.index = -1
            }

            if let vmaker = self.coreElement.vPropHolder?.vMaker as? VMakerSource {
                let type = vmaker.type
                if type == .input || type == .overlaidInput {
                    vmaker.videoSrcChannel = self.index
                }
            }
            self.camera?.angle = 0
            if self.onDetached != nil {
                let block = self.onDetached
                self.onDetached = nil
                block?()
            }
            if liveEmployment == .leaving {
                self.updateLiveEmploymentIfNeeded(timestamps: nil)
            }
            self.attachmentState.current = .detached
            self._didDetach()
        }
    }

    func _finalDetach(_ completion: @escaping () -> Void) {
        onDetached = completion
        attachmentState.target = .detached
        attachment_runStateMachine()

        // Force to detach without waiting for the fade out
        if attachmentState.current == .detaching {
            detachFromMixer()
        }
    }

    private func liveEmploymentDidChange(oldValue: LiveEmployment,
                                         newValue: LiveEmployment,
                                         timestamps: Timestamps?) {

        if let src = playedSource {

            // If no timestamp was provided, take the current one
            guard let timestamps = timestamps ?? channelManager?.getTimestamps() else {
                return
            }

            // media track

            if newValue == .active {
                let url = src.playedMediaUrl

                let start = src.playtimeStart
                let start_us = Int64(round(start * 1000000.0))

                if !isPlaying {
                    isPlaying = true

                    m2ref = timestamps.refts - start_us

                    // Start playing
                    mediaProvider?.liveOn()

                    if let mid = MediaUtil.mid(url: url) {
                        AssetStats.shared.reportAssetInsertion(mid: mid, audio: src.hasAudio, video: src.hasVideo)
                    }
                }

                if let journalRecorder = channelManager?.journalRecorder, !_isPlaybackSavedInJournal {
                    _isPlaybackSavedInJournal = true

                    if let jwriter = journalRecorder.jwriter {
                        jwriter.writePrerecordedMediaStart(
                            channel: index,
                            mts: start_us,
                            refts: timestamps.refts,
                            src: src)
                    }
                }
            }

            if newValue == .none || newValue == .suspended {
                if isPlaying {
                    isPlaying = false
                    _isPlaybackSavedInJournal = false

                    // Stop playing
                    mediaProvider?.liveOff()
                }
            }

            // audio

            let oldIsAudioPlaying = isAudioPlaying
            isAudioPlaying = newValue != .none && newValue != .suspended

            if isAudioPlaying != oldIsAudioPlaying && attachmentState.current == .attached {
                applyAudioStateChanges(timestamps)
            }
        }

        // timer

        let timerShouldRun = newValue != .none && newValue != .suspended
        let oldTimerShouldRun = oldValue != .none && oldValue != .suspended

        if timerShouldRun && !oldTimerShouldRun {
            for timer in timers {
                timer.start()
            }
            autoDismissTimer?.start()

            if ((timers.count == 1 && timers[0].getLoop() == false) || autoDismissTimer != nil) &&
                !timerProgressWatcher.isVisible {
                if autoDismissTimer == nil,
                   let mainMixer,
                   let channelId = timerDataChannelIds.first {
                    mainMixer.dataHub.addOutput(to: timerProgressWatcher, channelId: channelId)
                }
                timerProgressWatcher.isVisible = true
            }
        }
        if !timerShouldRun && oldTimerShouldRun {
            for timer in timers {
                timer.stop()
            }
            autoDismissTimer?.stop()
        }

        let progressShouldBeVisible = newValue != .none
        if !progressShouldBeVisible && timerProgressWatcher.isVisible {
            timerProgressWatcher.isVisible = false
            if autoDismissTimer == nil,
               let channelId = timerDataChannelIds.first,
               let mainMixer {
                mainMixer.dataHub.removeOutput(to: timerProgressWatcher, channelId: channelId)
            }
        }
    }

    func enableSource() {
        if !isSourceEnabled {
            isSourceEnabled = true
            camera?.enableSource()
        }
        startVideo()
    }

    func disableSource() {
        stopVideo()
        if isSourceEnabled {
            isSourceEnabled = false
            camera?.disableSource()
        }
    }

    /**
     * Start fade out if audio is playing and a fade out duration value has been set
     */
    func startAudioFadeOutIfNeeded() {
        guard let manager = self.channelManager else {
            return
        }

        if isAudioPlaying && playedSource?.audioFadeOutDuration != nil {
            isAudioPlaying = false
            applyAudioStateChanges(manager.getTimestamps())
        }
    }

    private var isAudioUnmuted: Bool {
        return !isMuted
    }

    // defined by the user (what user want)
    @objc final var isMuted: Bool = true {
        didSet {
            if isMuted != oldValue {
                if attachmentState.current == .attached {
                    applyAudioStateChanges(channelManager?.getTimestamps())
                }

                channelManager!.delegates.fireChannelAudioStateDidChange([self])
            }
        }
    }

    @objc final var audioGain: Float = 1.0 {
        didSet {
            if audioGain != oldValue {
                if attachmentState.current == .attached {
                    applyAudioStateChanges(channelManager?.getTimestamps())
                }

                channelManager!.delegates.fireChannelAudioStateDidChange([self])
            }
        }
    }

    @objc final var audioRouting: AudioRouting = .none {
        didSet {
            if audioRouting != oldValue {
                if attachmentState.current == .attached {
                    applyAudioStateChanges(channelManager?.getTimestamps())
                }

                channelManager!.delegates.fireChannelAudioStateDidChange([self])
            }
        }
    }

    func applyAudioStateChanges(_ timestamps: Timestamps?) {
        guard playedSource?.hasAudio == true || camera?.providesAudio == true else {
            return
        }

        let currentAttachmentState = attachmentState.current

        assert(currentAttachmentState == .attached || currentAttachmentState == .detaching)

        let active = isAudioUnmuted && isAudioPlaying

        let activeStateChange = active != appliedAudioActive
        let gainChange = (audioGain != appliedAudioGain) || (activeStateChange && active)
        let routingChange = (audioRouting != appliedAudioRouting) || (activeStateChange && active)

        // Only apply changes if the channel is or was active and something changed
        if (active || appliedAudioActive) &&
            (activeStateChange || gainChange || routingChange) {
            appliedAudioActive = active
            appliedAudioGain = audioGain
            appliedAudioRouting = audioRouting

            if activeStateChange {
                let fadeDuration = (active ? playedSource?.audioFadeInDuration : playedSource?.audioFadeOutDuration) ??
                StoredElementData.MediaConf.defaultAudioFadeDuration
                channelManager!._setChannel(self, muted: !active, fadeDuration: isLivePaused ? 0.0 : fadeDuration)
                isAudioFadeOutInProgress = !active
            }

            if gainChange || routingChange {
                channelManager!._setChannel(self, gain: audioGain, routing: audioRouting)
            }

            if let channelManager,
               let jwriter = channelManager.journalRecorder?.jwriter,
               index >= 0 {
                let audioSwitch = AudioSwitch(channel: index)

                if activeStateChange {
                    audioSwitch.mute = !active
                }
                if gainChange {
                    audioSwitch.gain = audioGain
                }
                if routingChange {
                    audioSwitch.routing = audioRouting
                }
                // If no timestamp was provided, take the current one
                let timestamps = timestamps ?? channelManager.getTimestamps()
                jwriter.writeViewSwitch(JournalSwitch(
                    refts: timestamps.refts,
                    filterTree: nil,
                    transition: nil,
                    audioSwitches: [audioSwitch]))
            }
        }
    }

    var _isAudioActive: Bool {
        return appliedAudioActive
    }

    func startVideo() {
        if isVideoEnabled {
            return
        }
        if !(mediaProvider?.providesVideo ?? false) {
            return
        }

        if isSwitchingConf {
            isVideoEnabled = true
            return
        }

        camera?.startVideo()
        isVideoEnabled = true
    }

    func stopVideo() {
        if !isVideoEnabled {
            return
        }

        if isSwitchingConf {
            isVideoEnabled = false
            return
        }

        camera?.stopVideo()
        isVideoEnabled = false
    }

    func _startRec() -> Bool {
        guard let camera = self.camera else {
            return false
        }
        if !camera.supportsRecording {
            return false
        }
        guard let name = self.channelManager!._recName else {
            return false
        }
        guard let gid = self.channelManager!._recGID else {
            return false
        }
        let part = self.channelManager!._recPart(forChannel: index)

        var cname: String
        if camera.providesVideo {
            cname = MediaUtil.localizedTextForAngle
        } else if camera.providesAudio {
            cname = MediaUtil.localizedTextForAudioChannel
        } else {
            cname = "Channel"
        }
        var mid = String(format: "%@-%02x", gid, index + 1)
        var mname = "\(name) \(cname) \(index + 1)"
        if part != 0 {
            mid = String(format: "%@-%02x", mid, part + 1)
            mname = "\(mname) \(MediaUtil.localizedTextForPart) \(part + 1)"
        }

        recMID = mid
        recStartTimeAvailable = false
        recPart = part

        weak var weak_me = self

        var conf = recConf.rec_conf
        if camera.providesVideo && recConf.options.contains(.applyRecProfileBitRate) {
            let frameRate = camera.conf!.pointee.v_frame_rate
            let frameSize = camera.frameSize
            conf.v_bit_rate = RecProfile.shared.cameraBitRate(for: frameSize, frameRate: frameRate, encoding: .AVC)
            conf.v_bit_rate_scale_with_encoding = (RecProfile.shared.cameraVideoBitRateScaleMode != .none)
            if conf.v_bit_rate > 0 {
                print("Channel \(index): custom video rec bit rate: \(conf.v_bit_rate)")
            }
        }

        camera.startRec(withName: mname, mid: mid, gid: gid, conf: &conf) { mid, mts in
            if let me = weak_me, me.recMID == mid, !me.recStartTimeAvailable {
                me.recStartTimeAvailable = true
                me.recStartTime = mts

                me._saveRecStartTime()

                var m2os: Int32 = 0
                camera.m2os(&m2os)
                let refts32 = Int32(truncatingIfNeeded: mts) &+ m2os
                var refts = gmu_mtime_us()
                let delta = Int32(truncatingIfNeeded: refts) &- refts32
                refts -= Int64(delta)

                if let manager = me.channelManager {
                    manager.delegates.fireChannelDidStartRecording(me,
                                                                   mid: mid,
                                                                   name: mname,
                                                                   refTime: refts,
                                                                   mediaTime: mts)
                }
            }
        }

        return true
    }

    func _stopRec() -> Bool {
        guard let camera = self.camera else {
            return false
        }

        camera.stopRec()

        channelManager!.delegates.fireChannelDidStopRecording(self)

        return true
    }

    func _saveRecStartTime() {
        if recStartTimeAvailable, let recMID = recMID, let manager = self.channelManager, let camera = self.camera,
           let jw = manager.journalRecorder?.jwriter {
            jw.writeLiveMediaStart(
                angle: index + 1,
                hostName: camera.hostName ?? "",
                srcName: camera.sourceName ?? "",
                mid: recMID,
                mts: recStartTime,
                hasAudio: camera.providesAudio,
                hasVideo: camera.providesVideo,
                isAudioRef: isAudioRef)
            manager.pollTimeRefsSoon()
        }
    }

    func startRec(conf: M2RecConf) {
        if isRecording {
            return
        }

        recConf = conf

        if isSwitchingConf {
            isRecording = true
            return
        }
        if _startRec() {
            isRecording = true
        }
    }

    func stopRec() {
        if !isRecording {
            return
        }
        if isSwitchingConf {
            isRecording = false
            return
        }
        if _stopRec() {
            isRecording = false
        }
    }

    func saveRecStartTime() {
        guard self.camera != nil else {
            return
        }

        _saveRecStartTime()

        if recStartTimeAvailable, let recMID = recMID {
            channelManager!.delegates.fireChannelDidContinueRecording(
                self,
                mid: recMID,
                startRefTime: 0,
                startMediaTime: recStartTime)
        }
    }

    @objc func becomeAudioRef() {
        if channelManager!.journalRecorder?.jwriter != nil {
            return // not yet supported
        }
        guard let camera = self.camera else {
            return
        }
        if !camera.providesAudio {
            return
        }
        if channelManager!.audioRef == self {
            return
        }

        channelManager!.audioRef = self

        channelManager!.delegates.fireCameraDidBecomeAudioRef(self)
    }

    @objc func assignMediaPublishing(_ pubId: String, toMedia mid: String) {
        camera?.assignMediaPublishing(pubId, toMedia: mid)
    }

    @objc func setMediaPublishing(_ pubId: String, properties props: [String: String]) {
        for (key, value) in props {
            camera?.setMediaPublishing(pubId, propertyName: key, value: value)
        }
    }

    @objc func startMediaPublishing(_ pubId: String) {
        camera?.startMediaPublishing(pubId)
    }

    @objc func stopMediaPublishing(_ pubId: String) {
        camera?.stopMediaPublishing(pubId)
    }

    @objc func refreshMediaPublishingState(_ pubId: String) {
        camera?.refreshMediaPublishingState(pubId)
    }

    func getThumbnail() -> UIImage? {
        // Only capture image if source logging is initialized to avoid wasted work
        guard mainMixer?.sourcePanelLogger.isInitialized == true else {
            return nil
        }

        // Image capture is non-critical and should never interrupt production
        do {
            // For played sources with existing thumbnails
            if let playedSource = self.playedSource {
                return playedSource.thumbnail?.readImage()
            }

            // For artwork/effects with existing thumbnails
            if let artwork = self.artwork {
                return artwork.thumbnail?.readImage()
            }

            if let effect = self.effect {
                return effect.thumbnail?.readImage()
            }

            // For live sources, capture actual frame
            if let camera = self.camera {
                return captureLiveCameraFrame()
            }

            return nil

        } catch {
            // Log non-fatal error but don't interrupt production
            let logger = LsLogger(subsystem: "swi.mixer", category: "M2Channel")
            logger.warning("Non-critical capture failed for channel: \(id): \(error.localizedDescription)")

            return nil
        }
    }

    private func captureLiveCameraFrame() -> UIImage? {
        guard let mainMixer = self.mainMixer,
              let vContext = mainMixer.channelManager.vContext as? VContext,
              index >= 0 else {
            return nil
        }

        // Check if provider has active sinks (video is flowing)
        let hadSinks = vContext.providerIsUsedByASink(index)

        if !hadSinks {
            // No sinks connected - no video is flowing, skip thumbnail capture
            print("M2Channel: Skipping thumbnail capture - no active video sinks for channel \(index)")
            return nil
        }

        // Render the camera frame to capture thumbnail
        return renderCameraFrame(vContext: vContext)
    }

    private func renderCameraFrame(vContext: VContext) -> UIImage? {
        // Create a temporary VOutputPump to render the camera source
        let pump = VOutputPump(vContext: vContext)

        // Use the global broadcast profile aspect ratio
        let globalAspectRatio = OutputProfile.shared.targetAspectRatio.floatValue

        // Set thumbnail size based on global aspect ratio
        let thumbnailWidth: Int
        let thumbnailHeight: Int

        if globalAspectRatio > 1.0 {
            // Landscape aspect ratio (16:9, etc.)
            thumbnailWidth = 960
            thumbnailHeight = Int(Float(thumbnailWidth) / globalAspectRatio)
        } else {
            // Portrait aspect ratio (9:16, etc.)
            thumbnailHeight = 960
            thumbnailWidth = Int(Float(thumbnailHeight) * globalAspectRatio)
        }

        pump.setFrameSize(width: thumbnailWidth, height: thumbnailHeight)
        pump.frameAspectRatio = globalAspectRatio

        print("M2Channel: Capturing thumbnail with global aspect ratio \(globalAspectRatio) (\(thumbnailWidth)x\(thumbnailHeight))")

        // Create a source filter for this camera
        let sourceFilter = VFilterSource(vOut: pump)

        // Create a minimal prop bundle with default values to prevent crashes
        let filterDef = VFilterSourceDef()
        let bundleDef = VPropBundleDef(name: "source", propDefs: filterDef.propDefs, imagePropDefs: filterDef.imagePropDefs)
        let propBundle = VPropBundle(bundleDef: bundleDef, targetAspectRatio: OutputProfile.shared.targetAspectRatio)
        sourceFilter.setProps(props: propBundle)

        sourceFilter.setVideoSource(index)
        pump.setInput(filter: sourceFilter)

        // Create a FIFO to capture the rendered frame
        let fifo = mflow_vfifo_create(1)!
        let fifoPushable = MFlowVPushable(
            mflow_vpushable: &fifo.pointee.z,
            transfer: false
        )
        pump.addOutput(pushable: fifoPushable)

        // Render one frame
        pump.manualBegin()
        pump.manualAction(ts: 0)

        // Extract the frame and convert to UIImage
        var capturedImage: UIImage?
        if let cvImage = fifo.pointee._frame.cvPixelBufferRef?.takeUnretainedValue() {
            capturedImage = VideoFrameUtil.frameToUIImage(cvImage)
        }

        // Cleanup
        pump.manualEnd()
        pump.removeInput()
        fifo.pointee.z.z.release()

        return capturedImage
    }




}

extension M2Channel: CameraConfSwitchDelegate {
    public func cameraWillSwitchConf(_ camera: Camera) {
        self.cameraPosition = camera.cameraPosition
        if self.isRecording {
            _ = self._stopRec()
        }
        if self.isVideoEnabled {
            camera.stopVideo()
        }
        self.isSwitchingConf = true
        self.channelManager?.delegates.fireCameraConfStateDidChange(self)
    }

    public func cameraDidSwitchConf(_ camera: Camera) {
        if isVideoEnabled {
            if camera.providesVideo {
                camera.startVideo()
            } else {
                isVideoEnabled = false
            }
        }
        if isRecording {
            isRecording = _startRec()
        }
        isSwitchingConf = false
        channelManager?.delegates.fireCameraConfStateDidChange(self)
        if cameraPosition != camera.cameraPosition {
            cameraPosition = camera.cameraPosition
            channelManager?.delegates.fireCameraDidSwap(self)
        }
    }
}

extension M2Channel: CameraResourceDelegate {
    nonisolated public func storageSpaceDidChange(_ sender: Camera) {
        Task { @MainActor in
            channelManager?.delegates.fireChannelStorageSpaceDidChange(self)
        }
    }

    nonisolated public func batteryStateDidChange(_ sender: Camera) {
        Task { @MainActor in
            channelManager?.delegates.fireChannelBatteryStateDidChange(self)
        }
    }
}

extension M2Channel: MediaPublishingDelegate {
    nonisolated public func mediaPulishing(_ pubId: String,
                                           didRefreshState state: Int32,
                                           result: Int32,
                                           progress: Float) {
        Task { @MainActor in
            channelManager?.delegates.fireChannelMediaPublishingDidRefreshState(
                self,
                pubId: pubId,
                state: state,
                result: result,
                progress: progress)
        }
    }
}

private enum PreparationState {
    case unprepared
    case attaching
    case loading
    case ready
    case failed
}

/**
 * This is an array which index of type M2Domain and values of type Int
 */
struct DomainToIntMap {
    var values = [Int](repeating: 0, count: M2Domain.allCases.map({ $0.rawValue }).max()! + 1)

    subscript(index: M2Domain) -> Int {
        get { self.values[index.rawValue] }
        set { self.values[index.rawValue] = newValue }
    }
}
