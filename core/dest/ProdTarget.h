//
//  ProdTarget.h
//  Cap-iOS
//
//  Created by <PERSON><PERSON> on August 25, 2015.
//  Copyright (c) 2015 Switcher Inc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "RLSourceRecorder.h"
#import "ProdError.h"


/**
 * When the status switch to kProdTargetStateRequestingAction, the client
 * should ask what to do by reading actionRequest. Then, he has to call
 * performAction by giving one of the actions present in actionChoice.
 * When called, performAction will switch state to kProdTargetStateProcessingAction
 * or to an other state, but cannot stay in kProdTargetStateRequestingAction.
 * This means that only one action can be requested at a time and if a second request
 * is pending, we should finrst switch to another state before go back to
 * kProdTargetStateRequestingAction again. This other state can typically be
 * kProdTargetStateProcessingAction.
 */
typedef NS_ENUM(NSInteger, ProdTargetState) {
    kProdTargetStateIdle,
    kProdTargetStateRunning,
    kProdTargetStateFinishing,
    kProdTargetStateRequestingAction,
    kProdTargetStateProcessingAction,
};

NSString *prodTargetStateName(ProdTargetState state);

typedef NS_ENUM(NSInteger, ProdTargetActionRequest) {
//    ProdTargetActionRequestNone = 0,
    kProdTargetActionRequestErrorNotification = 1,
};

typedef NS_OPTIONS(NSInteger, ProdTargetActionMask) {
    kProdTargetActionMaskContinue = 1,
    kProdTargetActionMaskStop = 2,
};

@class ProdTarget;

@protocol ProdTargetDelegate <NSObject>
- (void)prodTargetStateDidChange:(ProdTarget *)target;
- (void)prodTargetStartTimeBecameAvailable:(ProdTarget *)target; // fired only by recorders
- (void)prodTargetGoodStart:(ProdTarget *)target; // fired only by streamers
@end

@protocol ProdTargetProtocol;
@protocol ProdRecorderProtocol;
@protocol ProdStreamerProtocol;
@protocol ProdLocalStreamerProtocol;

@interface ProdTarget : NSObject<ProdTargetProtocol> {
    __weak id<ProdTargetDelegate> _delegate;
    ProdTargetState _state;
    ProdTargetActionRequest _actionRequest;
    ProdTargetActionMask _actionChoice;
    ProdError *_actionError;
}

@property (nonatomic, weak) id<ProdTargetDelegate> delegate;
@property (nonatomic, readonly) ProdTargetState state;
@property (nonatomic, readonly) ProdTargetActionRequest actionRequest;
@property (nonatomic, readonly) ProdTargetActionMask actionChoice;
@property (nonatomic, readonly) ProdError *actionError;

- (void)_setState:(ProdTargetState)state;

- (void)start;
- (void)stop;
- (void)performAction:(ProdTargetActionMask)action NS_SWIFT_NAME(perform(action:));
- (void)dumpState;

@end


@interface ProdRecorder : ProdTarget<ProdRecorderProtocol>
@property (nonatomic, readonly) BOOL isStartTimeAvailable;
@property (nonatomic, readonly) int64_t startTime; // media time in us
@end


@interface ProdStreamer : ProdTarget<ProdStreamerProtocol>

@property (nonatomic, readonly) int progressTime; // in seconds, -1 = not available
@property (nonatomic) BOOL consumeSource; // set when the source is removed as soon as streamed

@end


@interface ProdLocalStreamer : ProdStreamer<ProdLocalStreamerProtocol>

@property (nonatomic) NSString * _Nullable filePath;

/**
 * Should be set only is the given recorder is recording.
 */
@property (nonatomic) id<RLGrowingSource> _Nullable recorder;

@end
