//
//  ProgramRecorder.m
//  Switcher
//
//  Created by <PERSON><PERSON> on September 3, 2013.
//  Copyright (c) 2013 Switcher Inc. All rights reserved.
//

#import "InternalSwift.h"
#include <gmutil/gmutil.h>
#import "ProgramRecorder.h"
#include "prod_cap.h"
#include <mtech/qtf/qtf_writer.h>
#include <mtech/qtf/qtf_input_avc.h>
#include <mtech/qtf/qtf_input_aac.h>
#include <mtech/mflow/mflow_venc_h264.h>
#include <mtech/mflow/mflow_aenc_aac.h>
#include <mtech/mio/mio_mwriter.h>
#include <mtech/mflow/mflow_pcm_relay.h>


@interface ProgramRecorder ()
- (void)recordingDidStartWithTime:(int64_t)time;
- (void)recordingHasPermanentError;
@end


#if 0

static void print_audio_basic_description(const AudioStreamBasicDescription *desc)
{
    printf("mSampleRate=%g\n",       (double)desc->mSampleRate);
    printf("mFormatID=%d\n",         (int)desc->mFormatID);
    printf("mFormatFlags=%d\n",      (int)desc->mFormatFlags);
    printf("mBytesPerPacket=%d\n",   (int)desc->mBytesPerPacket);
    printf("mFramesPerPacket=%d\n",  (int)desc->mFramesPerPacket);
    printf("mBytesPerFrame=%d\n",    (int)desc->mBytesPerFrame);
    printf("mChannelsPerFrame=%d\n", (int)desc->mChannelsPerFrame);
    printf("mBitsPerChannel=%d\n",   (int)desc->mBitsPerChannel);
}

static void print_audio_channel_layout(const AudioChannelLayout *layout)
{
    if (layout == NULL) {
        printf("audioChannelLayout=NULL\n");
    } else {
        printf("mChannelLayoutTag=%d\n",          (int)layout->mChannelLayoutTag);
        printf("mChannelBitmap=%d\n",             (int)layout->mChannelBitmap);
        printf("mNumberChannelDescriptions=%d\n", (int)layout->mNumberChannelDescriptions);

        for (int i=0; i<layout->mNumberChannelDescriptions; i++) {
            const AudioChannelDescription *desc = &layout->mChannelDescriptions[i];
            printf("mChannelDescriptions[%d].mChannelLabel=%d\n", i, (int)desc->mChannelLabel);
            printf("mChannelDescriptions[%d].mChannelFlags=%d\n", i, (int)desc->mChannelFlags);
            printf("mChannelDescriptions[%d].mCoordinates={%g, %g, %g}\n", i,
                   (double)desc->mCoordinates[0], (double)desc->mCoordinates[1], (double)desc->mCoordinates[2]);
        }
    }
}

#endif

static void *_retain_block(void (^block)(void))
{
    return (__bridge_retained void *)[block copy];
}

static void _exec_block(void *ctx)
{
    dispatch_block_t block = (__bridge_transfer dispatch_block_t)ctx;
    block();
}

static void _start_time_listener(void *ctx, int64_t time)
{
    ProgramRecorder *me = (__bridge ProgramRecorder *)ctx;
    dispatch_async(dispatch_get_main_queue(), ^{
        [me recordingDidStartWithTime:time];
    });
}

static void _error_listener(void *ctx)
{
    ProgramRecorder *me = (__bridge ProgramRecorder *)ctx;
    dispatch_async(dispatch_get_main_queue(), ^{
        [me recordingHasPermanentError];
    });
}

@implementation ProgramRecorder {
    NSString *_tmpFilePath;
    NSString *_filePath;
    MIO_MWRITER *_mio_writer;
    int _recState; // 1 = waiting first audio sample, 2 = waiting first video sample, 0 = running
    NSString *_gid;
    MFlowVRelay *_vRelay;
    MFLOW_PCM_RELAY *_arelay;
    MFlowVPushable *_vPushable;
    MFLOW_PCM_PUSHABLE *_apushable;

    // Live transcription
    LiveTranscriptionService *_transcription_service;
    MFLOW_PCM_PUSHABLE *_transcription_apushable;

    // for qtf recording
    QTF_WRITER *_qtf_writer;
    QTF_INPUT_AVC * _qtf_input_avc;
    QTF_INPUT_AAC * _qtf_input_aac;

    dispatch_queue_t _video_enc_queue;
    dispatch_queue_t _audio_enc_queue;
    dispatch_queue_t _rec_queue;

    BOOL _growthStopping;
    BOOL _growthStopped;
    __weak id<RLGrowingSourceDelegate> _growthDelegate;
    MediaListTransaction *_mediaListTransaction;
}

@synthesize isGrowingSourceStopping = _growthStopping;
@synthesize isGrowingSourceStopped = _growthStopped;
@synthesize growingSourceDelegate = _growthDelegate;
@synthesize isStartTimeAvailable = _isStartTimeAvailable;
@synthesize startTime = _startTime;

- (NSString*)broadcastId
{
    return _broadcastId;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
    }
    return self;
}

- (void)dealloc
{
    assert(!_mediaListTransaction);
}

/**
 * Both name and gid can be NULL.
 */
- (void)start
{
    if (_state != kProdTargetStateIdle)
        return;

    _isStartTimeAvailable = NO;

    if (!_name)
        _name = [MediaUtil createRecordingName: false];

    if (_recConf.format == REC_CONF_FORMAT_SEG) {

        src_conf_size_t size = { 0 };
        float fragment_interval = 30;
        float key_frame_interval = 2;
        int videoBitrate = 0;
        int audioBitrate = 0;
        int audioSampleRate = 48000;
        bool audioStereo = true;

        if (_bcProfile) {
            NSNumber *n;

            n = _bcProfile.videoKeyFrameIntervalNumber;
            key_frame_interval = n.floatValue;

            if (key_frame_interval > 4.0f)
                fragment_interval = key_frame_interval;
            else if (key_frame_interval > 2.0f)
                fragment_interval = key_frame_interval * 2.0f;
            else
                fragment_interval = 6.0f;

            n = _bcProfile.audioBitRateNumber;
            audioBitrate = n.intValue;

            n = _bcProfile.audioSampleRateNumber;
            audioSampleRate = n.intValue;

            n = _bcProfile.audioChannelCountNumber;
            audioStereo = n.intValue >= 2;

            n = _bcProfile.videoBitRateNumber;
            videoBitrate = n.intValue;

            n = _bcProfile.videoFrameWidthNumber;
            size.width = n.intValue;
            n = _bcProfile.videoFrameHeightNumber;
            size.height = n.intValue;
        } else {
            BCProfileLibrary *lib = BCProfileLibrary.sharedInstance;
            size = lib.defaultProgramFrameSize;
        }
        size = [_outputProfile sizeConformingToTargetAspectRatio:size];

        _video_enc_queue = dispatch_queue_create("AVRecorder video enc", NULL);
        _audio_enc_queue = dispatch_queue_create("AVRecorder audio enc", NULL);
        _rec_queue = dispatch_queue_create("AVRecorder file write", NULL);

        _qtf_writer = qtf_writer_create();
        qtf_writer_set_mode(_qtf_writer, 1);
        qtf_writer_set_start_time_listener(_qtf_writer, (__bridge void *)self, _start_time_listener);

        // video

        MFLOW_VENC_H264_PARAMS venc_params = {
            .input_width = size.width,
            .input_height = size.height,
            .output_width = size.width,
            .output_height = size.height,
            .bitrate = videoBitrate,
            .max_num_reorder_frames = 0, // currently the segmented format recorder doesn't manage 'cslg' atoms
            .max_key_frame_interval_duration = key_frame_interval,
        };

        MFLOW_VENC_H264 *venc = mflow_venc_h264_create(&venc_params);

        _qtf_input_avc = qtf_input_avc_create();
        qtf_input_set_time_scale(&_qtf_input_avc->z, 600);

        MFLOW_NALU_PUSHABLE *nalu_pusher = qtf_input_avc_create_nalu_pushable(_qtf_input_avc);
        mflow_venc_h264_set_output(venc, nalu_pusher, _rec_queue);
        c3_release(&nalu_pusher->z);

        qtf_writer_add_input(_qtf_writer, &_qtf_input_avc->z);

        _vPushable = [[MFlowVPushable alloc]
                      initWithMflow_vpushable:&venc->z.z
                      transfer:true];

        _vRelay = [[MFlowVRelay alloc] initWithOutput:_vPushable queue:_rec_queue];

        // audio

        if (audioSampleRate != _audioMixer.sampleRate)
            printf("ProgramRecorder: WARNING: bad sample rate: %d\n", audioSampleRate);

        MFLOW_AENC_AAC_PARAMS params = {
            .bit_rate = audioBitrate,
            .sample_rate = (int)_audioMixer.sampleRate,
            .stereo = audioStereo,
        };

        MFLOW_AENC_AAC *aenc = mflow_aenc_aac_create(&params);

        _qtf_input_aac = qtf_input_aac_create();

        MFLOW_AAC_PUSHABLE *aac_pushable = qtf_input_aac_create_pushable(_qtf_input_aac);
        mflow_aenc_aac_set_output(aenc, aac_pushable, _rec_queue);
        c3_release(&aac_pushable->z);

        qtf_writer_add_input(_qtf_writer, &_qtf_input_aac->z);

        _arelay = mflow_pcm_relay_create(_audio_enc_queue, &aenc->z.z);

        _apushable = &aenc->z.z;

        // metadata

        qtf_writer_put_metadata_utf8_str(_qtf_writer,
                                         AVMetadataQuickTimeMetadataKeySoftware.UTF8String,
                                         "RecoLive");
        qtf_writer_put_metadata_utf8_str(_qtf_writer,
                                         "com.recolive.media-type",
                                         "live");
        if (_mid) {
            qtf_writer_put_metadata_utf8_str(_qtf_writer,
                                             "com.recolive.media-id",
                                             _mid.UTF8String);
        }
        if (_gid) {
            qtf_writer_put_metadata_utf8_str(_qtf_writer,
                                             "com.recolive.group-id",
                                             _gid.UTF8String);
        }

        // open file

        NSString *fileName = [NSString stringWithFormat:@"%@ %@.mmvideo", _name, MediaUtil.localizedTextForLive];
        NSURL *url = [MediaList.sharedInstance.mediaDirUrl URLByAppendingPathComponent:fileName];
        [NSFileManager.defaultManager removeItemAtURL:url error:nil];
        _filePath = url.path;

        assert(!_mediaListTransaction);
        _mediaListTransaction = [MediaList.sharedInstance addFileWithPath:_filePath];

        int rv = qtf_writer_open(_qtf_writer, url.fileSystemRepresentation);
        if (rv < 0) {
#ifdef DEBUG
            GMU_FATAL(rv, "cannot open output file %s", url.fileSystemRepresentation);
#endif
        }

        [_vmixerOutput addOutputWithPushable:_vRelay];

        [_audioMixer enableProgramOutput:&_arelay->z];

    } else {

        src_conf_size_t size = { 0 };

        NSString *fileName = [NSString stringWithFormat:@"%@ %@.mov", _name, MediaUtil.localizedTextForLive];
        NSURL *url = [MediaList.sharedInstance.mediaDirUrl URLByAppendingPathComponent:fileName];
        [NSFileManager.defaultManager removeItemAtURL:url error:nil];
        _filePath = url.path;

        _mio_writer = mio_mwriter_create(url.fileSystemRepresentation);
        _mio_writer->set_realtime(_mio_writer, true);
        _mio_writer->set_video_enabled(_mio_writer, true);
        _mio_writer->set_audio_enabled(_mio_writer, true);
        _mio_writer->set_audio_encoding(_mio_writer, MIO_MWRITER_AUDIO_ENC_AAC);

        _mio_writer->put_metadata_utf8_str(_mio_writer, "com.recolive.media-type", "live");
        if (_mid)
            _mio_writer->put_metadata_utf8_str(_mio_writer, "com.recolive.media-id", _mid.UTF8String);
        if (_gid)
            _mio_writer->put_metadata_utf8_str(_mio_writer, "com.recolive.group-id", _gid.UTF8String);

        if (_bcProfile) {
            NSNumber *n;

            n = _bcProfile.videoKeyFrameIntervalNumber;
            float key_frame_interval = n.floatValue;

            float fragment_interval;
            if (key_frame_interval > 4.0f)
                fragment_interval = key_frame_interval;
            else if (key_frame_interval > 2.0f)
                fragment_interval = key_frame_interval * 2.0f;
            else
                fragment_interval = 6.0f;

            _mio_writer->set_fragment_interval(_mio_writer, fragment_interval);     // in seconds
            _mio_writer->set_video_gop_duration(_mio_writer, key_frame_interval);   // in seconds

            n = _bcProfile.audioBitRateNumber;
            int audioBitrate = n.intValue;
            _mio_writer->set_audio_bitrate(_mio_writer, audioBitrate);

            n = _bcProfile.videoBitRateNumber;
            int videoBitrate = n.intValue;
            _mio_writer->set_video_bitrate(_mio_writer, videoBitrate);

            n = _bcProfile.audioSampleRateNumber;
            int audioSampleRate = n.intValue;
            _mio_writer->set_audio_sample_rate(_mio_writer, audioSampleRate);

            n = _bcProfile.audioChannelCountNumber;
            int audioChannelCount = n.intValue;
            _mio_writer->set_audio_stereo(_mio_writer, audioChannelCount > 1);

            n = _bcProfile.videoFrameWidthNumber;
            size.width = n.intValue;
            n = _bcProfile.videoFrameHeightNumber;
            size.height = n.intValue;
            size = [_outputProfile sizeConformingToTargetAspectRatio:size];
            _mio_writer->set_frame_size(_mio_writer, size.width, size.height);

            // special workaround for facebook
            NSString *url = _bcProfile.url;
            if ([url rangeOfString:@"facebook.com"].location != NSNotFound)
                _mio_writer->set_video_frame_reordering(_mio_writer, false);

            // special workaround for youtube as well
            _mio_writer->set_video_frame_reordering(_mio_writer, false);
        } else {
            BCProfileLibrary *lib = BCProfileLibrary.sharedInstance;
            size = lib.defaultProgramFrameSize;
            size = [_outputProfile sizeConformingToTargetAspectRatio:size];
            _mio_writer->set_fragment_interval(_mio_writer, 30); // seconds
            _mio_writer->set_frame_size(_mio_writer, size.width, size.height);
            _mio_writer->set_audio_stereo(_mio_writer, _audioMixer.stereo);
            _mio_writer->set_audio_sample_rate(_mio_writer, (int)_audioMixer.sampleRate);
            // TODO: set audio and video bitrates
        }

        assert(!_mediaListTransaction);
        _mediaListTransaction = [MediaList.sharedInstance addFileWithPath:_filePath];

        _mio_writer->set_auto_start_time(_mio_writer, false, true);
        _mio_writer->set_start_time_listener(_mio_writer, (__bridge void *)self, _start_time_listener);
        _mio_writer->set_error_listener(_mio_writer,  (__bridge void *)self, _error_listener);

        _mio_writer->start(_mio_writer);

        _rec_queue = dispatch_queue_create("ProgramRecorder MIO Queue", NULL);

        _apushable = _mio_writer->new_audio_pushable(_mio_writer);

        _vPushable = [[MFlowVPushable alloc]
                      initWithMflow_vpushable:_mio_writer->new_video_pushable(_mio_writer)
                      transfer:true];

        _arelay = mflow_pcm_relay_create(_rec_queue, _apushable);

        _vRelay = [[MFlowVRelay alloc] initWithOutput:_vPushable queue:_rec_queue];

        [_vmixerOutput addOutputWithPushable:_vRelay];

        [_audioMixer enableProgramOutput:&_arelay->z];
    }

    [self startLiveTranscription];

    // notify

    [self _setState:kProdTargetStateRunning];
}

- (void)startLiveTranscription
{
    if (!_gid)
        return;

    _transcription_service = [[LiveTranscriptionService alloc] initWithGid:_gid broadcastId: _broadcastId];
    _transcription_apushable = [_transcription_service start];
    if (!_transcription_apushable) {
        _liveTranscriptionStarted = NO;
        // Service didn't start properly
        _transcription_service = NULL;
        return;
    }

    _liveTranscriptionStarted = YES;
    [_audioMixer enableTranscriptionOutput:_transcription_apushable];
}

- (void)stop
{
    if (_state == kProdTargetStateFinishing || _state == kProdTargetStateIdle)
        return;

    dispatch_async(dispatch_get_main_queue(), ^{
        [self stopStep1];
    });

    [self _setState:kProdTargetStateFinishing];
}

- (void)stopStep1
{
    _growthStopping = YES;
    id<RLGrowingSourceDelegate> delegate = _growthDelegate;
    if (delegate) {
        [delegate source:self willStopGrowingWhenAllowed:^{
            [self stopStep2];
        }];
    } else {
        [self stopStep2];
    }
}

/**
 * Stop audio source.
 * Called in the main queue.
 */
- (void)stopStep2
{
    [_audioMixer disableProgramOutput];
    if (_transcription_apushable) {
        [_audioMixer disableTranscriptionOutput];
    }

    mflow_pcm_relay_terminate(_arelay, _retain_block(^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [self stopStep2b];
        });
    }), _exec_block);
}

/**
 * Flush audio encoder.
 * Called in the main queue.
 */
- (void)stopStep2b
{
    if (_recConf.format == REC_CONF_FORMAT_SEG) {
        assert(_apushable->z.clazz == &mflow_aenc_aac_class);
        mflow_aenc_aac_terminate((MFLOW_AENC_AAC *)_apushable, _retain_block(^{
            dispatch_async(dispatch_get_main_queue(), ^{
                [self stopStep3];
            });
        }), _exec_block);
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self stopStep3];
        });
    }
}

/**
 * Stop video source.
 * Called in the main queue.
 */
- (void)stopStep3
{
    [_vmixerOutput removeOutput:_vRelay completion:^{
        dispatch_async(_rec_queue, ^{
            [_vRelay terminateWithCompletion:^{
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self stopStep3b];
                });
            }];
        });
    }];
}

/**
 * Flush video encoder.
 * Called in the main queue.
 */
- (void)stopStep3b
{
    if (_recConf.format == REC_CONF_FORMAT_SEG) {
        assert(_vPushable.mflow_vpushable->z.clazz == &mflow_venc_h264_class);
        mflow_venc_h264_terminate((MFLOW_VENC_H264 *)_vPushable.mflow_vpushable, _retain_block(^{
            dispatch_async(_rec_queue, ^{
                [self stopStep4];
            });
        }), _exec_block);
    } else {
        dispatch_async(_rec_queue, ^{
            [self stopStep4];
        });
    }
}

/**
 * Close output file.
 * Called in the rec queue.
 */
- (void)stopStep4
{
    if (_recConf.format == REC_CONF_FORMAT_SEG) {
        int rv = qtf_writer_close(_qtf_writer);
        if (rv < 0) {
#ifdef DEBUG
            GMU_FATAL(rv, "cannot close output file");
#endif
        }

        c3_release(&_qtf_input_aac->z.z);
        _qtf_input_aac = NULL;

        c3_release(&_qtf_input_avc->z.z);
        _qtf_input_avc = NULL;

        c3_release(&_qtf_writer->z);
        _qtf_writer = NULL;

        dispatch_async(dispatch_get_main_queue(), ^{
            [self stopStep5];
        });
    } else {
        _mio_writer->close_async(_mio_writer, dispatch_get_main_queue(), _retain_block(^{
            c3_release(&_mio_writer->z);
            _mio_writer = NULL;
            [self stopStep5];
        }), _exec_block);
    }
}

/**
 * Called in the main queue.
 */
- (void)stopStep5
{
    [_mediaListTransaction commit];
    _mediaListTransaction = nil;

    c3_release(&_arelay->z.z);
    _arelay = NULL;
    c3_release(&_apushable->z);
    _apushable = NULL;

    if (_transcription_apushable) {
        c3_release(&_transcription_apushable->z);
        _transcription_apushable = NULL;
    }
    _transcription_service = NULL;

    _vPushable = nil;
    _video_enc_queue = nil;
    _audio_enc_queue = nil;
    _rec_queue = nil;

    _growthStopping = NO;
    _growthStopped = YES;
    id<RLGrowingSourceDelegate> delegate = _growthDelegate;
    if (delegate)
        [delegate sourceDidStopGrowing:self];

    [self _setState:kProdTargetStateIdle];
}

- (void)performAction:(ProdTargetActionMask)action
{
    [self stop];
}

- (void)recordingDidStartWithTime:(int64_t)time
{
    _startTime = time;
    _isStartTimeAvailable = YES;
    [_delegate prodTargetStartTimeBecameAvailable:self];
}

- (void)recordingHasPermanentError
{
    if (_state != kProdTargetStateRunning)
        return;

    _actionRequest = kProdTargetActionRequestErrorNotification;
    _actionChoice = kProdTargetActionMaskStop;
    if (_mio_writer->get_error && _mio_writer->get_error(_mio_writer) == GMU_ERR_NO_SPACE) {
        _actionError = [ProdError errorWithCode:ProdErrorCodeDiskFull];
    } else {
        _actionError = [ProdError errorWithCode:ProdErrorCodeRecordingError];
    }

    [self _setState:kProdTargetStateRequestingAction];
}

@end
