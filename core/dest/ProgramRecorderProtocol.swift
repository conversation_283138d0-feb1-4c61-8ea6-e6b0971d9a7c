//
//  ProgramRecorderProtocol.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 14.07.2025.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

@objc public protocol ProgramRecorderProtocol: ProdRecorderProtocol, RLGrowingSource {
    var bcProfile: SwitcherStreamSettings? { get set }
    var name: String? { get set }
    var mid: String? { get set }
    var gid: String? { get set }
    var broadcastId: String? { get set }
    var outputProfile: OutputProfile? { get set }
    var vmixerOutput: VOutputPump? { get set }
    var audioMixer: AudioMixer? { get set }
    var recConf: REC_CONF { get set }
    var filePath: String? { get }
    var liveTranscriptionStarted: Bool { get }

    func start()
    func stop()
}
