//
//  IsocamStreamerMock.swift
//  Switcher-B1Tests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 15.07.2025.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation
@testable import Switcher_B1

class IsocamStreamerMock: IsocamStreamerProtocol {
    var url: URL?
    var name: String?
    var broadcastId: String?
    var useSSNAuthentication: Bool = false
    var progressTime: Int32 = -1
    var consumeSource: Bool = false

    var delegate: (any ProdTargetDelegate)?
    var state: ProdTargetState = .idle
    var actionRequest: ProdTargetActionRequest = .errorNotification
    var actionChoice: ProdTargetActionMask = .stop
    var actionError: ProdError?
    func perform(action: ProdTargetActionMask) { }
    func dumpState() { }

    func start() {
        state = .running
    }
    func stop() {
        state = .idle
    }
}
