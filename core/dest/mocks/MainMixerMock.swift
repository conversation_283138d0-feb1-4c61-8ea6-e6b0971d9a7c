//
//  MainMixerMock.swift
//  Switcher-B1Tests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 15.07.2025.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation
@testable import Switcher_B1

class MainMixerMock: MainMixerProtocol {
    var channelManager: any M2ChannelManagerProtocol { M2ChannelManagerMock() }
    var channelCatalog = M2ChannelCatalog()
    var delegates = M2Delegates()
    var lastSpeedStatus: String = "idle"
    var lastSpeedResult: Double = 0.0
    var resyncDelay: Float = 1.0

    func startProd() { }
    func stopProd() { }

    var sourcePanelLogger: any SourcePanelLoggerProtocol = SourcePanelLoggerMock()
    var markersRecorder: (any MarkersRecorderProtocol)?
    func addMarkersRecorder(_ recorder: any MarkersRecorderProtocol) {
        markersRecorder = recorder
    }
    func removeMarkersRecorder(_ recorder: any MarkersRecorderProtocol) {
        if markersRecorder === recorder {
            markersRecorder = nil
        }
    }

}
