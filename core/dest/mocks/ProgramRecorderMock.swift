//
//  ProgramRecorderMock.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 15.07.2025.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

@testable import Switcher_B1

class ProgramRecorderMock: ProgramRecorderProtocol {
    var isStartTimeAvailable: Bool = false
    var startTime: Int64 = -1
    var isGrowingSourceStopping: Bool = false
    var isGrowingSourceStopped: Bool = true
    var growingSourceDelegate: (any RLGrowingSourceDelegate)!

    var delegate: (any ProdTargetDelegate)?
    var state: ProdTargetState = .idle
    var actionRequest: ProdTargetActionRequest = .errorNotification
    var actionChoice: ProdTargetActionMask = .stop
    var actionError: ProdError?
    func perform(action: ProdTargetActionMask) { }
    func dumpState() { }

    var bcProfile: SwitcherStreamSettings?
    var name: String?
    var mid: String?
    var gid: String?
    var broadcastId: String?
    var outputProfile: OutputPro<PERSON><PERSON>?
    var vmixerOutput: VOutputPump?
    var audioMixer: AudioMixer?
    var recConf = REC_CONF()
    var filePath: String?
    var liveTranscriptionStarted: Bool = false

    func start() {
        state = .running
    }
    func stop() {
        state = .idle
    }
}
