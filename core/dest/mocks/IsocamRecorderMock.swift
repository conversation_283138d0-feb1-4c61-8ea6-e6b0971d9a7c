//
//  IsocamRecorderMock.swift
//  Switcher-B1Tests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 15.07.2025.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation
@testable import Switcher_B1

class IsocamRecorderMock: IsocamRecorderProtocol {
    var isocamRecOptions: M2RecOption = .remoteCameraOnly
    var isocamRecConf = REC_CONF()
    var name: String = ""
    var gid: String = ""
    var isStartTimeAvailable: Bool = false
    var startTime: Int64 = -1

    var delegate: (any ProdTargetDelegate)?
    var state: ProdTargetState = .idle
    var actionRequest: ProdTargetActionRequest = .errorNotification
    var actionChoice: ProdTargetActionMask = .stop
    var actionError: ProdError?
    func perform(action: ProdTargetActionMask) { }
    func dumpState() { }

    func start() {
        state = .running
    }
    func stop() {
        state = .idle
    }
}
