//
//  DashStreamer.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON> on 11.11.20.
//  Copyright © 2020 Switcher Inc. All rights reserved.
//

import Foundation
import InternalBridging

private let logger = LsLogger(subsystem: "swi.core", category: "DashStreamer") // lazily created (only if used)

@objc public class DashStreamer: ProdLocalStreamer {

    private enum Event {
        case none
        case start
        case stop
        case done
        case error
    }

    private var url: URLComponents
    private let dashHttp: DashHttp

    public override var actionRequest: ProdTargetActionRequest { return .errorNotification }
    public override var actionChoice: ProdTargetActionMask { return .stop }

    private var errorCode: ProdErrorCode = .none
    private var _actionError: ProdError?
    public override var actionError: ProdError { return _actionError! }
    private var _progressTime: Int32 = 0
    public override var progressTime: Int32 { return _progressTime }

    @objc public init(url: URL) {
        self.url = URLComponents(url: url, resolvingAgainstBaseURL: false)!
        self.url.scheme = "https"
        dashHttp = DashHttp(baseUrl: self.url)

        /*
         * dashHttp.maxRetries = 6: dashHttp retries to transmit a request for a maximum of 6 times
         * A dashHttp request returns an error after 31.5 - 96 s in case of a permanent communication
         * problem where no request passes.
         * Minimum of 31.5 s:
         * In case the internal http request takes ~0 s until an error is reported, the time corresponds
         * to the sum of all idle times between retrials: 0.5 + 1 + 2 + 4 + 8 + 16 = 31.5 s
         * Minimum of 31.5 s:
         * In case the internal http request takes a maximum of 16 s until an error is reported, the
         * time corresponds to the sum of 6 * 16 s = 96 s. No additinal idle times between retrials is added.
         *
         * After the successful transmission of the first segment maxRetries is set to 0 which means
         * infinite retrials. In this case no error is returned and retransmissions are not stopped.
         */
        dashHttp.maxRetries = 6
    }

    /**
     * Must be called from the main dispatch queue
     */
    public override func start() {
        processEvent(event: .start)
    }

    /**
     * Must be called from the main dispatch queue
     */
    public override func stop() {
        processEvent(event: .stop)
    }

    /**
     * Must be called from the main dispatch queue
     */
    public override func perform(action: ProdTargetActionMask) {
        processEvent(event: .stop)
    }

    private func processEventAsync(event: Event) {
        DispatchQueue.main.async {
            self.processEvent(event: event)
        }
    }

    private func processDashEventAsync(event: Event) {
        DispatchQueue.main.async {
            self.processDashEvent(event: event)
        }
    }

    private func processEvent(event: Event) {

        switch state {

        case .idle:
            if event == .start, // start command from ProdManager
                let recorder {
                recorder.growingSourceDelegate = self
                processDashEventAsync(event: .start)
                _setState(.running)
                logger.log(type: .info, "started")
            }

        case .running:
            if event == .stop {         // stop command from ProdManager
                processDashEventAsync(event: .stop)
                _setState(.finishing)
            } else if event == .done {  // sub-state-machine is done sending all segments
                _setState(.idle)
            } else if event == .error { // sub-state-machine is in error
                _actionError = ProdError(errorCode: errorCode)
                _setState(.requestingAction)
            }

        case .finishing:
            if event == .done {         // sub-state-machine is done finishing on stop request
                _setState(.idle)
            }

        case .requestingAction:         // error
            if event == .stop {         // stop command from ProdManager
                processDashEventAsync(event: .stop)
                _setState(.finishing)
            }
        default:
            FatalLogger.shared.logFatalError("DashStreamer: invalid state")
        }

        logger.log(type: .info, "state: \(prodTargetStateName(state)!)")
    }

    // MARK: - Dash State Machine

    let segmTimescale: Int = 1000
    let segmDuration: Int = 2000 // sync frame period is always 2 sec (correspods to seg duration)

    @objc public var name = "Unknown"
    @objc public var videoWidth = -1
    @objc public var videoHeight = -1
    @objc public var videoBitrate = -1
    @objc public var audioBitrate = -1

    private enum DashState {
        case idle
        case requestingBroadcastId
        case sendingStart
        case dispatchingSegments
        case stoppingDispatchSegments
        case finishing
        case error
    }

    private var dashState: DashState = .idle
    private var dashEnterState = false

    private var comStatusCode: Int = 0
    private var comData: Data?
    private var segmDispatcher: DashSegmDispatcher?
    private var dispResult: DashSegmDispatcher.Result = .none
    private var dHttpReq: DashHttp.Request?
    private var dashSrc: Dash?
    private var broadcastId: String?

    private func processDashEvent(event: Event) {
        let prevState = dashState

        switch dashState {

        case .idle:
            if dashEnterState {
                // Clean-up
                dashSrc = nil
                dHttpReq = nil
                segmDispatcher = nil
                processEventAsync(event: .done) // main state machine: all finished
            }
            if event == .start,
               let recorder,
               let filePath {
                dashSrc = Dash(qtf_src_path: filePath, generate_md5: true)
                dashSrc!.setSourceGrowing(!recorder.isGrowingSourceStopped)
                dashSrc!.setSourceConsuming(consumeSource)

                guard videoWidth != -1 && videoHeight != -1 && videoBitrate != -1 && audioBitrate != -1 else {
                    FatalLogger.shared.logFatalError()
                }
                dashHttp.videoWidth = videoWidth
                dashHttp.videoHeight = videoHeight
                dashHttp.videoBitrate = videoBitrate
                dashHttp.audioBitrate = audioBitrate
                dashHttp.timescale = segmTimescale
                dashHttp.duration = segmDuration

                segmDispatcher = DashSegmDispatcher(dashSource: dashSrc!, dashHttp: dashHttp)

                dashState = .sendingStart // pc_debug set to .requestingBroadcastId
            }

        case .requestingBroadcastId:
            if dashEnterState {
                let dict = ["name": name]
                let jsonBody = dict.JSONFromDictionary()
                dHttpReq = dashHttp.startRequest(
                    .broadcast,
                    uploadBodyData: jsonBody,
                    completion: {(statusCode: Int, _: Error?, data: Data?) in
                    DispatchQueue.main.async {
                        if self.dashState == .requestingBroadcastId {
                            self.comStatusCode = statusCode
                            self.comData = data
                            self.processDashEvent(event: .done)
                        }
                    }
                }
                )
            }

            if event == .stop {
                comData = nil
                dashHttp.cancelRequest(dHttpReq!)
                dashState = .idle
            } else if event == .done {
                if comStatusCode == -1 {
                    // Http request failed
                    errorCode = .httpRequestFailed
                    dashState = .error
                } else if comStatusCode / 100 == 2 {
                    // Status OK between 200 and 299
                    broadcastId = nil
                    if let jsonData = comData {
                        let dict = jsonData.dictionaryFromJSON() as? [String: String]
                        broadcastId = dict?["id"]
                    }
                    if broadcastId != nil {
                        dashState = .sendingStart
                    } else {
                        errorCode = .badHTTPResponseFormat
                        dashState = .error
                    }
                } else {
                    // Status code error : not between 200 and 299 or com error
                    errorCode = .badHTTPResponseCode
                    dashState = .error
                }
                comData = nil
                dHttpReq = nil
            }

        case .sendingStart:
            if dashEnterState {
                dHttpReq = dashHttp.startRequest(
                    .start,
                    filename: "manifest.mpd",
                    completion: {(statusCode: Int, _: Error?, _: Data?) in
                    DispatchQueue.main.async {
                        if self.dashState == .sendingStart {
                            self.comStatusCode = statusCode
                            self.processDashEvent(event: .done)
                        }
                    }
                }
                )
            }

            if event == .stop {
                dashHttp.cancelRequest(dHttpReq!)
                dashState = .idle
            } else if event == .done {
                if comStatusCode == -1 {
                    // Http request failed
                    errorCode = .httpRequestFailed
                    dashState = .error
                } else if comStatusCode / 100 == 2 {
                    // Status OK between 200 and 299
                    dashState = .dispatchingSegments
                } else {
                    // Status code error : not between 200 and 299 or com error
                    errorCode = .badHTTPResponseCode
                    dashState = .error
                }
                dHttpReq = nil
            }

        case .dispatchingSegments:
            if dashEnterState {
                segmDispatcher!.start(progress: { (segmCount: Int, progressTime: Int) in
                    self._progressTime = Int32(progressTime)
                    if segmCount == 1 {
                        // first segment has passed successfully
                        self.dashHttp.maxRetries = 0 // set to infinite retrials
                    } else if segmCount == 2 {
                        // first data segment has passed successfully
                        self.delegate.prodTargetGoodStart(self)
                    }
                }, completion: { (dispResult: DashSegmDispatcher.Result) in
                    DispatchQueue.main.async {
                        if self.dashState == .dispatchingSegments || self.dashState == .stoppingDispatchSegments {
                            self.dispResult = dispResult
                            self.processDashEvent(event: .done)
                        }
                    }
                })
            }

            if event == .stop {
                segmDispatcher!.stop()
                dashState = .stoppingDispatchSegments
            } else if event == .done {
                if dispResult == .finished {
                    dashState = .finishing
                } else if dispResult == .error {
                    errorCode = segmDispatcher!.errorCode
                    dashState = .error
                }
            }

        case .stoppingDispatchSegments:
            if dashEnterState {
            }
            if event == .done {
                dashState = .idle
            }

        case .finishing:
            if dashEnterState {
                dHttpReq = dashHttp.startRequest(
                    .end,
                    filename: "manifest.mpd",
                    completion: {(statusCode: Int, _: Error?, _: Data?) in
                    DispatchQueue.main.async {
                        if self.dashState == .finishing {
                            self.comStatusCode = statusCode
                            self.processDashEvent(event: .done)
                        }
                    }
                }
                )
            }

            if event == .stop {
                dashHttp.cancelRequest(dHttpReq!)
                dashState = .idle
            } else if event == .done {
                if comStatusCode == -1 {
                    // Http request failed
                    errorCode = .httpRequestFailed
                    dashState = .error
                } else if comStatusCode / 100 == 2 {
                    // Status OK between 200 and 299
                    dashState = .idle
                } else {
                    // Status code error : not between 200 and 299 or com error
                    errorCode = .badHTTPResponseCode
                    dashState = .error
                }
                dHttpReq = nil
            }

        case .error:
            if dashEnterState {
                processEventAsync(event: .error) // let main state machine know and handle the error
            }
            if event == .stop {
                dashState = .idle
            }
        }

        // state transition, enter new state
        if dashState != prevState {
            logger.log(type: .info, "dash state: \(prevState) => \(dashState)")
            dashEnterState = true
            processDashEventAsync(event: .none)
        } else {
            dashEnterState = false
        }
    }
}

extension DashStreamer: RLGrowingSourceDelegate {
    public func sourceDidAddSegment(_ source: (any RLGrowingSource)!) {
    }

    public func source(_ source: (any RLGrowingSource)!, willStopGrowingWhenAllowed allow: (() -> Void)!) {
        dashSrc?.sourceGrowingWillStop(queue: DispatchQueue.main, completion: { allow() })
    }

    public func sourceDidStopGrowing(_ source: (any RLGrowingSource)!) {
        dashSrc?.sourceGrowingDidStop()
    }
}
