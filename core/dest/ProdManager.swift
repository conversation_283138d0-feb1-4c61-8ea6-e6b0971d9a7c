//
//  ProdManager.swift
//  Cap-iOS
//
//  Created by <PERSON> on 4/15/25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation

protocol ProdManagerDelegate: AnyObject {
    func prodManagerStateDidChange()
    func prodManagerStreamValidationDidBegin(silently: Bool)
    func prodManagerStreamValidationDidEnd()
    func prodManagerStartTimeBecameAvailable()
    func prodManagerSanityStateDidChange()
}

@MainActor
class ProdManager: NSObject {
    private let logger = LsLogger(subsystem: "swi.core", category: "ProdManager")
    static let sanityCheckInterval: TimeInterval = 15.0

    weak var delegate: (any ProdManagerDelegate)?

    var state: ProdManagerState = .idle
    var actionRequest: ProdManagerActionRequest = .none
    var actionChoice: ProdManagerActionMask = []
    var actionError: ProdError?
    var prodSuccessAction: (any StreamEndedPostProdActionProtocol)?

    private var actionOwner: ActionOwner = .invalid
    private var autoSaveEventCreationInProgress = false

    let factory: any ProdManagerFactoryProtocol
    let mainMixer: any MainMixerProtocol
    var vmixerOutput: VOutputPump?
    var audioMixer: AudioMixer?
    let outputProfile: any OutputProfileProtocol
    let analyticsHelper: any ProdManagerAnalyticsHelperProtocol

    // MARK: – Control Flags
    private var starting = false
    private var stopping = false
    private var processingAction = false
    private var callingDelegate = false

    // MARK: – Recorders & Streamers
    var programRecorder: (any ProgramRecorderProtocol)?
    var programStreamer: (any ProdLocalStreamerProtocol)?
    var isocamRecorder: (any IsocamRecorderProtocol)?
    var isocamStreamer: (any IsocamStreamerProtocol)?
    var markersRecorder: (any MarkersRecorderProtocol)?

    // MARK: - Identifiers & Names
    var gid: String
    var prodName: String?
    var broadcastId: String?
    let bcProfileLibrary: any BCProfileLibraryProtocol
    var bcProfileId: BCProfileId?
    var bcProfile: SwitcherStreamSettings?
    var bcProfileType: BCProfileType = .recordOnly
    var bcProfileRequestInProgress = false

    var firstStreamValidation = false
    var isUploadedToCloud = false
    private(set) var isStartStep1Complete = false
    private(set) var isCountdownComplete = false

    // MARK: - Consumption flags
    private var programStreamerConsumesSource = false
    var isocamStreamerConsumesSource = false

    // MARK: - Recording/streaming booleans
    var programRec = false
    var programStream = false
    var isocamRec = false
    var isocamStream = false

    // MARK: - Sanity & Rotation
    private var programStreamSanityCheckTimer: Timer?
    public var broadcastSanityState: ProdManagerSanityState = .unknown
    var isLocalDeviceRotationAllowed = false

    // MARK: - Thumbnail
    var thumbnail: MFLOW_NAT_VFRAME_REF = mflow_nat_vframe_ref_null()
    var midForThumbnail: String?
    var gidForFinalComposition: String?
    private var programThumbnailGrabber: (any ProgramThumbnailGrabberProtocol)?

    // MARK: - Logging & Monitoring
    var streamLogger: (any StreamLoggerProtocol)?
    var cameraLogger: (any CameraLoggerProtocol)?
    var healthMonitor: (any DeviceHealthMonitorProtocol)?

    // MARK: - Timing & Errors
    var lastAttemptedAutoConnectTime: Date?
    var startError: ProdError?
    var streamStateError: ProdError?
    var inaccurateStartTime: Int64 = 0
    var inaccurateEndTime: Int64 = 0

    private(set) var analyticsReconnectionCount = 0
    private var analyticsProgBroadcastFinalError: ProdError?
    private var analyticsToolUse: ToolUseStats?
    private var analyticsExternalDisplayConnectionCount = 0

#if CONFIG_SWITCHER_CLOUD
    private var streamStatePoller: (any StreamStatePollerProtocol)?
    private var outputStatusPoller: (any OutputStatusPollerProtocol)?
#endif

    init(mainMixer: any MainMixerProtocol,
         factory: (any ProdManagerFactoryProtocol)? = nil,
         bcProfileLibrary: (any BCProfileLibraryProtocol)? = nil,
         outputProfile: (any OutputProfileProtocol)? = nil) {
        let factory = factory ?? ProdManagerFactory()
        self.factory = factory
        self.gid = UUID().uuidString
        self.bcProfileLibrary = bcProfileLibrary ?? BCProfileLibrary.shared
        self.mainMixer = mainMixer
        self.outputProfile = outputProfile ?? OutputProfile.shared
        self.analyticsHelper = factory.createProdManagerAnalyticsHelper()
        super.init()

        self.isStartStep1Complete = false
        self.isCountdownComplete = false
        self.actionOwner = .invalid
    }

    deinit {
        if !mflow_nat_vframe_ref_is_null(thumbnail) {
            mflow_nat_vframe_release(thumbnail)
            thumbnail = mflow_nat_vframe_ref_null()
        }
    }

    var currentProfile: BCProfileType {
        return bcProfileLibrary.currentProfileType
    }

    public func getProfileType() -> Int {
        return bcProfileType.rawValue
    }

    func setProfileType(_ raw: Int) {
        if let newType = BCProfileType(rawValue: raw) {
            bcProfileType = newType
        } else {
            bcProfileType = .recordOnly
        }
    }

    var isBroadcastingEnabled: Bool {
#if CONFIG_SWITCHER
        return currentProfile != .recordOnly
#else
        return NO
#endif
    }

    var isRecordingEnabled: Bool {
#if CONFIG_SWITCHER
        let rp = RecProfile.shared
        return bcProfileLibrary.programRecording || rp.remoteCameraRecording || rp.fullCameraRecording
#else
        return true
#endif
    }

    var isRecordingToLocalDisk: Bool {
        guard let rec = programRecorder else { return false }
        return switch rec.state {
        case .running, .finishing: true
        default: false
        }
    }

    var isReadyForAutoGeneration: Bool {
        return switch currentProfile {
        case .recordOnlyAutoSave:
            !bcProfileLibrary.isAutoSaveReady
        case .practice:
            !bcProfileLibrary.isPracticeModeReady
        default:
            false
        }
    }

    var isStartTimeAvailable: Bool {
        if let rec = programRecorder {
            return rec.isStartTimeAvailable
        }

        if let rec = isocamRecorder {
            return rec.isStartTimeAvailable
        }
        return false
    }

    func start() {
        analyticsHelper.resetFinalization()

        isCountdownComplete = false
        isStartStep1Complete = false

        // Reset capture upload tracking for new production
        mainMixer.sourcePanelLogger.resetUploadTracking()

        lastAttemptedAutoConnectTime = nil

        assert(!callingDelegate)

        if state != .idle {
            return
        }

        gid = UUID().uuidString

        startError = nil
        streamStateError = nil
        actionOwner = .invalid
        broadcastSanityState = .unknown

        midForThumbnail = nil
        gidForFinalComposition = nil

        bcProfile = nil
        self.setProfileType(bcProfileLibrary.currentProfileType.rawValue)
        bcProfileId = bcProfileLibrary.currentProfileId

        prodName = MediaUtil.createRecordingName(getProfileType() == BCProfileType.practice.rawValue)

        broadcastId = nil
        isUploadedToCloud = false

        analyticsHelper.reset()

        // will be updated more precisely later
        inaccurateStartTime = gmu_mtime_us()
        inaccurateEndTime = inaccurateStartTime

        DispatchQueue.main.async {
            self.startStep1()
        }

        starting = true
        self.progressStateChanges()
    }

    func startStep1() {
        starting = false
        if stopping {
            stopping = false
            self.progressStateChanges()
            return
        }

        if isReadyForAutoGeneration {
            startStep1_createAutoGeneratedEvent()
        } else if currentProfile == .customRtmp && bcProfileId == nil {
            startError = ProdError(errorCode: .noRtmpChannel)
            progressStateChanges()
        } else {
            startStep1_checkState()
        }
    }

    func startStep1_createAutoGeneratedEvent() {
        autoSaveEventCreationInProgress = true
        Task { @MainActor in
            do {
                try await bcProfileLibrary.createAutoGeneratedEvent(
                    name: prodName ?? MediaUtil.createRecordingName(false),
                    withTimeoutSecs: currentProfile.eventCreationTimeoutDuration)

                if autoSaveEventCreationInProgress {
                    autoSaveEventCreationInProgress = false
                    startStep1_checkState()
                }
            } catch {
                if self.currentProfile == .practice {
                    startError = ProdError(errorCode: .practiceModeFailed)
                    progressStateChanges()
                } else {
                    bcProfileLibrary.currentProfileType = .recordOnly
                    bcProfile = nil
                    fireStateDidChange()
                    startStep1DidFinish()
                }
            }
        }
    }

    func startStep1_checkState() {
#if CONFIG_SWITCHER_CLOUD
        /* Some streaming channels can be used to stream only once. To stream again,
         * the user must create a new channel.
         * If the server provides an url to check the status, we check it now.*/

        if let profileTypeAttr = bcProfileLibrary.attributes(ofProfileType: currentProfile),
           let streamStatusUrl = profileTypeAttr.streamStatusUrl {
            streamStatePoller = factory.createStreamStatePoller(url: streamStatusUrl,
                                                                ssnAuth: profileTypeAttr.streamStatusUrlNeedsSSNAuth)
            streamStatePoller!.addDelegate(self)
            firstStreamValidation = true
            streamStatePoller!.start()
        } else {
            self.startStep1_checkProfile()
        }
#else
        self.startStep1_checkProfile()
#endif
    }

    func startStep1DidFinish() {
        self.isStartStep1Complete = true
        self.checkAndStartStep2IfReady()
    }

    func startStep1_checkProfile() {
#if CONFIG_SWITCHER
        let profileId = bcProfileLibrary.currentProfileId
        let profileTypeAttr = bcProfileLibrary.attributes(ofProfileType: currentProfile)

        if !isBroadcastingEnabled {
            // no streaming
            self.startStep1DidFinish()
            return
        }

#if CONFIG_SWITCHER_CLOUD

        if let profileTypeAttr = profileTypeAttr,
           let profileUrl = profileTypeAttr.profileUrl, !profileUrl.isFileURL {

            bcProfileRequestInProgress = true
            let api = SwitcherStreamSettingsAPI.shared
            api.getSettings(withUrl: profileUrl) { [weak self] remoteProfile in
                guard let self = self else { return }
                if self.bcProfileRequestInProgress {
                    self.bcProfileRequestInProgress = false
                    if let remoteProfile = remoteProfile {
                        let localProfile = self.bcProfileLibrary.profile(withId: bcProfileLibrary.currentProfileId)
                        if localProfile != remoteProfile {
                            startError = ProdError(errorCode: .inconsistentProfile)
                            self.progressStateChanges()
                        } else {
                            if self.currentProfile == .multiStreaming {
                                let frameWidth = NSNumber(value: bcProfileLibrary.streamingFrameSize.width)
                                let frameHeight = NSNumber(value: bcProfileLibrary.streamingFrameSize.height)
                                localProfile?.videoFrameWidthNumber = frameWidth
                                localProfile?.videoFrameHeightNumber = frameHeight
                                localProfile?.videoBitRateNumber = nil // recalculated in cloneWithDefaultValues
                            }
                            self.bcProfile = localProfile?.cloneWithDefaultValues()
                            self.startStep1DidFinish()
                        }
                    } else {
                        startError = ProdError(code: .badHTTPResponseFormat)
                        self.progressStateChanges()
                    }
                }
            }

            progressStateChanges()
            return
        }
#endif
        bcProfile = bcProfileLibrary.profile(withId: profileId)?.cloneWithDefaultValues()
#endif
        self.startStep1DidFinish()

    }

    func attemptAutoReconnect() -> Bool {
        // No auto-reconnect if the connection was never established
        if startError == nil &&
            streamStateError == nil &&
            broadcastSanityState != .waitingForFirstCheck &&
            isAutoReconnectValid {
            lastAttemptedAutoConnectTime = Date.now
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) { [weak self] in
                self?.performAction(.recoverStreaming)
            }
            return true
        } else {
            return false
        }
    }

    func stop() {
        assert(!callingDelegate)

        analyticsHelper.finalizationStartTime = gmu_mtime_us()

        if state == .finishing || state == .idle {
            return
        }

        let someTargets = programStreamer != nil ||
        programRecorder != nil ||
        isocamRecorder != nil ||
        isocamStreamer != nil

        if starting {
            logger.info("prod stop level=0")
            assert(startError == nil)
            assert(!processingAction)
            assert(!someTargets)
            stopping = true
            self.progressStateChanges()
            return
        }

        processingAction = false

#if CONFIG_SWITCHER_CLOUD
        streamStatePoller?.stop()
        streamStatePoller?.removeDelegate(self)
        streamStatePoller = nil
        streamStateError = nil

        if firstStreamValidation {
            assert(!someTargets)
            firstStreamValidation = false
            startError = nil
            self.progressStateChanges()
            return
        }

        if let outputStatusPoller {
            outputStatusPoller.stopPolling()
            self.outputStatusPoller = nil
        }
#endif

        if autoSaveEventCreationInProgress {
            assert(!someTargets)
            autoSaveEventCreationInProgress = false
            startError = nil
            self.progressStateChanges()
            return
        }

        if bcProfileRequestInProgress {
            assert(!someTargets)
            bcProfileRequestInProgress = false
            startError = nil
            self.progressStateChanges()
            return
        }

        if startError != nil {
            assert(!someTargets)
            self.startError = nil
            self.progressStateChanges()
            return
        }

        if !someTargets {
            self.progressStateChanges()
            return
        }

        if state == .flushing {
            // recorders are not running, streamers are running -> break flushing
            breakFlushing()
        } else {
            // recorders and streamers are running -> stop recorders
            stopRecorders()
        }

        healthMonitor?.stopMonitoring()
    }

    func forceStop() {
        self.stop()

        if state == .flushing {
            self.stop()
        }
    }

    func breakFlushing() {
        if let prodSuccessAction, prodSuccessAction.state.isRunning {
            prodSuccessAction.terminate()
        }
        programStreamer?.stop()
        isocamStreamer?.stop()
        analyticsHelper.isForcedToStop = true
    }

    func stopRecorders() {
        inaccurateEndTime = gmu_mtime_us()
        if let markersRecorder {
            markersRecorder.stop(time: inaccurateEndTime)
            mainMixer.removeMarkersRecorder(markersRecorder)
            self.markersRecorder = nil
        }
        programRecorder?.stop()
        isocamRecorder?.stop()
        programThumbnailGrabber?.stop()
    }

    var isAutoReconnectValid: Bool {
        if let lastAttemptedDate = lastAttemptedAutoConnectTime {
            return DateInterval(start: lastAttemptedDate, end: Date.now).duration > 60
        } else {
            return true
        }
    }

    @MainActor
    func resetAutoGeneratedEvent() {
        let currentProfileType = BCProfileLibrary.shared.currentProfileType
        if currentProfileType.supportsAutoGeneration {
            BCProfileLibrary.shared.currentProfileType = currentProfileType
        }
    }

    func startStep2() {
        var isocamRecConf: RecConf = .init()
        var programRecConf: RecConf = .init()

        rec_conf_init(&isocamRecConf)
        rec_conf_init(&programRecConf)

        var isocamMode: RecProfileIsocamMode = .full
        var isocamRecOptions: M2RecOption = .applyRecProfileBitRate

        step2Init()

#if CONFIG_SWITCHER
        var programStreamUrl: URL?
        var isocamStreamUrl: URL?

        programRec = bcProfileLibrary.programRecording
        programStreamerConsumesSource = !bcProfileLibrary.programRecording

        isocamMode = configureIsoCamMode(isocamRecOptions: &isocamRecOptions)

        if let bcProfile = bcProfile, let urlString = bcProfile.url, let url = URL(string: urlString) {
            configureScheme(bcProfile: bcProfile,
                            isocamMode: isocamMode,
                            programStreamUrl: &programStreamUrl,
                            isocamStreamUrl: &isocamStreamUrl,
                            url: url,
                            isocamRecConf: isocamRecConf)
        }
#endif
        if isocamStreamerConsumesSource {
            isocamRecConf.format = REC_CONF_FORMAT_SEG
        }

        if programStreamerConsumesSource {
            programRecConf.format = REC_CONF_FORMAT_SEG
        }

        var filePath: String?

        logger.info(String(format: "prod start (isocam=%d)", isocamMode.rawValue))

        analyticsHelper.startAnalytics()

        inaccurateStartTime = gmu_mtime_us()
        inaccurateEndTime = inaccurateStartTime

        mainMixer.startProd()

        configureLoggers()

#if CONFIG_SWITCHER_CLOUD
        if isocamStream {
            isocamStreamer = initializeIsocamStreamer(isocamStreamUrl)
        }
#endif
        if isocamRec {
            isocamRecorder = initializeIsocamRecorder(isocamRecConf, options: isocamRecOptions)
        }

#if CONFIG_SWITCHER
        guard
            let profileType = BCProfileType(rawValue: bcProfileType.rawValue),
            let profileTypeAttr = bcProfileLibrary.attributes(ofProfileType: profileType)
        else {
            logger.error("Error getting profile type attributes")
            return
        }

        broadcastId = profileTypeAttr.selectedProfile!.broadcastId

        initializeSourceLogging()

        if programRec {
            let mid = "\(gid as String)-L"
            filePath = initializeProgramRecorder(programRecConf)

            if !programStreamerConsumesSource {
                midForThumbnail = mid
            }

            if !isocamRec && !programStreamerConsumesSource {
                // setup marker writer
                markersRecorder = initializeMarkersRecorder(filePath!)
            }
        }

        if programStream {
            configureProgramStreamer(programStreamUrl!, filePath: filePath!)
        }

#endif

        let recToLocalFile = programRec && !programStreamerConsumesSource
        if !recToLocalFile {
            initializeThumbnailGrabber()
        }

        if isocamMode == .full {
            gidForFinalComposition = gid
        }

#if CONFIG_SWITCHER
        if profileTypeAttr.streamEndedPostUrl != nil {
            initializePostStreamAction(profileTypeAttr)
        }

#if CONFIG_SWITCHER_CLOUD
        if bcProfileType.rawValue == BCProfileType.multiStreaming.rawValue {
            initializeOutputStatusPoller()
        }
#endif
#endif

        // check user account
#if CONFIG_SWITCHER_CLOUD
        SSNUserAccount.shared.checkAccountValidity()
#endif
        assert((programStreamer != nil) || (programRecorder != nil) ||
               (isocamRecorder != nil) || (isocamStreamer != nil))

        initializeDeviceHealthMonitor()
    }

    private func checkAndStartStep2IfReady() {
        if isStartStep1Complete && isCountdownComplete {
            self.startStep2()
        }
    }

    func countdownDidFinish() {
        self.isCountdownComplete = true
        self.checkAndStartStep2IfReady()
    }

    func countdownDidntFinish() {
        self.isCountdownComplete = false
    }

    private func step2Init() {
        isocamRec = true
        isocamStream = false
        programRec = false
        programStream = false
    }

    private func configureLoggers() {
        streamLogger = factory.createStreamLogger(mainMixer: mainMixer)
        streamLogger?.start()
        cameraLogger = factory.createCameraLogger(mainMixer: mainMixer)
    }

    // MARK: - startStep2 helper initialization functions
    private func initializeIsocamStreamer(_ url: URL?) -> any IsocamStreamerProtocol {
        assert(isocamRec)
        let streamer = factory.createIsocamStreamer(mainMixer: mainMixer)
        isocamStreamer = streamer
        streamer.delegate = self
        streamer.consumeSource = isocamStreamerConsumesSource
        streamer.url = url?.changingScheme(to: "http")
        streamer.useSSNAuthentication = true // TODO: always ?
        streamer.start()
        return streamer
    }

    private func initializeIsocamRecorder(_ conf: REC_CONF, options: M2RecOption) -> any IsocamRecorderProtocol {
        let recorder = factory.createIsocamRecorder(mainMixer: mainMixer, name: prodName!, gid: gid)
        isocamRecorder = recorder
        recorder.delegate = self
        recorder.isocamRecOptions = options
        recorder.isocamRecConf = conf
        recorder.start()
        return recorder
    }

    @discardableResult
    private func initializeProgramRecorder(_ conf: REC_CONF) -> String? {
        let mid = "\(gid)-L"
        let recorder = factory.createProgramRecorder()
        programRecorder = recorder
        recorder.delegate = self
        recorder.name = prodName
        recorder.mid = mid
        recorder.gid = gid
        recorder.broadcastId = broadcastId
        recorder.bcProfile = bcProfile
        if let outputProfile = outputProfile as? OutputProfile {
            recorder.outputProfile = outputProfile
        }
        recorder.vmixerOutput = vmixerOutput
        recorder.audioMixer = audioMixer
        recorder.recConf = conf
        recorder.start()
        return recorder.filePath
    }

    private func initializeMarkersRecorder(_ filePath: String) -> any MarkersRecorderProtocol {
        let indexURL = URL(fileURLWithPath: filePath)
            .deletingPathExtension()
            .appendingPathExtension("mmindex")
        let mid = "\(gid)-M"
        let markers = factory.createMarkersRecorder(url: indexURL, mid: mid, gid: gid)
        markersRecorder = markers
        if programRecorder?.isStartTimeAvailable == true {
            markers.start(time: programRecorder!.startTime)
            mainMixer.addMarkersRecorder(markers)
        }

        return markers
    }

    private func configureProgramStreamer(_ programStreamUrl: URL, filePath: String) {
        assert(programRec)

        let path: String = filePath

        let scheme = programStreamUrl.scheme?.lowercased()
        if scheme == "rtmp" || scheme == "rtmps" {
            initializeRLRTMPRecoverableStreamer(path)
        } else if scheme == "dash1" {
            initializeDashStreamer(url: programStreamUrl, path: path)
        } else {
            initializeRLHLSStreamer(url: programStreamUrl, path: path)
        }

        finishStreamerSetup()

        programStreamSanityCheckTimer = Timer.scheduledTimer(withTimeInterval: Self.sanityCheckInterval,
                                                             repeats: true,
                                                             block: { [weak self] _ in
            guard let self = self else { return }
            Task { @MainActor in
                self.checkProgramStreamSanity()
            }
        })

        self.checkProgramStreamSanity()
    }

    private func initializeRLRTMPRecoverableStreamer(_ path: String) {
        let rtmpStreamer = factory.createProgramStreamer(bcProfile: bcProfile)
        rtmpStreamer.filePath = path
        programStreamer = rtmpStreamer
    }

    private func initializeDashStreamer(url: URL, path: String) {
        let streamer = DashStreamer(url: url)
        streamer.filePath = path
        streamer.name = prodName!

        let width = bcProfile?.videoFrameWidthNumber?.intValue ??
        Int(SwitcherStreamSettings.defaultVideoFrameWidth)
        let height = bcProfile?.videoFrameHeightNumber?.intValue ??
        Int(SwitcherStreamSettings.defaultVideoFrameHeight)
        let initialSize = SourceConf.Size(width: Int32(width), height: Int32(height))

        let finalSize = outputProfile.sizeConformingToTargetAspectRatio(initialSize)
        streamer.videoWidth = Int(finalSize.width)
        streamer.videoHeight = Int(finalSize.height)

        streamer.videoBitrate = bcProfile?.videoBitRate ?? 0
        streamer.audioBitrate = bcProfile?.audioBitRate ?? 0

        programStreamer = streamer
    }

    private func initializeRLHLSStreamer(url: URL, path: String) {
        let streamer = RLHLSStreamer()
        streamer.url = url.changingScheme(to: "http")
        streamer.filePath = path
        streamer.name = prodName!
        streamer.useSSNAuthentication = true // TODO: always?
        streamer.videoBitrate = Int32(bcProfile?.videoBitRate ?? 0)
        streamer.audioChannelCount = Int32(bcProfile?.audioChannelCount ?? 0)
        // n = [bcProfile valueForKey:BCProfileKeyVideoFrameWidth];
        // streamer.videoWidth = n.intValue;
        // n = [bcProfile valueForKey:BCProfileKeyVideoFrameHeight];
        // streamer.videoHeight = n.intValue;
        programStreamer = streamer
    }

    private func finishStreamerSetup() {
        programStreamer?.recorder = programRecorder as? ProgramRecorder
        programStreamer?.consumeSource = programStreamerConsumesSource
        programStreamer?.delegate = self
        programStreamer?.start()
    }

    private func initializeDeviceHealthMonitor() {
        healthMonitor = factory.createDeviceHealthMonitor()
        healthMonitor?.startMonitoring(interval: 30)
    }

    private func initializeOutputStatusPoller() {
        outputStatusPoller = factory.createOutputStatusPoller(broadcastId: broadcastId!)
        outputStatusPoller?.startPolling()
    }

    private func initializePostStreamAction(_ profileTypeAttr: BCProfileLibraryTypeAttributes) {
        prodSuccessAction = factory.createStreamEndedPostProdAction(
            broadcastId: broadcastId!,
            url: profileTypeAttr.streamEndedPostUrl!,
            useSwiAuth: profileTypeAttr.streamEndedPostUrlNeedsSSNAuth)
        prodSuccessAction?.delegate = self
    }

    private func initializeThumbnailGrabber() {
        programThumbnailGrabber = factory.createProgramThumbnailGrabber()
        programThumbnailGrabber?.vmixerOutput = vmixerOutput
        programThumbnailGrabber?.delegate = self
        programThumbnailGrabber?.start()
    }

    // MARK: - Progress State Changes
    func progressStateChanges() {
#if CONFIG_SWITCHER_CLOUD
        if let programStreamer,
           let streamStatePoller,
           streamStatePoller.isStreamValidationInProgress && programStreamer.state != ProdTargetState.running {
            streamStatePoller.stop()
            streamStatePoller.removeDelegate(self)
            self.streamStatePoller = nil
        }
#endif

        if let programStreamSanityCheckTimer,
           let programStreamer, programStreamer.state == .idle {
            programStreamSanityCheckTimer.invalidate()
            self.programStreamSanityCheckTimer = nil
            if broadcastSanityState != .unknown {
                self.broadcastSanityState = .unknown
                delegate?.prodManagerSanityStateDidChange()
            }
        }

        let state: ProdManagerState

        if processingAction {
            state = .processingAction
        } else if starting && !stopping {
            assert(!firstStreamValidation)
            assert(!bcProfileRequestInProgress)
            assert(startError == nil)
            state = .running
        } else if starting && stopping {
            assert(!firstStreamValidation)
            assert(!bcProfileRequestInProgress)
            assert(startError == nil)
            state = .finishing
        } else if firstStreamValidation {
            assert(!starting)
            assert(!stopping)
            assert(startError == nil)
            assert(!bcProfileRequestInProgress)
            state = .running
        } else if bcProfileRequestInProgress {
            // warning: _streamStateError can be true
            assert(!starting)
            assert(!stopping)
            assert(startError == nil)
            state = .running
        } else if startError != nil || streamStateError != nil {
            state = .requestingAction
        } else if programStreamer != nil || programRecorder != nil || isocamRecorder != nil ||
                    isocamStreamer != nil || programThumbnailGrabber != nil || prodSuccessAction != nil {
            var substates: SubStates = SubStates()
            substates.programRecording = programRecorder?.state.translateRecorderState() ?? .idle
            substates.programStreaming = programStreamer?.state.translateStreamerState() ?? .idle
            substates.isocamRecording = isocamRecorder?.state.translateRecorderState() ?? .idle
            substates.isocamStreaming = isocamStreamer?.state.translateStreamerState() ?? .idle
            substates.programThumbnailGrabber = programThumbnailGrabber?.state.translateThumbnailGrabberState() ?? .idle
            substates.postProdAction = .idle

            /*
             * When we start streaming in isocam mode, _isocamStreamer runs before _isocamRecorder does.
             * In this case, we want the common state to be running and not flushing.
             */
            if substates.isocamStreaming == .flushing && isocamRecorder == nil {
                substates.isocamStreaming = .running
            }

            if let prodSuccessAction = self.prodSuccessAction {
                if !prodSuccessAction.state.isRunning && !prodSuccessAction.state.isTerminated {

                    let tmpState = substates.commonState()

                    if tmpState == .finishing || tmpState == .idle {
                        prodSuccessAction.start()
                    }
                }
                substates.postProdAction = prodSuccessAction.state.isTerminated ? .idle : .flushing
            }

            state = substates.commonState()
        } else {
            assert(!stopping)
            state = .idle
        }

        if self.state != state {
            logger.info("state: \(self.state.name) => \(state.name)")

            self.state = state

            if self.state == .requestingAction {
                assert(actionOwner == .invalid)
                if let startError {
                    actionRequest = .errorNotification
                    actionChoice = .stopAll
                    actionError = startError
                    actionOwner = .start
                } else if let streamStateError {
                    actionRequest = .errorNotification
                    actionError = streamStateError
                    actionOwner = .streamStatePoller
                    if self.doesItMakeSenseToContinueAfterStoppingTarget(self.prodTargetOwnedBy(actionOwner)) {
                        actionChoice = .stopAll.union(.stopStreaming)
                    } else {
                        actionChoice = .stopAll
                    }
                } else {
                    for i in 0..<5 {
                        if let owner = ActionOwner(rawValue: i),
                           self.prodTargetOwnedBy(owner)?.state == .requestingAction {
                            actionOwner = owner
                            break
                        }
                    }

                    assert(actionOwner != .invalid)
                    actionError = self.prodTargetOwnedBy(actionOwner)?.actionError
                    actionRequest = ProdTargetActionRequest.translateActionRequest(
                        self.prodTargetOwnedBy(actionOwner)!.actionRequest)

                    assert(!(self.prodTargetOwnedBy(actionOwner)?.actionChoice.isDisjoint(with: .stop))!)
                    if actionOwner == .progStreamAdd || actionOwner == .isocamStream {
                        if self.doesItMakeSenseToContinueAfterStoppingTarget(self.prodTargetOwnedBy(actionOwner)) {
                            actionChoice = .stopAll.union(.stopStreaming)
                        } else {
                            actionChoice = .stopAll
                        }
                        if let target = self.prodTargetOwnedBy(actionOwner),
                           target.actionChoice.contains(.continue) && programRecorder?.state == .running {
                            actionChoice = actionChoice.union(.recoverStreaming)
                        }
                    } else {
                        actionChoice = .stopAll
                    }
                    if actionOwner == .progStreamAdd && analyticsHelper.progBroadcastFirstError == nil {
                        analyticsHelper.progBroadcastFirstError = actionError
                    }
                    if actionOwner == .progStreamAdd && actionRequest == .errorNotification && actionError != nil {
                        analyticsHelper.progBroadcastFinalError = actionError
                        analyticsHelper.progBroadcastErrorCount += 1
                    }
                }
                logger.info("actionRequest=\(actionRequest) actionChoice=\(actionChoice)")
            }

            if state == .idle {
                let finalizationEndTime = gmu_mtime_us()
                let finalizationDurationUs = finalizationEndTime - analyticsHelper.finalizationStartTime
                // Final Duration in Seconds
                let finalDurationSec = Int32(finalizationDurationUs / 1000000)
                analyticsHelper.finalizingTimeSeconds = finalDurationSec

                streamLogger?.stop()

                // Report Analytics 
                self.reportForAnalytics()
                self.mainMixer.sourcePanelLogger.reportLogs(broadcastDurationSeconds: finalDurationSec)

                if programStreamerConsumesSource {
                    if let urlString = programStreamer?.filePath,
                       let url = URL(string: urlString) {
                        url.withUnsafeFileSystemRepresentation({ path in
                            if let path {
                                gmu_remove_dir(path, true)
                            }
                        })
                    }
                }

                self.thumbnail = programThumbnailGrabber?.frame ?? mflow_nat_vframe_ref_null()
                if !mflow_nat_vframe_ref_is_null(self.thumbnail) {
                    mflow_nat_vframe_retain(self.thumbnail)
                }

                programRecorder?.delegate = nil
                programStreamer?.delegate = nil
                isocamRecorder?.delegate = nil
                isocamStreamer?.delegate = nil
                programThumbnailGrabber?.delegate = nil
                programRecorder = nil
                programStreamer = nil
                isocamRecorder = nil
                isocamStreamer = nil
                programThumbnailGrabber = nil
                prodSuccessAction?.delegate = nil
                prodSuccessAction = nil
                streamLogger = nil
                cameraLogger = nil

                mainMixer.stopProd()

#if CONFIG_SWITCHER_CLOUD
                streamStatePoller?.stop()
                streamStatePoller?.removeDelegate(self)
                self.streamStatePoller = nil
#endif
            }
            self.fireStateDidChange()
        }
    }

    func fireStateDidChange() {
        assert(!callingDelegate)
        callingDelegate = true
        delegate?.prodManagerStateDidChange()
        callingDelegate = false
    }

    private func translateThumbnailGrabberState(state: ProgramThumbnailGrabberState) -> ProdManagerState {
        return switch state {
        case .running: .running
        case .terminating: .finishing
        case .idle: .idle
        default: .idle
        }
    }

    func checkProgramStreamSanity() {
        guard let streamer = programStreamer else { return }
        let state = streamer.state
        let active = (state == .running || state == .finishing)
        let progressTime = streamer.progressTime

        if active && progressTime >= 0 {
            let runTime = Int((gmu_mtime_us() - inaccurateStartTime) / 1_000_000)
            let buffering = progressTime < runTime - 30
            let newState: ProdManagerSanityState
            if progressTime < 1 {
                newState = .waitingForFirstCheck
            } else if buffering {
                newState = .buffering
                analyticsHelper.networkWarningIndicatorDuration += Int32(ProdManager.sanityCheckInterval)
            } else {
                newState = .healthy
            }
            if broadcastSanityState != newState {
                broadcastSanityState = newState
                delegate?.prodManagerSanityStateDidChange()
            }
        }
    }

    func dumpState() {
        print("State dump vvvvv")
        logger.log(type: .info, "State dump vvvvv")
        logger.log(type: .info, "state=\(state.rawValue)")
        programRecorder?.dumpState()
        programStreamer?.dumpState()
        isocamRecorder?.dumpState()
        isocamStreamer?.dumpState()
        print("State dump ^^^^^")
        logger.log(type: .info, "state dump ^^^^^")
    }

    func performAction(_ action: ProdManagerActionMask) {
        assert(!callingDelegate)

        if actionOwner == .start {
            startError = nil
            progressStateChanges()
            return
        }

        /*
         * When the caller invokes this method, it expects the state to change.
         * One of the prod targets will probably change its state but we are not
         * sure that this results in a change of the prod manager state.
         * So, here we force a transition through the kProdManagerStateProcessingAction
         * state. At the end of this method, we refresh the state which will
         * automatically switch to something else than kProdManagerStateProcessingAction.
         */

        processingAction = true
        progressStateChanges()

        switch action {
        case .stopAll:
            stopAll()
            fallthrough // no break here

        case .stopStreaming:
            stopStreaming()

        case .recoverStreaming:
            recoverStreaming()

        default:
            assert(false)
        }

        /*
         * We now refresh the state which will switch to something else
         * than kProdManagerStateProcessingAction. See comment above.
         */
        actionOwner = .invalid
        processingAction = false
        progressStateChanges()
    }

    private func stopAll() {
        programRecorder?.stop()
        isocamRecorder?.stop()
        programThumbnailGrabber?.stop()
        inaccurateEndTime = gmu_mtime_us()
        if let markers = markersRecorder {
            markers.stop(time: inaccurateEndTime)
            mainMixer.removeMarkersRecorder(markers)
            markersRecorder = nil
        }
    }

    private func stopStreaming() {
        assert(actionOwner != .invalid)
        prodTargetOwnedBy(actionOwner)?.perform(action: .stop)
        if actionOwner == .streamStatePoller || actionOwner == .progStreamAdd {
            streamStateError = nil
        }
    }

    private func recoverStreaming() {
        assert(actionOwner != .invalid)
        if actionOwner == .progStreamAdd {
            analyticsHelper.reconnectionCount += 1
            analyticsHelper.progBroadcastFinalError = nil
        }
        prodTargetOwnedBy(actionOwner)?.perform(action: .continue)
        if actionOwner == .streamStatePoller || actionOwner == .progStreamAdd {
            streamStateError = nil
        }
    }

    func prodTargetOwnedBy(_ index: ActionOwner) -> (any ProdTargetProtocol)? {
        return switch index {
        case .progRec:  programRecorder
        case .progStreamAdd, .streamStatePoller: programStreamer
        case .isocamRec: isocamRecorder
        case .isocamStream:  isocamStreamer
        default: nil
        }
    }

    /*
     * When an streaming error appears, we will inform the user through a request
     * for action. At least we prompt the user to confirm he got the error and we then
     * stop the streaming. In some case, however, we can suggest to the user to
     * continue recording.
     * This method help us to define if something else can be kept going.
     */
    func doesItMakeSenseToContinueAfterStoppingTarget(_ target: (any ProdTargetProtocol)?) -> Bool {
        if let rec = programRecorder, rec !== target, rec.state == .running, !programStreamerConsumesSource {
            return true
        }
        if let stream = programStreamer, stream !== target, stream.state == .running {
            return true
        }
        if let rec = isocamRecorder, rec !== target, rec.state == .running {
            return true
        }
        if let stream = isocamStreamer, stream !== target, stream.state == .running {
            return true
        }
        return false
    }

    func programFrameSize() -> SourceConf.Size {
        var size = SourceConf.Size(width: 0, height: 0)

        if let profileId = bcProfileLibrary.currentProfileId {
            if profileId.type == .multiStreaming {
                size.width = Int32(bcProfileLibrary.streamingFrameSize.width)
                size.height = Int32(bcProfileLibrary.streamingFrameSize.height)
            } else if let profile = bcProfileLibrary.profile(withId: profileId)?.cloneWithDefaultValues() {
                size.width = Int32(profile.videoFrameWidthNumber?.intValue ?? 0)
                size.height = Int32(profile.videoFrameHeightNumber?.intValue ?? 0)
            }
        } else {
            size = bcProfileLibrary.defaultProgramFrameSize
        }

        if size.width == 0 || size.height == 0 {
            size.width = Int32(SwitcherStreamSettings.defaultVideoFrameWidth)
            size.height = Int32(SwitcherStreamSettings.defaultVideoFrameHeight)
        }

        return outputProfile.sizeConformingToTargetAspectRatio(size)
    }

    func programFrameAspectRatio() -> gmu_ratio_i32 {
        return outputProfile.targetAspectRatio
    }

    func setLocalDeviceRotation(_ rotation: Int32) {
        isLocalDeviceRotationAllowed = true
        if analyticsHelper.localDeviceRotation != rotation {
            analyticsHelper.localDeviceRotation = rotation
            analyticsHelper.numberOfLocalDeviceRotationChanges += 1
        }
    }

    func duration() -> Int64 {
        return inaccurateEndTime - inaccurateStartTime
    }

    //
    //    /**
    //     * When we ended recording and we are flushing all data to the server, this
    //     * method returns the number of microseconds of video we still have to send.
    //     * If we are not flushing or we are not able to estiate the time, this method
    //     * returns -1.
    //     */
    func timeToFlush() -> Int64 {
        if state != .flushing {
            return -1
        }

        guard let progress_s = programStreamer?.progressTime, progress_s >= 0 else { return -1 }

        let duration_us = self.duration()
        let progress_us = Int64(progress_s) * 1_000_000

        let videoFlushTime = gmu_max_i64(duration_us - progress_us, 0)

        // Add estimated capture upload time
        let captureUploadTime = mainMixer.sourcePanelLogger.estimateUploadTime()

        // Log the time breakdown for debugging
        if captureUploadTime > 0 {
            let videoSeconds = Double(videoFlushTime) / 1_000_000
            let captureSeconds = Double(captureUploadTime) / 1_000_000
            print("ProdManager.timeToFlush: video=\(String(format: "%.1f", videoSeconds))s, captures=\(String(format: "%.1f", captureSeconds))s")
        }

        return videoFlushTime + captureUploadTime
    }



    /// Initialize source logging when recording actually starts (not just when entering studio)
    private func initializeSourceLogging() {
        guard let broadcastId = broadcastId,
              bcProfileType.isSourceLoggingEnabled else {
            return
        }

        mainMixer.sourcePanelLogger.initialize(broadcastId: broadcastId)
        logger.info("Source logging initialized for broadcast: \(broadcastId)")
    }

    func startTime() -> Int64 {
        if let rec = programRecorder {
            return rec.startTime + Int64(mainMixer.resyncDelay)
        }

        if let rec = isocamRecorder {
            return rec.startTime + Int64(mainMixer.resyncDelay)
        }

        return 0
    }
}

extension ProdManager {
    @MainActor func reportForAnalytics() {

        if !analyticsHelper.isEnabled {
            return
        }
        analyticsHelper.setProperties(prodManager: self)

        analyticsHelper.reportEvents(programRec: programRec,
                                     programStream: programStream,
                                     bcProfileType: bcProfileType.rawValue)
    }
}
