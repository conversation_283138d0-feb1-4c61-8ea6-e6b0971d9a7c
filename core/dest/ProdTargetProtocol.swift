//
//  ProdTargetProtocol.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 14.07.2025.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation

@objc public protocol ProdTargetProtocol: AnyObject {
    var delegate: (any ProdTargetDelegate)? { get set }
    var state: ProdTargetState { get }
    var actionRequest: ProdTargetActionRequest { get }
    var actionChoice: ProdTargetActionMask { get }
    var actionError: ProdError? { get }

    func start()
    func stop()
    func perform(action: ProdTargetActionMask)
    func dumpState()
}

@objc public protocol ProdRecorderProtocol: ProdTargetProtocol {
    var isStartTimeAvailable: Bool { get }
    var startTime: Int64 { get }
}

@objc public protocol ProdStreamerProtocol: ProdTargetProtocol {
    var progressTime: Int32 { get }
    var consumeSource: Bool { get set }
}

@objc public protocol ProdLocalStreamerProtocol: ProdStreamerProtocol {
    var filePath: String? { get set }
    var recorder: (any RLGrowingSource)? { get set }
}
