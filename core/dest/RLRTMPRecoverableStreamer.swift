//
//  RLRTMPRecoverableStreamer.swift
//  Switcher
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 28.01.22.
//  Copyright © 2022 Switcher Inc. All rights reserved.
//

import Foundation
import InternalBridging

class RLRTMPRecoverableStreamer: ProdLocalStreamer {

    private let logger = LsLogger(subsystem: "swi.core", category: "RLRTMPRecoverableStreamer")

    private var bcProfile: SwitcherStreamSettings?

    private var rtmpComErrorCount: Int = 0
    private var onGrowthDidStop: (() -> Void)?

    private var rtmpCtrl: RtmpController

    public override var actionRequest: ProdTargetActionRequest { return .errorNotification }
    private var _actionChoice: ProdTargetActionMask = .stop
    public override var actionChoice: ProdTargetActionMask { _actionChoice }
    private var _actionError: ProdError?
    public override var actionError: ProdError { return _actionError! }

    public override var progressTime: Int32 { return rtmpCtrl.streamTime }

    init(bcProfile: SwitcherStreamSettings?) {
        self.bcProfile = bcProfile

        rtmpCtrl = RtmpController(rtmp_controller: rtmp_controller_create(), transfer: true)
        rtmpCtrl.mainQueue = .main

        super.init()

        rtmpCtrl.delegate = self
    }

    /**
     * Must be called from the main dispatch queue
     */
    public override func start() {
        logger.info("start")

        if state == .idle {

            guard bcProfile != nil else { FatalLogger.shared.logFatalError() }

            _setState(.running)

            recorder?.growingSourceDelegate = self
            if let recorder, recorder.isGrowingSourceStopping == true {
                weak var weakSelf = self
                onGrowthDidStop = {
                    weakSelf?.doStart()
                }
            } else {
                doStart()
            }
        }
    }

    private func doStart() {
        if state == .running {
            rtmpCtrl.url = URL(string: bcProfile?.url ?? "")
            rtmpCtrl.streamName = bcProfile?.rtmpStream

            rtmpCtrl.audioBitrate = bcProfile?.audioBitRate ?? 0
            rtmpCtrl.videoBitrate = bcProfile?.videoBitRate ?? 0
            rtmpCtrl.keyFrameInterval = bcProfile?.videoKeyFrameInterval ?? 0
            rtmpCtrl.isEmulatingFmleUserAgent = bcProfile?.emulateFMLEUserAgent ?? false
            rtmpCtrl.timeBeforeConnection = bcProfile?.timeBeforeConnection ?? 0.0
            rtmpCtrl.mediaPath = filePath

            rtmpCtrl.isSourceGrowing = recorder?.isGrowingSourceStopped == false
            rtmpCtrl.isSourceConsuming = consumeSource

            _actionChoice = .stop
            rtmpComErrorCount = 0
            rtmpCtrl.start()
        }
    }

    private func recover() {
        guard state == .requestingAction else { FatalLogger.shared.logFatalError() }

        _setState(.running)

        if let recorder, recorder.isGrowingSourceStopping {
            weak var weakSelf = self
            onGrowthDidStop = {
                weakSelf?.doRecover()
            }
        } else {
            doRecover()
        }
    }

    private func doRecover() {
        if state == .running {
            _actionChoice = .stop
            rtmpComErrorCount = 0
            rtmpCtrl.recover()
        }
    }

    /**
     * Must be called from the main dispatch queue
     */
    public override func stop() {
        logger.info("stop")

        stop(true)
    }

    private func stop(_ closeAbruptly: Bool) {
        if state == .running || state == .requestingAction {

            rtmpCtrl.stop(closeAbruptly) { [self] in
                assert(Thread.isMainThread)
                _setState(.idle)
            }

            _setState(.finishing)
        }
    }

    /**
     * Must be called from the main dispatch queue
     */
    public override func perform(action: ProdTargetActionMask) {
        logger.info("perform: action=\(action)")
        if state == .requestingAction {
            if action == .continue {
                recover()
            } else {
                stop()
            }
        }
    }

    override public func dumpState() {
        logger.info("state=\(state)")
    }

    func errorFromRTMPState() -> ProdError? {
        var error: ProdError?

        var state: Int32 = 0
        var errorCode: Int32 = 0
        var errorOp: Int32 = 0
        var errorMsg: UnsafePointer<CChar>?

        rtmpCtrl.rtmpStreamerInfo(state: &state, code: &errorCode, op: &errorOp, message: &errorMsg)

        switch state {
        case RTMP_STATE_OK:
            break
        case RTMP_STATE_RESET_BY_PEER:
            error = ProdError(code: .socketClosedByPeer)
        case RTMP_STATE_NET_ERROR:
            switch errorOp {
            case RTMP_ERROR_OP_RX:
                error = ProdError(code: .socketRxError)
            case RTMP_ERROR_OP_TX:
                error = ProdError(code: .socketTxError)
            default:
                error = ProdError(code: .socketError)
            }
            error?.unixErrno = errorCode
        case RTMP_STATE_SEC_ERROR:
            error = ProdError(code: .socketSecurityError)
            error?.esocketSecError = errorCode
            if let errorMsg = errorMsg {
                error?.esocketSecErrorMessage = String(utf8String: errorMsg)
            }
        case RTMP_STATE_RTMP_ERROR:
            switch errorCode {
            case RTMP_ERROR_NO_HANDSHAKE:
                error = ProdError(code: .noRTMPHandshake)
            case RTMP_ERROR_BAD_HANDSHAKE:
                error = ProdError(code: .badRTMPHandshake)
            default:
                error = ProdError(code: .unknownRTMPError)
            }
        default:
            error = ProdError(code: .unknownRTMPError)
        }

        return error
    }
}

extension RLRTMPRecoverableStreamer: RLGrowingSourceDelegate {
    public func sourceDidAddSegment(_ source: (any RLGrowingSource)!) {
    }

    public func source(_ source: (any RLGrowingSource)!, willStopGrowingWhenAllowed allow: (() -> Void)!) {
        rtmpCtrl.sourceGrowingWillStop(queue: .main, completion: { allow() })
    }

    public func sourceDidStopGrowing(_ source: (any RLGrowingSource)!) {
        rtmpCtrl.sourceGrowingDidStop()

        if let onGrowthDidStop = self.onGrowthDidStop {
            onGrowthDidStop()
            self.onGrowthDidStop = nil
        }
    }
}

extension RLRTMPRecoverableStreamer: RtmpControllerDelegate {
    public func rtmpControllerDidEmitEvent(controller: RtmpController, event: RtmpControllerEvent) {
        assert(Thread.isMainThread)
        logger.info("rtmpControllerDidEmitEvent: event=\(event)")

        if event == .goodStart {
            delegate.prodTargetGoodStart(self)
        } else if event == .end {
            stop(false)
        } else {
            rtmpComErrorCount += 1
            if rtmpComErrorCount > 1 {
                return
            }

            var error: ProdError?

            switch event {
            case .communicationClosedByPeer, .networkError, .securityError, .rtmpError:
                error = errorFromRTMPState()
                if error == nil {
                    error = ProdError(code: .unknownRTMPError)
                }
            case .noData:
                error = ProdError(code: .nothingToStream)
            default:
                error = ProdError(code: .unknownRTMPError)
            }

            _actionChoice = [.stop, .continue]
            _actionError = error
            _setState(.requestingAction)
        }
    }

    public func rtmpControllerConnectionFailed(
        controller: RtmpController,
        domain: RtmpControllerDomain,
        error: Int32,
        errNo: Int32,
        gmuError: Int32,
        secError: Int32,
        secErrorMsg: String?
    ) {
        logger
            .info(
                "rtmpControllerConnectionFailed: domain=\(domain) error=\(error) errNo=\(errNo) gmuError=\(gmuError) secError=\(secError) secErrorMsg=\(secErrorMsg ?? "<nil>")"
            )

        if state == .finishing {
            _setState(.idle)
            return
        }

        var actionError: ProdError?

        if (secError != 0) && error == GMU_ERR_IO {
            actionError = ProdError(code: .socketSecurityError)
            actionError?.esocketSecError = secError
            actionError?.esocketSecErrorMessage = secErrorMsg
        } else if error == GMU_ERR_NET_CLOSED_BY_PEER {
            actionError = ProdError(code: .socketClosedByPeer)
        } else if error == GMU_ERR_IO {
            actionError = ProdError(code: domain == .rtmpHandshake ? .socketError : .noESocketHandshake)
            actionError?.unixErrno = errNo
            actionError?.gmuError = gmuError
        } else if error == GMU_ERR_NET_NO_HOST {
            actionError = ProdError(code: .noHost)
        } else if error == GMU_ERR_NET_NO_SOCKET {
            actionError = ProdError(code: .noSocket)
        } else if error == GMU_ERR_UNSUPPORTED_OP || error == GMU_ERR_NET_NO_CONNECTION {
            actionError = ProdError(code: .noConnection)
        } else if error == GMU_ERR_SYSTEM {
            actionError = ProdError(code: .noESocket)
        } else if error == GMU_ERR_TIMEOUT {
            actionError = ProdError(code: domain == .rtmpHandshake ? .noRTMPHandshake : .noESocketHandshake)
        } else if error == GMU_ERR_NET_BAD_RESPONSE {
            actionError = ProdError(code: .badRTMPHandshake)
        } else {
            actionError = ProdError(code: .unknownRTMPError)
        }

        _actionChoice = [.stop, .continue]
        _actionError = actionError
        _setState(.requestingAction)
    }
}
