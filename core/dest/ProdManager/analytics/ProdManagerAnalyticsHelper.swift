//
//  ProdManagerAnalyticsHelper.swift
//  Cap-iOS
//
//  Created by <PERSON> on 6/9/25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation

private let logger = LsLogger(subsystem: "swi.core", category: "ProdManagerAnalyticsHelper")

@MainActor
protocol ProdManagerAnalyticsHelperProtocol: AnyObject {
    var isEnabled: Bool { get set }
    var isForcedToStop: Bool { get set }
    var progBroadcastFirstError: ProdError? { get set }
    var progBroadcastFinalError: ProdError? { get set }
    var progBroadcastErrorCount: Int32 { get set }
    var reconnectionCount: Int32 { get set }
    var externalDisplayConnectionCount: Int32 { get set }
    var toolUse: ToolUseStats? { get set }

    var numberOfLocalDeviceRotationChanges: Int32 { get set }
    var startLocalDeviceRotation: Int32 { get set }
    var localDeviceRotation: Int32 { get set }

    var timestampCount: Int32 { get set }
    var finalizationStartTime: Int64 { get set }
    var finalizingTimeSeconds: Int32 { get set }
    var networkWarningIndicatorDuration: Int32 { get set }

    func reset()
    func resetProps()
    func resetFinalization()

    func startAnalytics()
    func setProperties(prodManager: ProdManager)
    func reportEvents(programRec: Bool,
                      programStream: Bool,
                      bcProfileType: Int)
}

@MainActor
class ProdManagerAnalyticsHelper: ProdManagerAnalyticsHelperProtocol {
    let analytics = Analytics.shared

    // MARK: - Analytics Property Data
    /// Data to send in the report event
    var props: ProdManagerAnalyticsData = .init()

    // MARK: - Properties
    var isEnabled = false
    var isForcedToStop = false
    var progBroadcastFirstError: ProdError?
    var progBroadcastFinalError: ProdError? // nil if ended without any error or all errors have been recovered
    var progBroadcastErrorCount: Int32 = 0
    var reconnectionCount: Int32 = 0
    var externalDisplayConnectionCount: Int32 = 0
    var toolUse: ToolUseStats?

    var numberOfLocalDeviceRotationChanges: Int32 = 0
    var startLocalDeviceRotation: Int32 = 0
    var localDeviceRotation: Int32 = 0

    var timestampCount: Int32 = 0
    var finalizationStartTime: Int64 = 0
    var finalizingTimeSeconds: Int32 = 0
    var networkWarningIndicatorDuration: Int32 = 0
    var initialResyncDelayChangeCount = 0

    // MARK: - Prop Initialization
    func reset() {
        isEnabled = false
        isForcedToStop = false
        progBroadcastFirstError = nil
        progBroadcastFinalError = nil
        progBroadcastErrorCount = 0
        reconnectionCount = 0
    }

    func resetProps() {
        props = .init()
    }

    func resetFinalization() {
        // reset finalization variables for new stream
        finalizationStartTime = 0
        finalizingTimeSeconds = 0
        networkWarningIndicatorDuration = 0
    }

    func startAnalytics() {
        isEnabled = true
        externalDisplayConnectionCount = SecondScreenManager.connectionCount
        if SecondScreenManager.activeConnectionCount > 0 {
            externalDisplayConnectionCount += -1
        }
        toolUse = ToolUseStats()
        toolUse?.begin()
        cut_stat_reset()
        AssetStats.shared.reset()

        startLocalDeviceRotation = localDeviceRotation
        numberOfLocalDeviceRotationChanges = 0
        initialResyncDelayChangeCount = ResyncSettings.shared.resyncDelayChangeCount
    }

    func setProperties(prodManager: ProdManager) {
        resetProps()

#if CONFIG_SWITCHER
        self.setTargetSizeAndRatio(bcProfile: prodManager.bcProfile)

        let rtmps = self.setHost(bcProfile: prodManager.bcProfile)

        props.broadcastId = prodManager.broadcastId

        setIsocamMode()

        props.isocamBroadcast = prodManager.isocamStream
        props.programRec = prodManager.programRec
        props.programBroadcast = prodManager.programStream

        if let type = BCProfileType(rawValue: prodManager.bcProfileType.rawValue) {
            props.broadcastService = BCProfileTypeWrapper.toString(type)
        }

        props.timestampCount = "\(timestampCount)"

#endif

        self.setCamCount(mainMixer: prodManager.mainMixer)

        let duration: Int64 = prodManager.inaccurateEndTime - prodManager.inaccurateStartTime / 1_000_000
        props.duration = duration

        let ipv4_only: Bool = pcap_ipv4_only_mode()
        props.ipv4OnlyMode = ipv4_only

        props.resyncDelay = prodManager.mainMixer.resyncDelay

        let resynDelayChangeCount: Int = ResyncSettings.shared.resyncDelayChangeCount - initialResyncDelayChangeCount
        props.resyncDelayChangeCount = resynDelayChangeCount

        if prodManager.isLocalDeviceRotationAllowed {
            props.rotStart = startLocalDeviceRotation
            props.rotCount = numberOfLocalDeviceRotationChanges
        }

        self.setCutStats()

        if isForcedToStop {
            props.forcedToStop = isForcedToStop
        }

        props.reconnectionCount = reconnectionCount

        self.setBroadcastsErrors()

        self.setUserInfo(rtmps: rtmps)

        if SecondScreenManager.connectionCount > externalDisplayConnectionCount {
            props.programOnExternalDisplay = true
        }

        toolUse?.end()

        let usedTools = toolUse?.usedIds ?? []
        props.usedTools = usedTools

        props.usedFeatures = toolUse?.featuresUsedIds ?? []

        setStreamLoggerStats(prodManager: prodManager, duration: duration)

        let cameras = prodManager.cameraLogger?.cameraLogs ?? []
        props.cameras = cameras

        if let jsonData = try? JSONEncoder().encode(cameras),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            logger.info("cameras=\(jsonString)")
        }

        setProductionStats(prodManager: prodManager)

        // Health monitoring
        setHealthStats(prodManager: prodManager)

        if let programRecorder = prodManager.programRecorder {
            props.liveTranscription = programRecorder.liveTranscriptionStarted
        }

        props.speedTestMps = prodManager.mainMixer.lastSpeedResult
        props.speedTestStatus = prodManager.mainMixer.lastSpeedStatus

        props.networkWarningIndicatorDuration = networkWarningIndicatorDuration
        props.finalizingTime = finalizingTimeSeconds

    }

    // MARK: - Prop Setters Helpers
    private func setTargetSizeAndRatio(bcProfile: SwitcherStreamSettings?) {
        var targetSize: src_conf_size_t = .init(width: 0, height: 0)
        if let bcProfile {
            targetSize.width = Int32(bcProfile.videoFrameWidthNumber?.intValue ?? 0)
            targetSize.width = Int32(bcProfile.videoFrameHeightNumber?.intValue ?? 0)
        } else {
            targetSize = BCProfileLibrary.shared.defaultProgramFrameSize
        }

        targetSize = OutputProfile.shared.sizeConformingToTargetAspectRatio(targetSize)
        props.targetFormat = "\(targetSize.width) x \(targetSize.height)"

        let targetAspectRatio = OutputProfile.shared.targetAspectRatio
        props.targetAspectRatio = TargetAspectRatioUtil.ratioToString(targetAspectRatio)
    }

    private func setHost(bcProfile: SwitcherStreamSettings?) -> Bool {
        var isRtmpUsed: Bool = false
        if let urlString = bcProfile?.url,
           let url = URL(string: urlString) {
            isRtmpUsed = url.scheme?.lowercased() == "rtmps"
            var host: String = url.host() ?? ""
            if let suffix: Character = host.last,
                suffix >= "0" && suffix <= "9" {
                if host.hasPrefix("192.168.") {
                    host = "192.168.x.x"
                } else if host.hasPrefix("10.0") {
                    host = "10.x.x.x"
                } else {
                    if let firstDotRange = host.range(of: ".") {
                        let nextSearchStart = host.index(after: firstDotRange.lowerBound)
                        let searchRange = nextSearchStart..<host.endIndex
                        if let secondDotRange = host.range(of: ".", range: searchRange) {
                            host = "\(host[..<secondDotRange.lowerBound]).*"
                        }
                    }
                }
            } else {
                let fullRange = host.startIndex..<host.endIndex

                if let lastDotRange = host.range(of: ".", options: .backwards, range: fullRange) {
                    let beforeLastDotRange = host.startIndex..<lastDotRange.lowerBound
                    if let secondLastDotRange = host.range(of: ".",
                                                           options: .backwards,
                                                           range: beforeLastDotRange) {
                        host = "*" + host[secondLastDotRange.lowerBound...]
                    }
                }
            }
            props.broadcastScheme = url.scheme
            props.broadcastDest = host
        }
        return isRtmpUsed
    }

    private func setIsocamMode() {
        let isocamMode = RecProfile.shared.isocamMode
        let director: Bool = isocamMode == .full
        props.director = director

        if director {
            let directorSize: src_conf_size_t = RecProfile.shared.cameraFrameSize
            let directorFormat = "\(directorSize.width) x \(directorSize.height)"
            props.directorFormat = directorFormat
        }

        let isocamModeStr: String = switch isocamMode {
        case .full: "director"
        case .remoteCameraOnly: "remote"
        default: "none"
        }
        props.isocamRec = isocamModeStr
    }

    private func setBroadcastsErrors() {
        props.progBroadcastErrorCount = progBroadcastErrorCount

        if let progBroadcastFirstError {
            props.progBroadcastError = progBroadcastFirstError.localizedMessage
            props.progBroadcastErrorCode = self.getErrorCode(progBroadcastFirstError)
        }
        if let progBroadcastFinalError {
            props.progBroadcastFinalError = progBroadcastFinalError.localizedMessage
            props.progBroadcastFinalErrorCode = self.getErrorCode(progBroadcastFinalError)
        }
    }

    private func getErrorCode(_ prodError: ProdError) -> String? {
        let name: String? = prodError.errorName
        let subname: String? = prodError.errorSubname
        if let name {
            let code: String
            if let subname {
                code = "\(name).\(subname)"
            } else {
                code = name
            }
            return code
        }
        return nil
    }

    private func setUserInfo(rtmps: Bool) {
        let userInfo: SwiUserInfo = SSNUserAccount.shared.userInfo
        if rtmps && userInfo.hasAlternativeRtmpsEnabled {
            props.alternativeRtmps = true
        }

        if rtmps && userInfo.hasInsecureRtmpsEnabled {
            props.insecureRtmps = true
        }
    }

    private func setCamCount(mainMixer: any MainMixerProtocol) {
        var camCount: Int = 0
        var panTiltCamCount: Int = 0
        var localSeemoCamCount: Int = 0
        var remoteSeemoCamCount: Int = 0
        var localExtCamCount: Int = 0
        var remoteExtCamCount: Int = 0
        for ch in mainMixer.channelCatalog.sortedLiveChannels {
            if let camera = ch.camera, camera.hasVideo == true {
                camCount += 1

                let ptzCap = camera.ptzCtrlCap
                if (ptzCap.pointee.flags & (PTZ_CAP_F_PAN_SPD | PTZ_CAP_F_TILT_SPD)) != 0 {
                    panTiltCamCount += 1
                }

                // early SeeMo versions had a "-seemo" suffix in the productId
                if (camera.productId?.hasPrefix("-seemo") ?? false) || camera.sourceType == .seemo {
                    if camera.isRemote {
                        remoteSeemoCamCount += 1
                    } else {
                        localSeemoCamCount += 1
                    }
                } else if camera.sourceType == .external {
                    if camera.isRemote {
                        remoteExtCamCount += 1
                    } else {
                        localExtCamCount += 1
                    }
                }
            }
        }

        props.camCount = camCount

        if panTiltCamCount > 0 {
            props.panTiltCamCount = panTiltCamCount
        }
        if localSeemoCamCount > 0 {
            props.localSeemoCamCount = localSeemoCamCount
        }
        if remoteSeemoCamCount > 0 {
            props.remoteSeemoCamCount = remoteSeemoCamCount
        }
        if localExtCamCount > 0 {
            props.localExtCamCount = localExtCamCount
        }
        if remoteExtCamCount > 0 {
            props.remoteExtCamCount = remoteExtCamCount
        }
    }

    private func setCutStats() {
        /*
         int total_non_pro_cuts = cut_stat.counters.elem_tap
         + cut_stat.counters.key
         + cut_stat.counters.multiview_completion
         + cut_stat.counters.quick_multiview
         + cut_stat.counters.multiview_menu_item;
         */
        let totalProCuts: Int32 = cut_stat.counters.pro_source_double_tap +
        cut_stat.counters.pro_scene_double_tap
        // + cut_stat.counters.pro_overlay_double_tap
        + cut_stat.counters.pro_live_button
        + cut_stat.counters.pro_live_key
        + cut_stat.counters.pro_direct_key
        + cut_stat.counters.pro_quick_multiview

        if UIDevice.current.isPad {
            let cutType: String
            if cut_stat.counters.swap_multiview > 0 {
                cutType = "swap-mv"
            } else if cut_stat.counters.swap > 0 {
                cutType = "swap"
            } else if totalProCuts > 0 {
                cutType = "pro"
            } else {
                cutType = "simple"
            }
            props.padCutType = cutType
        }

        let totalKbCuts: Int32 = cut_stat.counters.key
        + cut_stat.counters.pro_live_key
        + cut_stat.counters.pro_direct_key

        props.keyboardUse = totalKbCuts > 0
        props.audioClips = cut_stat.counters.audio_tap
    }

    private func setStreamLoggerStats(prodManager: ProdManager, duration: Int64) {
        if let streamLogger = prodManager.streamLogger,
           duration >= Int64(streamLogger.startDelay) + 120 {
            let cameraLogs = prodManager.streamLogger?.cameraLogs
            let duration = prodManager.streamLogger?.logDuration
            let resyncDelay = prodManager.mainMixer.resyncDelay
            let resyncDelayChangeCount = prodManager.streamLogger?.resyncDelayChangeCount

            props.cameraLogs = cameraLogs
            props.cameraLogDuration = duration
            props.cameraLogResyncDelay = resyncDelay
            props.cameraLogResyncDelayChangeCount = resyncDelayChangeCount

            if let streamLoggerData = try? JSONEncoder().encode(cameraLogs),
               let streamLoggerDataString = String(data: streamLoggerData, encoding: .utf8) {
                logger.info("cameraLogs=\(streamLoggerDataString)")
            }

            if let duration {
                logger.info("cameraLogDuration=\(duration)")
            }

            logger.info("cameraLogResyncDelay=\(resyncDelay)")

            if let resyncDelayChangeCount {
                logger.info("cameraLogResyncDelayChangeCount=\(resyncDelayChangeCount)")
            }
        }
    }

    private func setProductionStats(prodManager: ProdManager) {
        let assetStats = AssetStats.shared.assetStats
        props.assetTypes = assetStats.assetTypes
        props.totalCuts = assetStats.totalCuts

        let groupStats = AssetStats.shared.groupStats(mainMixer: prodManager.mainMixer)
        props.totalGroupCount = groupStats.totalGroupCount
        props.involvedGroupCount = groupStats.involvedGroupCount

        if let assetStatsData = try? JSONEncoder().encode(assetStats),
           let assetStatsString = String(data: assetStatsData, encoding: .utf8) {
            logger.info("assetsState\(assetStatsString.utf8CString)")
        }
    }

    private func setHealthStats(prodManager: ProdManager) {
        let healthStats = prodManager.healthMonitor?.productionHealthStats
        props.maxCpuLoad = healthStats?.maxCpuLoad
        props.avgCpuLoad = healthStats?.avgCpuLoad
        props.maxRamUsage = healthStats?.maxRamUsage
        props.avgRamUsage = healthStats?.avgRamUsage
        props.totalDeviceRam = healthStats?.totalDeviceRam
    }

    // MARK: - Report
    func reportEvents(programRec: Bool,
                      programStream: Bool,
                      bcProfileType: Int) {

        dump(props)
        analytics.reportEvent(name: "Prod Summary", properties: props.toDictionary())
        analytics.reportEvent(name: "Created Any Video")

        if programRec && programStream {
            analytics.reportEvent(name: "Recorded and Broadcasted Video")
        } else if programRec {
            analytics.reportEvent(name: "Recorded Video")
        } else if programStream {
            analytics.reportEvent(name: "Broadcasted Video")
        }

        reportToolEvents()

#if CONFIG_SWITCHER
        if let type = BCProfileType(rawValue: bcProfileType) {
            switch type {
            case .customRtmp:
                analytics.reportEvent(name: "Used Custom RTMP")
            case .facebook:
                analytics.reportEvent(name: "Streamed to Facebook")
            case .youTube:
                analytics.reportEvent(name: "Streamed to YouTube")
            default:
                break
            }
        }
#endif

        analytics.flush()
    }

    private func reportToolEvents() {
        let usedTools = toolUse?.usedIds ?? []
        if usedTools.contains("cloudy-VideoChat") {
            analytics.reportEvent(name: "Used Video Chat in Prod")
        }
        if usedTools.contains("cloudy-Scoreboard") {
            analytics.reportEvent(name: "Used Scoreboard in Prod")
        }
    }
}
