//
//  StreamStatePoller.swift
//  Cap-iOS
//
//  Created by <PERSON> on 7/1/25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation

protocol StreamStatePollerProtocol: AnyObject {
    var isStreamValidationInProgress: Bool { get }

    func addDelegate(_ delegate: any StreamStatePollerDelegate)
    func removeDelegate(_ delegate: any StreamStatePollerDelegate)
    func start()
    func stop()
}

class StreamStatePoller: StreamStatePollerProtocol {

    let url: URL
    var ssnAuth: Bool
    var task: Task<Void, any Error>?
    var longStreamValidation: Bool = false
    var delegateList: NSMutableArray = .init()

    init(url: URL, ssnAuth: Bool) {
        self.url = url
        self.ssnAuth = ssnAuth
    }

    func addDelegate(_ delegate: any StreamStatePollerDelegate) {
        delegateList.add(delegate)
    }

    func removeDelegate(_ delegate: any StreamStatePollerDelegate) {
        delegateList.remove(delegate)
    }

    var isStreamValidationInProgress: Bool {
        return task != nil
    }

    func start() {
        self.stop()
        self.startRequest()
    }

    func stop() {
        if let task {
            task.cancel()
            self.task = nil
        }

        if longStreamValidation {
            fireValidationDidEnd()
        }
    }

    private func startRequest() {
        task = Task {
            do {
                let result = try await StreamStatePollerAPI.shared.poll(url: url,
                                                                        isAuthRequired: ssnAuth)

                if let errorCode = result.errorCode {
                    processError(error: StreamStatePoller.errorFromErrorCode(errorCode: errorCode))
                    return
                }

                if let displayValidationProgress = result.displayValidationProgress,
                displayValidationProgress {
                    await processProgress(silentValidation: result.silentValidation ?? false)
                } else {
                    processSuccess()
                }

                self.task = nil
            } catch {
                processError(error: ProdError(errorCode: .badHTTPResponseCode))
            }
        }
    }

    private func processSuccess() {
        if longStreamValidation {
            fireValidationDidEnd()
        }

        for delegate in delegateList {
            if let del = delegate as? (any StreamStatePollerDelegate) {
                del.streamValidationDidSucceed()
            }
        }
    }

    private func processProgress(silentValidation: Bool) async {
        if !longStreamValidation {
            longStreamValidation = true

            for delegate in delegateList {
                if let del = delegate as? (any StreamStatePollerDelegate) {
                    del.streamValidationDidBegin(silently: silentValidation)
                }
            }
        }
        await self.scheduleNextRequest()
    }

    private func processError(error: ProdError) {
        if longStreamValidation {
            fireValidationDidEnd()
        }

        for delegate in delegateList {
            if let del = delegate as? (any StreamStatePollerDelegate) {
                del.streamValidationDidFail(error: error)
            }
        }
    }

    private func fireValidationDidEnd() {
        longStreamValidation = false
        for delegate in delegateList {
            if let del = delegate as? (any StreamStatePollerDelegate) {
                del.streamValidationDidEnd()
            }
        }
    }

    private func scheduleNextRequest() async {
        try? await Task.sleep(nanoseconds: 4_000_000_000)
        do {
            try Task.checkCancellation()
            self.startRequest()
        } catch { } // Do not start a new request if cancelled
    }

    static func errorFromErrorCode(errorCode: String) -> ProdError {
        let code: ProdErrorCode = switch errorCode {
        case "invalidAccount": .invalidAccount
        case "invalidEvent": .invalidEvent
        case "invalidIngestionSettings": .invalidIngestionSettings
        default: .streamingProviderGenericError
        }
        return ProdError(errorCode: code)
    }
}
