//
//  ProdManager+ProdTargetDelegate.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 6/2/25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation

// MARK: - ProdTargetDelegate
@MainActor extension ProdManager: @preconcurrency ProdTargetDelegate {
    public func prodTargetStateDidChange(_ target: ProdTarget!) {
        self.progressStateChanges()
    }

    public func prodTargetStartTimeBecameAvailable(_ target: ProdTarget!) {
        if target === programRecorder, let markers = markersRecorder {
            markers.start(time: programRecorder!.startTime)
            mainMixer.addMarkersRecorder(markers)
        }
        delegate?.prodManagerStartTimeBecameAvailable()
    }

    public func prodTargetGoodStart(_ target: ProdTarget!) {
        self.checkProgramStreamSanity()
    }
}
