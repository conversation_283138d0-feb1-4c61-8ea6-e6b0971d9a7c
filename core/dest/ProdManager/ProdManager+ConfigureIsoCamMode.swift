//
//  ProdManager+ConfigureIsoCamMode.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 6/4/25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation

@MainActor extension ProdManager {
    func configureIsoCamMode(isocamRecOptions: inout M2RecOption) -> RecProfileIsocamMode {
        let isocamMode = RecProfile.shared.isocamMode

        switch isocamMode {
        case .none:
            isocamRec = false
        case .remoteCameraOnly:
            // record on all camera mode
            isocamRec = true
            isocamRecOptions.insert(.remoteCameraOnly)
            isocamRecOptions.remove(.applyRecProfileBitRate)
        case .full:
            // director mode: check that we are not starting to record on a local camera
            for ch in mainMixer.channelCatalog.sortedLiveChannels {
                let cam = ch.camera
                if cam?.providesVideo == true && (cam is LocalCamera || cam is HybridCamera) {
                    break
                }
            }
            isocamRec = true
        @unknown default:
            break
        }

        return isocamMode
    }
}
