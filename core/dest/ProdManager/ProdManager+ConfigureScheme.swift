//
//  ProdManager+ConfigureScheme.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 6/4/25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation

@MainActor extension ProdManager {
    func configureScheme(bcProfile: SwitcherStreamSettings,
                         isocamMode: RecProfileIsocamMode,
                         programStreamUrl: inout URL?,
                         isocamStreamUrl: inout URL?,
                         url: URL,
                         isocamRecConf: RecConf) {

        // TODO: check args

        let scheme: String = url.scheme!.lowercased()

        if scheme == "hls-up" || scheme == "rtmp" || scheme == "rtmps" || scheme == "dash1" {
            configureRtmpOrDashScheme(programStreamUrl: &programStreamUrl, url: url)
            return
        }

#if CONFIG_SWITCHER_B1
        if scheme == "hls-isocam" {
            configureIsoCamScheme(bcProfile: bcProfile,
                                  isocamMode: isocamMode,
                                  isocamStreamUrl: &isocamStreamUrl,
                                  url: url,
                                  isocamRecConf: isocamRecConf)
            return
        }
#endif

        startError = ProdError(errorCode: .badUrlScheme)
        self.progressStateChanges()
        return
    }

    private func configureRtmpOrDashScheme(programStreamUrl: inout URL?, url: URL) {
        programRec = true
        programStream = true
        programStreamUrl = url
    }

    private func configureIsoCamScheme(bcProfile: SwitcherStreamSettings,
                                       isocamMode: RecProfileIsocamMode,
                                       isocamStreamUrl: inout URL?,
                                       url: URL,
                                       isocamRecConf: RecConf) {
        var isocamRecConf: RecConf = isocamRecConf
        if isocamMode == .remoteCameraOnly && programRec {
            startError = ProdError(errorCode: .dualStreamingNotSupported)
            self.progressStateChanges()
            return
        }
        isocamRec = true
        isocamStream = true
        isocamStreamUrl = url
        isocamRecConf.v_fragment_interval = 6
        isocamStreamerConsumesSource = true

        var number: NSNumber
        number = bcProfile.audioBitRateNumber!
        isocamRecConf.a_bit_rate = number.int32Value
        number = bcProfile.videoBitRateNumber!
        isocamRecConf.v_bit_rate = number.int32Value
        isocamRecConf.a_enc = MFLOW_CODEC_AAC
    }
}
