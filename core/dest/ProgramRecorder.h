//
//  ProgramRecorder.h
//  Switcher
//
//  Created by <PERSON><PERSON> on September 3, 2013.
//  Copyright (c) 2013 Switcher Inc. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "AVProvider.h"
#import "ProdTarget.h"
#import "RLSourceRecorder.h"
#import "RLAudioEngine.h"


@class OutputProfile;
@class SwitcherStreamSettings;
@class VOutputPump;
@protocol ProgramRecorderProtocol;

@interface ProgramRecorder : ProdRecorder <RLGrowingSource, ProgramRecorderProtocol>

@property (nonatomic) SwitcherStreamSettings * _Nullable bcProfile;
@property (nonatomic) NSString * _Nullable name;
@property (nonatomic) NSString * _Nullable mid;
@property (nonatomic) NSString * _Nullable gid;
@property (nonatomic, nullable) NSString *broadcastId;
@property (nonatomic) OutputProfile * _Nullable outputProfile;
@property (nonatomic) VOutputPump * _Nullable vmixerOutput;
@property (nonatomic) AudioMixer * _Nullable audioMixer;
@property (nonatomic) REC_CONF recConf;
@property (nonatomic, readonly) NSString * _Nullable filePath;
@property (nonatomic, readonly) BOOL liveTranscriptionStarted;

- (void)start;
- (void)stop;

@end
