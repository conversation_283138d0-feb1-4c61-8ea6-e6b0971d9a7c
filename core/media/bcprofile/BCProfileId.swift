//
//  BCProfileId.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 21.03.24.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//

import Foundation

@objcMembers public class BCProfileId: NSObject {
    var type: BCProfileType
    var name: String?
    var customRtmpId: String?

    init(type: BCProfileType, name: String?, customRtmpId: String? = nil) {
        self.type = type
        self.name = name
        self.customRtmpId = customRtmpId
    }

    func toString() -> String {
        let typeString = BCProfileTypeWrapper.toString(type)
        if let name = name {
            return "\(typeString):\(name)"
        } else {
            return typeString
        }
    }

    public override func isEqual(_ object: Any?) -> Bool {
        if let other = object as? BCProfileId {
            return type == other.type && name == other.name && customRtmpId == other.customRtmpId
        }
        return false
    }
}
