//
//  BCProfileLibrary.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 20.03.24.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//

import Foundation

@MainActor
protocol BCProfileLibraryChangeDelegate: AnyObject {
    /**
     * Called when the active profile ID changed or its properties
     * changed.
     * To be notified also when profiles are added and removed via iTunes,
     * please start list monitoring by calling startListMonitoring.
     */
    func activeBCProfileDidChange(onlyStreamingQuality: Bool)

    /**
     * Called when an inactive profile ID changed or its properties
     * changed.
     * To be notified also when profiles are added and removed via iTunes,
     * please start list monitoring by calling startListMonitoring.
     */
    func inactiveBCProfilesDidChange()

    func programRecordingDidChange()

    func autoGeneratedEventDidComplete()
}

@MainActor
protocol BCProfileLibraryTypeListDelegate: AnyObject {
    func bcProfileTypeListDidChange()
}

enum BCProfileLibraryError: Error {
    case autoSaveDisabled
}

@MainActor
@objc public class BCProfileLibrary: NSObject,
                                     StreamingProviderListDelegate,
                                     BCProfileLibraryProtocol,
                                     ObservableObject {
    private lazy var logger = LsLogger(subsystem: "swi.core", category: "BCProfileLibrary")

    @objc(sharedInstance) public static let shared = BCProfileLibrary(streamingProviderList: StreamingProviderList
        .shared,
                                                                      outputProfile: OutputProfile.shared,
                                                                      userDefaults: UserDefaults.standard,
                                                                      userAccount: SSNUserAccount.shared,
                                                                      userInfo: SSNUserAccount.shared.userInfo,
                                                                      broadcastsAPI: BroadcastsAPI.shared,
                                                                      streamSettingsAPI: SwitcherStreamSettingsAPI
                                                                      .shared)

    private let streamingProviderList: any StreamingProviderListProtocol
    private let outputProfile: any OutputProfileProtocol
    private let userDefaults: any UserDefaultsProtocol
    private let userAccount: any SSNUserAccountProtocol
    private let userInfo: any SwiUserInfoProtocol
    private let broadcastsAPI: any BroadcastsAPIProtocol
    private let streamSettingsAPI: any SwitcherStreamSettingsAPIProtocol

    // User Defaults keys
    static let profileTypeKey = "CurrentBroadcastingProfileType"
    static let profileNameKey = "CurrentBroadcastingProfileName"
    static let customRtmpIdKey = "CurrentCustomRtmpId"
    static let videoFormatWidthKey = "bcprofile-def-video-format-width"
    static let videoFormatHeightKey = "bcprofile-def-video-format-height"
    static let programRecordingKey = "bcprofile-program-recording"

    @Published var customRtmpSettings: [SwitcherStreamSettings] = []
    @Published var customRtmpSettingsRefreshInProgress: Bool = false
    @Published var customRtmpSettingsError = false

    private static func profileNameKey(withType type: BCProfileType) -> String {
        return "\(BCProfileLibrary.profileNameKey).\(type.toString())"
    }

    init(streamingProviderList: any StreamingProviderListProtocol,
         outputProfile: any OutputProfileProtocol,
         userDefaults: any UserDefaultsProtocol,
         userAccount: any SSNUserAccountProtocol,
         userInfo: any SwiUserInfoProtocol,
         broadcastsAPI: any BroadcastsAPIProtocol,
         streamSettingsAPI: any SwitcherStreamSettingsAPIProtocol) {
        self.streamingProviderList = streamingProviderList
        self.outputProfile = outputProfile
        self.userAccount = userAccount
        self.userDefaults = userDefaults
        self.userInfo = userInfo
        self.broadcastsAPI = broadcastsAPI
        self.streamSettingsAPI = streamSettingsAPI

        super.init()

        let defaultSize = defaultProgramFrameSize
        autoSaveAttributes.selectedProfile?.videoFrameWidth = Int(defaultSize.width)
        autoSaveAttributes.selectedProfile?.videoFrameHeight = Int(defaultSize.height)

#if CONFIG_SWITCHER_CLOUD
        streamingProviderList.addDelegate(self)
        streamingProviderListDidChange()
#else
        _attributesList.addObject(BCProfileLibraryTypeAttributes.generic())
#endif
        userDefaults.register(defaults: ["bcprofile-program-recording": true])

        Task { await refreshCustomRtmpSettings()}
    }

    public func refreshCustomRtmpSettings() async {
        guard !customRtmpSettingsRefreshInProgress else { return }
        customRtmpSettingsRefreshInProgress = true
        do {
            customRtmpSettings = sortCustomRtmpSettings(try await streamSettingsAPI.getSettings())
            customRtmpSettingsRefreshInProgress = false
            customRtmpSettingsError = false
        } catch {
            customRtmpSettingsError = true
            customRtmpSettings = []
            customRtmpSettingsRefreshInProgress = false
        }
    }

    private func sortCustomRtmpSettings(_ customRtmpSettings: [SwitcherStreamSettings]) -> [SwitcherStreamSettings] {
        return customRtmpSettings.sorted { $0.updatedOn ?? Date() > $1.updatedOn ?? Date() }
    }

    private var transactions = 0

    private func beginTransaction() {
        transactions += 1
    }

    private func endTransaction() {
        transactions -= 1

        if transactions == 0 {
            if notifyActiveProfileChangeLater {
                fireActiveBCProfileDidChange()
                notifyActiveProfileChangeLater = false
            }
            if notifyInactiveProfileChangeLater {
                fireInactiveBCProfilesDidChange()
                notifyInactiveProfileChangeLater = false
            }
            if notifyProgramRecordingChangeLater {
                fireProgramRecordingDidChange()
                notifyProgramRecordingChangeLater = false
            }
        }
    }

    enum PracticeModeState {
        case idle
        case inProgress
        case ready
        case error
    }
    var isPracticeModeReady: Bool {
        return practiceModeState == .ready
    }
    @Published var practiceModeState: PracticeModeState = .idle
    var isAutoSaveReady: Bool = false
    var currentProfileType: BCProfileType {
        get {
            guard let typeString = userDefaults.string(forKey: BCProfileLibrary.profileTypeKey) else {
                return .recordOnly
            }

            let type = BCProfileType.fromString(typeString)
            // in case of known incompatible aspect ratio, default to record only
            if let attr = attributes(ofProfileType: type),
               !attr.aspectRatioCompatibilitySet.isCompatibleWith(outputProfile.targetAspectRatio) {
                return .recordOnly
            }

            return type
        }
        set {
            if newValue != currentProfileType {
                userDefaults.setValue(newValue.toString(), forKey: BCProfileLibrary.profileTypeKey)
                activeBCProfileDidChange()
            }

            if newValue.supportsAutoGeneration {
                let name = MediaUtil.createRecordingName(newValue == .practice)
                if newValue == .practice {
                    practiceModeState = .inProgress
                }

                Task { @MainActor in
                    let success = (try? await createAutoGeneratedEvent(
                        name: name,
                        withTimeoutSecs: newValue.eventCreationTimeoutDuration)) != nil

                    if newValue == .practice {
                        practiceModeState = success ? .ready : .error
                    } else {
                        isAutoSaveReady = success
                    }
                    fireAutoGeneratedEventDidComplete()
                }
            }
        }
    }

    private func currentUnsafeProfileId(withType type: BCProfileType) -> BCProfileId? {
        guard type != .recordOnly,
              let attr = attributes(ofProfileType: type) else {
            return nil
        }

        if attr.type.hasProfile {
            if let name = attr.selectedChannelName,
               attr.selectedProfile != nil {
                return BCProfileId(type: type, name: name)
            }
        } else if attr.type == .customRtmp {
            if let id = userDefaults.string(forKey: BCProfileLibrary.customRtmpIdKey) {
                return BCProfileId(type: type, name: nil, customRtmpId: id)
            }

            // Retro-compatibility
            let key = BCProfileLibrary.profileNameKey(withType: type)
            if let name = userDefaults.string(forKey: key) {
                return BCProfileId(type: type, name: name, customRtmpId: nil)
            }
        }
        return nil
    }

    public func currentProfileId(_ type: BCProfileType) -> BCProfileId? {
        guard let profileId = currentUnsafeProfileId(withType: type),
              let attr = attributes(ofProfileType: type) else {
            return nil
        }

        if attr.type.hasProfile {
            return profileId
        } else if attr.type == .customRtmp {
            // Check for unique ID first, if not available, check for channel name
            if let customRtmpId = profileId.customRtmpId,
               let settings = getCustomRtmpSettings(withId: customRtmpId) {
                return BCProfileId(type: .customRtmp,
                                   name: settings.channelName,
                                   customRtmpId: settings.id)
            } else if profileId.customRtmpId == nil,
                      let name = profileId.name,
                      let settings = getCustomRtmpSettings(withName: name) {
                return BCProfileId(type: .customRtmp,
                                   name: name,
                                   customRtmpId: settings.id)
            }
        }
        return nil
    }

    @objc public var currentProfileId: BCProfileId? {
        currentProfileId(currentProfileType)
    }

    public func setCurrentProfileId(_ profileId: BCProfileId, setCurrentType: Bool) {
        let attr = attributes(ofProfileType: profileId.type)

        beginTransaction()

        if setCurrentType {
            currentProfileType = profileId.type
        }

        if profileId.type == .recordOnly || profileId == currentProfileId(profileId.type) {
            endTransaction()
            return
        }

        if attr?.type == .customRtmp {
            userDefaults.setValue(profileId.customRtmpId, forKey: Self.customRtmpIdKey)
        }

        if setCurrentType || profileId.type == currentProfileType {
            activeBCProfileDidChange()
        } else {
            inactiveBCProfileDidChange()
        }

        endTransaction()
    }

    private func unsetCurrentProfileId(withType type: BCProfileType) {
        guard type != .recordOnly else {
            return
        }

        let attr = attributes(ofProfileType: type)

        if attr?.type == .customRtmp {
            userDefaults.removeObject(forKey: Self.customRtmpIdKey)
        }

        if type == currentProfileType {
            activeBCProfileDidChange()
        } else {
            inactiveBCProfileDidChange()
        }
    }

    @objc public func profile(withId profileId: BCProfileId?) -> SwitcherStreamSettings? {
        guard let profileId = profileId,
              profileId.type != .recordOnly,
              let attr = attributes(ofProfileType: profileId.type) else {
            return nil
        }

        if attr.type.hasProfile {
            if profileId.name == attr.selectedChannelName {
                return attr.selectedProfile
            }
        } else if attr.type == .customRtmp,
                  let customRtmpId = profileId.customRtmpId {
            return getCustomRtmpSettings(withId: customRtmpId)
        }

        return nil
    }

    public func getCustomRtmpSettings(withId id: String) -> SwitcherStreamSettings? {
        return customRtmpSettings.first(where: {$0.id == id})
    }

    public func getCustomRtmpSettings(withName name: String) -> SwitcherStreamSettings? {
        return customRtmpSettings.first(where: {$0.channelName == name})
    }

    /**
     * Can be used to edit or add a custom RTMP profile
     * Return true if the profile has been changed.
     */
    @discardableResult
    @MainActor
    public func setCustomRtmpSettings(_ streamSettings: SwitcherStreamSettings) async -> Bool {
        let profileId = BCProfileId(type: .customRtmp,
                                    name: streamSettings.channelName,
                                    customRtmpId: streamSettings.id)
        let oldProfile = profile(withId: profileId)
        if streamSettings == oldProfile {
            return false
        }

        do {
            customRtmpSettingsRefreshInProgress = true
            customRtmpSettings = sortCustomRtmpSettings(try await streamSettingsAPI.updateSettings([streamSettings]))
            customRtmpSettingsRefreshInProgress = false
        } catch {
            return false
        }

        if profileId.customRtmpId == currentProfileId?.customRtmpId {
            activeBCProfileDidChange()
        } else {
            inactiveBCProfileDidChange()
        }

        return true
    }

    @MainActor
    public func removeCustomRtmpSettings(_ streamSettings: SwitcherStreamSettings) async -> Bool {
        logger.info("Remove profile \(streamSettings.channelName ?? "nil")")

        streamSettings.isDeleted = true
        do {
            customRtmpSettingsRefreshInProgress = true
            customRtmpSettings = sortCustomRtmpSettings(try await streamSettingsAPI.updateSettings([streamSettings]))
            customRtmpSettingsRefreshInProgress = false
        } catch {
            customRtmpSettingsRefreshInProgress = false
            return false
        }

        let changeSelection = streamSettings.id == currentProfileId(.customRtmp)?.customRtmpId
        if changeSelection {
            unsetCurrentProfileId(withType: .customRtmp)
        }

        if currentProfileType == .customRtmp && changeSelection {
            activeBCProfileDidChange()
        } else {
            inactiveBCProfileDidChange()
        }

        return true
    }

    @objc public var defaultProgramFrameSize: SourceConf.Size {
        get {
            let ret = SourceConf.Size(width: Int32(userDefaults.integer(forKey: BCProfileLibrary.videoFormatWidthKey)),
                                      height: Int32(userDefaults
                                      .integer(forKey: BCProfileLibrary.videoFormatHeightKey)))

            if ret.width == 0 || ret.height == 0 {
                return SwitcherStreamSettings.defaultVideoFrameSize
            }

            return ret
        }
        set {
            let def = defaultProgramFrameSize
            if newValue.width == def.width && newValue.height == def.height {
                return
            }

            userDefaults.setValue(newValue.width, forKey: BCProfileLibrary.videoFormatWidthKey)
            userDefaults.setValue(newValue.height, forKey: BCProfileLibrary.videoFormatHeightKey)

            autoSaveAttributes.selectedProfile?.videoFrameWidth = Int(newValue.width)
            autoSaveAttributes.selectedProfile?.videoFrameHeight = Int(newValue.height)

            if currentProfileId == nil {
                inactiveBCProfileDidChange()
            } else {
                activeBCProfileDidChange()
            }
        }
    }

    // Use a private variable to avoid update loops with activeBCProfileDidChange()
    private var _streamingFrameSize: SourceConf.Size = SourceConf.Size(
        width: Int32(SwitcherStreamSettings.defaultVideoFrameWidth),
        height: Int32(SwitcherStreamSettings.defaultVideoFrameHeight)
    )
    var streamingFrameSize: SourceConf.Size {
        _streamingFrameSize
    }

    func setStreamingFrameSize(_ size: SourceConf.Size) {
        if size.width != _streamingFrameSize.width || size.height != _streamingFrameSize.height {
            _streamingFrameSize = size
            if currentProfileType == .multiStreaming {
                activeBCProfileDidChange(refreshStreamingFrameSize: false)
            } else {
                inactiveBCProfileDidChange()
            }
        }
    }

    private func refreshStreamingFrameSize() {
        if let streamingProfile = attributes(ofProfileType: .multiStreaming)?.selectedProfile,
           let width = streamingProfile.videoFrameWidth,
           let height = streamingProfile.videoFrameHeight {
            _streamingFrameSize = .init(width: Int32(width), height: Int32(height))
        }
    }

    var programRecording: Bool {
        get {
            if currentProfileType == .practice {
                return false
            }
            if currentProfileType == BCProfileType.recordingType() {
                return true
            }
            return userDefaults.bool(forKey: BCProfileLibrary.programRecordingKey)
        }
        set {
            if newValue == programRecording {
                return
            }

            userDefaults.setValue(newValue, forKey: BCProfileLibrary.programRecordingKey)

            programRecordingDidChange()
        }
    }

    private func validateProfile(_ profile: SwitcherStreamSettings) -> Bool {
        guard let width = profile.videoFrameWidth,
              let height = profile.videoFrameHeight else {
            return false
        }
        return width >= 16 && height >= 9
    }

    private var _attributesList: [BCProfileLibraryTypeAttributes] = []
    private(set) var autoSaveAttributes: BCProfileLibraryTypeAttributes = BCProfileLibraryTypeAttributes.autoSave()
    private(set) var practiceModeAttributes = BCProfileLibraryTypeAttributes.practiceMode()
    private(set) var practiceModeShareUrl: URL?

    var profileTypeList: [BCProfileType] {
        return _attributesList.map {$0.type}
    }

    public var profileTypeAttributeList: [BCProfileLibraryTypeAttributes] {
        return _attributesList
    }

    func attributes(ofProfileType type: BCProfileType) -> BCProfileLibraryTypeAttributes? {
        return _attributesList.first(where: {$0.type == type})
    }

    public func createAutoGeneratedEvent(name: String, withTimeoutSecs: Int?) async throws {
        let profileType = currentProfileType
        guard (userInfo.hasRecordOnlyAutoSave || profileType == .practice) && _attributesList.count > 0 else {
            throw BCProfileLibraryError.autoSaveDisabled
        }
        let defaultSize = defaultProgramFrameSize

        let eventCreationService = EventCreationService(userAccount: userAccount,
                                                        userInfo: userInfo,
                                                        bcProfileLib: self,
                                                        broadcastsAPI: broadcastsAPI,
                                                        streamSettingsAPI: streamSettingsAPI)

        let fetchTask = Task {
            let taskResult = try await eventCreationService.createAutoGeneratedEvent(broadcastId: UUID().uuidString,
                                                                                     type: profileType,
                                                                                     name: name,
                                                                                     width: Int(defaultSize.width),
                                                                                     height: Int(defaultSize.height))
            try Task.checkCancellation()
            return taskResult
        }

        var timeoutTask: Task<Void, any Error>?
        if let withTimeoutSecs {
            timeoutTask = Task {
                try await Task.sleep(nanoseconds: UInt64(withTimeoutSecs) * NSEC_PER_SEC)
                fetchTask.cancel()
            }
        }

        let creationResult = try await fetchTask.value

        // Update settings
        if let index = _attributesList.firstIndex(where: {$0.type == profileType}) {
            _attributesList[index] = creationResult.attributes
        }

        if profileType == .practice {
            practiceModeAttributes = creationResult.attributes
            if let urlString = creationResult.practiceUrl,
             let url = URL(string: urlString) {
              practiceModeShareUrl = url
             }
        } else if profileType == .recordOnlyAutoSave {
            autoSaveAttributes = creationResult.attributes
        }

        logger.info("\(profileType.toString()) stream ready")

        timeoutTask?.cancel()
    }

    func getSelectedLivestream(livestreams: [SwitcherLivestreamEvent],
                               currentId: BCProfileId?) -> SwitcherLivestreamEvent? {
        if let currentBroadcastId = self.profile(withId: currentId)?.broadcastId,
           let currentLivestream = livestreams.first(where: { currentBroadcastId == $0.id }) {
            return currentLivestream
        } else {
            return nil
        }
    }

    // MARK: - StreamingProviderListDelegate

    public func streamingProviderListRequestDidBegin() {}
    public func streamingProviderListRequestDidEnd() {}

    public func streamingProviderListDidChange() {
        var attributes: [BCProfileLibraryTypeAttributes] = []

        var customRtmpPresent = false

        // Force refresh the first time we load the attributes
        var activeChanged = _attributesList.isEmpty
        var inactiveChanged = _attributesList.isEmpty

        if userInfo.hasRecordOnlyAutoSave {
            attributes.append(autoSaveAttributes)
        }

        for entry in streamingProviderList.entries {
            let attr = BCProfileLibraryTypeAttributes(provider: entry)
            let active = attr.type == currentProfileType
            if attr.type == .customRtmp {
                customRtmpPresent = true
            } else if let currentProfileId = currentProfileId(attr.type) {
                let currentName = currentProfileId.name

                if attr.selectedChannelName != currentName ||
                    attr.selectedProfile?.isEqual(profile(withId: currentProfileId)) != true {
                    // the name or profile changed
                    if active {
                        activeChanged = true
                    } else {
                        inactiveChanged = true
                    }
                }
            }
            attributes.append(attr)
        }

        if !customRtmpPresent {
            attributes.append(BCProfileLibraryTypeAttributes.generic())
        }

        attributes.append(practiceModeAttributes)

        _attributesList = attributes
        fireTypeListDidChange()

        if activeChanged {
            activeBCProfileDidChange()
        }
        if inactiveChanged {
            inactiveBCProfileDidChange()
        }
    }

    // MARK: - Change Delegate

    private var changeDelegateList = WeakDelegateList<any BCProfileLibraryChangeDelegate>()
    private var notifyActiveProfileChangeLater = false
    private var notifyInactiveProfileChangeLater = false
    private var notifyProgramRecordingChangeLater = false

    func addChangeDelegate(_ delegate: any BCProfileLibraryChangeDelegate) {
        changeDelegateList.add(delegate)
    }

    func removeChangeDelegate(_ delegate: any BCProfileLibraryChangeDelegate) {
        changeDelegateList.remove(delegate)
    }

    private func activeBCProfileDidChange(refreshStreamingFrameSize: Bool = true) {
        if refreshStreamingFrameSize {
            self.refreshStreamingFrameSize()
        }
        if transactions != 0 {
            notifyActiveProfileChangeLater = true
        } else {
            // if refreshStreamingFrameSize is false, the output tab stream quality has been changed, in that case, the BC profile didn't change really and we notify we changed only the stream quality (we don't want to start again the speed test for example)
            fireActiveBCProfileDidChange(onlyStreamingQuality: !refreshStreamingFrameSize)
        }
    }

    private func fireActiveBCProfileDidChange(onlyStreamingQuality: Bool = false) {
        for delegate in changeDelegateList {
            delegate.activeBCProfileDidChange(onlyStreamingQuality: onlyStreamingQuality)
        }
    }

    private func inactiveBCProfileDidChange() {
        if transactions != 0 {
            notifyInactiveProfileChangeLater = true
        } else {
            fireInactiveBCProfilesDidChange()
        }
    }

    private func fireInactiveBCProfilesDidChange() {
        for delegate in changeDelegateList {
            delegate.inactiveBCProfilesDidChange()
        }
    }

    private func programRecordingDidChange() {
        if transactions != 0 {
            notifyProgramRecordingChangeLater = true
        } else {
            fireProgramRecordingDidChange()
        }
    }

    private func fireProgramRecordingDidChange() {
        for delegate in changeDelegateList {
            delegate.programRecordingDidChange()
        }
    }

    private func fireAutoGeneratedEventDidComplete() {
        for delegate in changeDelegateList {
            delegate.autoGeneratedEventDidComplete()
        }
    }

    // MARK: - Type List Delegate

    private var typeListDelegateList = WeakDelegateList<any BCProfileLibraryTypeListDelegate>()

    func addTypeListDelegate(_ delegate: any BCProfileLibraryTypeListDelegate) {
        typeListDelegateList.add(delegate)
    }

    func removeTypeListDelegate(_ delegate: any BCProfileLibraryTypeListDelegate) {
        typeListDelegateList.remove(delegate)
    }

    private func fireTypeListDidChange() {
        for delegate in typeListDelegateList {
            delegate.bcProfileTypeListDidChange()
        }
    }
}
