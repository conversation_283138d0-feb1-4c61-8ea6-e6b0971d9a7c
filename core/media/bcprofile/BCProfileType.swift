//
//  BCProfileType.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 20.03.24.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//

import Foundation
import SwiftUI

@objc public enum BCProfileType: Int, Identifiable {
    public var id: Int { self.rawValue }

    case none = -1
    case customRtmp = 0
    case youTube = 1 // Obsolete: Keep for retro-compatibility
    case facebook = 2 // Obsolete: Keep for retro-compatibility
    case recordOnly = 3
    case multiStreaming = 4
    case switcherPlayer = 5 // Obsolete: Keep for retro-compatibility
    case recordOnlyAutoSave = 6
    case practice = 7

    var isRecordingMode: Bool {
        self == .recordOnly || self == .recordOnlyAutoSave
    }

    var hasProfile: Bool {
        return switch self {
        case .multiStreaming, .recordOnlyAutoSave, .practice: true
        default: false
        }
    }

    var attributes: BCProfileLibraryTypeAttributes {
        return switch self {
        case .recordOnlyAutoSave: BCProfileLibraryTypeAttributes.autoSave()
        case .practice: BCProfileLibraryTypeAttributes.practiceMode()
        default: BCProfileLibraryTypeAttributes.generic()
        }
    }

    var apiBroadcastType: BroadcastType {
        return switch self {
        case .recordOnlyAutoSave: .autosave
        case .practice: .practice
        default: .default
        }
    }

    var supportsAutoGeneration: Bool {
        return switch self {
        case .recordOnlyAutoSave, .practice: true
        default: false
        }
    }

    var eventCreationTimeoutDuration: Int? {
        return switch self {
        case .recordOnlyAutoSave: 3
        default: nil
        }
    }
    
    var isSourceLoggingEnabled: Bool {
        return switch self {
        case .multiStreaming: true
        // Always enabled in dev mode
        default: ConfigurationDevelopmentManager.shared.isDevProfile
        }
    }

    func toString() -> String {
        return switch self {
        case .none: "none"
        case .customRtmp: "generic"
        case .youTube: "youtube"
        case .facebook: "facebook"
        case .recordOnly: "recordonly"
        case .multiStreaming: "simulcast"
        case .switcherPlayer: "switcherplayer"
        case .recordOnlyAutoSave: "recordonly-autosave"
        case .practice: "practice"
        }
    }

    static func fromString(_ string: String) -> BCProfileType {
        return switch string {
        case "generic": .customRtmp
        case "youtube": .youTube
        case "facebook": .facebook
        case "recordonly": .recordOnly
        case "simulcast": .multiStreaming
        case "switcherplayer": .switcherPlayer
        case "recordonly-autosave": .recordOnlyAutoSave
        case "practice": .practice
        default: .none
        }
    }

    static func recordingType(_ userInfo: SwiUserInfo = SSNUserAccount.shared.userInfo) -> BCProfileType {
        return userInfo.hasRecordOnlyAutoSave ? .recordOnlyAutoSave : .recordOnly
    }
}

@objc public class BCProfileTypeWrapper: NSObject {
    @objc static func toString(_ type: BCProfileType) -> String {
        return type.toString()
    }
    @objc static func fromString(_ string: String) -> BCProfileType {
        return BCProfileType.fromString(string)
    }
    @objc static func recordingType() -> BCProfileType {
        return BCProfileType.recordingType()
    }

    @objc static func supportsAutoGeneratedEvent(_ type: BCProfileType) -> Bool {
        return type.supportsAutoGeneration
    }
}

extension BCProfileType {
    static let broadcastModes: [BCProfileType] = [
        .multiStreaming,
        .practice
    ]

    var localizedName: LocalizedStringKey {
        switch self {
        case .multiStreaming:
            return "SwitcherNavigationActionSelectorView.streaming"
        case .practice:
            return "SwitcherNavigationActionSelectorView.practice"
        default:
            return ""
        }
    }
}
