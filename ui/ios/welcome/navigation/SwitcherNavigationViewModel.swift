//
//  SwitcherNavigationViewModel.swift
//  Cap-iOS
//
//  Created by <PERSON> on 15/06/2023.
//  Copyright © 2023 Switcher Inc. All rights reserved.
//

import Foundation
import SwiftUI
import Combine

#if CRASHLYTICS
import FirebaseCrashlytics
#endif

@MainActor
@Observable class SwitcherNavigationViewModel {

    static let shared = SwitcherNavigationViewModel()

    private let logger = LsLogger(subsystem: "swi.ui", category: "SwitcherNavigationViewModel")
    static let navigationLibraryLastSelectedKey = "navigation.library.last"
    @ObservationIgnored private var didNeverShowNews = true
    @ObservationIgnored public var cameraViewModel: CameraViewModel?

    var rootViewLink = SwitcherNavigationRootLink.main

    var navigationLink: SwitcherNavigationLink = SwitcherNavigationLink.home {
        willSet {
            navigationPath = NavigationPath()
        }
    }

    var navigationLibraryLink = SwitcherNavigationLibraryLink.local {
        didSet {
            UserDefaults.standard.set(navigationLibraryLink.rawValue, forKey: Self.navigationLibraryLastSelectedKey)
        }
    }

    var presentLibrarySelector = false {
        willSet {
            logger.info("presentLibrarySelector newValue: \(newValue)")
        }
    }

    var modalFullScreenPresentation: SwitcherNavigationActiveFullScreenCover? {
        willSet {
            if modalFullScreenPresentation != newValue {
                logger.info("modalFullScreenPresentation newValue: \(newValue?.id ?? "null") " +
                            "oldValue:\(modalFullScreenPresentation?.id ?? "null")")
            }
        }
    }

    var modalSheetPresentation: SwitcherNavigationActiveSheet? {
        willSet {
            if modalSheetPresentation != newValue {
                logger.info("modalSheetPresentation newValue: \(newValue?.id ?? "null") " +
                            "oldValue:\(modalSheetPresentation?.id ?? "null")")
            }
        }
    }

    var showPermissionAlert: Bool = false

    var showOrientationSelector: Bool = false

    var forceHideTabbar = false
    var videoLibraryUsageWorking = false

    var userIsLoggedIn: Bool = false

    var navigationPath = NavigationPath()

    var videoLibraryUsage = VideoLibraryUsageService(autoRefresh: false)

    @ObservationIgnored private var sinkUrlToImport: AnyCancellable?
    @ObservationIgnored public var urlsToImport: [URL] = []

    @ObservationIgnored private var sinkUrlToUpload: AnyCancellable?
    @ObservationIgnored public var urlsToUpload: [URL]?

    let permissionsManager = PermissionsManager.shared

    @ObservationIgnored var env: RLCapEnv?
    @ObservationIgnored var agentCoordinator: AgentCoordinator?

    @ObservationIgnored var showTabbar: Bool {
        return !forceHideTabbar && navigationPath.count == 0
    }

    private init() {
        UINavigationBar.appearance().largeTitleTextAttributes = [.font: UIFont.systemFont(ofSize: 22, weight: .bold)]

        self.sinkUrlToImport = AppFileImporter.shared.$urlsToImport.sink(receiveValue: { urls in
            if !urls.isEmpty && urls.count == AppFileImporter.shared.numberOfFilesToBeImported {
                self.urlsToImport = urls
                AppFileImporter.shared.urlsToImport.removeAll()
                self.refreshHome()
            }
        })

        self.sinkUrlToUpload = AppFileUploader.shared.$urlsToUpload.sink(receiveValue: { urls in
            if !urls.isEmpty {
                self.urlsToUpload = urls
                AppFileUploader.shared.urlsToUpload.removeAll()
                self.refreshHome()
            }
        })

        VideoLibraryUsageService.registerValidationNotifier(self)
        refreshHome()
        showDefaultLibrary()

        SSNUserAccount.shared.userInfo.addDelegate(self)
        SSNUserAccount.shared.addUserAccountDelegate(self)
        userAccountStateDidChange()

        if !permissionsManager.permissionsRequested {
            // The first time a user opens the app, ask for permissions with a sheet
            modalSheetPresentation = .permissions
        } else {
            // Returning user, check permissions status before building environment
            permissionsManager.askPermissions([.camera, .mic, .network, .speech]) {
                self.buildEnv()
            }
        }

        let nc = NotificationCenter.default
        nc.addObserver(self, selector: #selector(userLoggedOut), name: .switcherUserLoggedOut, object: nil)
        nc.addObserver(self, selector: #selector(userDidDeleteAccount), name: .switcherUserDeleteAccount, object: nil)
        nc.addObserver(self, selector: #selector(restartAVProvider),
                       name: .switcherShouldReloadCameraSettings, object: nil)

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.startBackgroundTasks()
        }

        self.rootViewLink = SSNUserAccount.shared.state == .loggedOut ? .main : .splashscreen

    }

    @objc func userLoggedOut() {
        SSNUserAccount.shared.logout()
        CloudyToolManager.shared.forceUpdatingCurrentBundleHead()
        refreshHome()
    }

    @objc func userDidDeleteAccount() {
        self.dismiss()
        self.modalSheetPresentation = .accountDeletion
        Analytics.shared.reportScreenVisit(name: "Account Deletion")
    }

    func buildEnv() {
        if env == nil && agentCoordinator == nil {
            env = RLCapEnv()
            agentCoordinator = AgentCoordinator(env: env!)
            agentCoordinator!.delegate = self

            if PermissionsManager.shared.areGranted([.camera, .mic, .network]) {
                agentCoordinator!.start()
            }
        }
    }

    @objc func restartAVProvider() {
        env?.restartAVProvider()
    }

    /*
     * Decide if we show the local library or the cloud library. We show the last page selected by the user.
     * If the user has never selected, we show the cloud section if there is no local recordings
     */
    private func showDefaultLibrary() {
        let selectedLibrary = UserDefaults.standard.integer(forKey: Self.navigationLibraryLastSelectedKey)
        if selectedLibrary > 0, let newLink = SwitcherNavigationLibraryLink(rawValue: selectedLibrary) {
            navigationLibraryLink = newLink
        } else {
            let fileManager = FileManager.default
            if let documentsPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true)
                .first {
                let hasVideos = try? fileManager.contentsOfDirectory(atPath: documentsPath)
                    .contains(where: {$0.hasSuffix(".mov")})
                navigationLibraryLink = hasVideos == true ? .local : .cloud
            }
        }
    }

    public func refreshHome() {

        /* sometimes there is an issue when the keyboard is still opened after a success log in -
         here we force the keyboard to hide */
        UIApplication.shared.endEditing()

        if userIsLoggedIn {
            VideoLibraryUsageService.refresh()
        }

        if !urlsToImport.isEmpty {
            self.presentSheet(sheet: .importAssetsExtension)
        } else if let urlsToUpload, !urlsToUpload.isEmpty {
            Task { @MainActor in
                self.videoLibraryUsageWorking = true
                if await self.videoLibraryUsage.validateVideoUpload() {
                    self.presentSheet(sheet: .videoLibraryUploadExtension(urls: urlsToUpload))
                }
                self.videoLibraryUsageWorking = false
            }
        } else {
            showSurveyIfNeeded()
        }
        presentNewsIfNeeded()
    }

    public func showSurveyIfNeeded() {
        if userIsLoggedIn && OnBoardingSurveyManager.shared.showSurvey {
            self.dismiss()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.presentSheet(sheet: .onBoardingSurvey)
            }
        }
    }

    public func showExitSurvey() {
        if userIsLoggedIn {
            self.dismiss()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.presentSheet(sheet: .exitSurvey)
            }
        }
    }

    public var canPresentExtraPopup: Bool {
        return rootViewLink == .main
    }

    public func dismiss() {
        modalFullScreenPresentation = nil
        modalSheetPresentation = nil
    }

    public func showOrientationSelector(isPad: Bool) {
        self.dismiss()
        if isPad {
            withAnimation {
                self.showOrientationSelector = true
            }
        } else {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation {
                    self.showOrientationSelector = true
                }
            }
        }
    }

    private func presentNewsIfNeeded() {
        if FeatureManager.shared.showNews && didNeverShowNews && modalSheetPresentation == nil {
            didNeverShowNews = false
            self.modalSheetPresentation = .whatsNew
        }
    }

    @MainActor public func enterStudio() {
        if permissionsManager.areGranted([.camera, .mic, .network]) {

            withAnimation(.linear(duration: 0.1)) {
                rootViewLink = .transition
            }

            VideoLibraryUploadsManager.shared.pause(pauseReason: .inMixer)

            SwiAccountBackgroundRefresh.shared.refresh()

            if !SSNUserAccount.shared.accessGranted {
                self.setupRecMode()
            }

            self.stopBackgroundTasks()
            CloudyToolManager.shared.applyUpgradePack()

            if let localCamera = env!.video {
                OrientationUtil.setupLocalCameraToBeUsedLocallyInTheMainMixer(localCamera: localCamera)
                localCamera.isVirtualPTZAllowed = UIDevice.current
                    .userInterfaceIdiom == .pad // asked by Nick for test purpose
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.linear(duration: 0.1)) {
                    self.rootViewLink = .switcher
                }
            }

            let props = [
                "targetAspectRatio": TargetAspectRatioUtil.ratioToString(OutputProfile.shared.targetAspectRatio)
            ]
            Analytics.shared.reportScreenVisit(name: "Switcher", properties: props)

            // exit the app when enter in background
            AppStateWatcher.shared.pushMode(.exitWhenInBackground)

        } else {
            showPermissionAlert = true
        }
    }

    @MainActor public func exitStudio() {

        // stay in background when exit the app
        AppStateWatcher.shared.popMode()

        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            self.rootViewLink = .main
            self.startBackgroundTasks()
        }
    }

    @MainActor private func startBackgroundTasks() {
        if userIsLoggedIn {
            VideoLibraryUploadsManager.shared.resume(pauseReason: .inMixer)
        }
        CloudyToolBackgroundActivity.shared.start()
        DiagManager.shared.startBackgroundProcessing()
        ClarityLogger.shared.resume()
        SwitcherInAppMessage.shared.startListening()
    }

    @MainActor private func stopBackgroundTasks() {
        DiagManager.shared.stopBackgroundProcessing()
        CloudVideoDownloadService.shared.cancelAllDownloads()
        CloudyToolBackgroundActivity.shared.stop()
        SwitcherInAppMessage.shared.stopListening()
    }

    private func setupRecMode() {
        // disable camera recording (including director mode)
        RecProfile.shared.remoteCameraRecording = false
        RecProfile.shared.fullCameraRecording = false

        // disable broadcasting
        BCProfileLibrary.shared.currentProfileType = .recordOnly

        // enable program recording
        BCProfileLibrary.shared.programRecording = true

        // set program recording options
        BCProfileLibrary.shared.defaultProgramFrameSize = src_conf_size_t(width: 1920, height: 1080)
    }

    public func applyNavigationInfo(navigationInfo: SwitcherNavigationInfo) {
        logger.info("applyNavigationInfo \(navigationInfo)")
        if navigationInfo.urlToUpload != nil {
            if videoLibraryUsage.usageState.okToUpload {
                self.presentSheet(sheet: .deeplink(navigationInfo))
            } else {
                self.presentSheet(sheet: .storageLimitAlert)
            }
        } else if navigationInfo.broadcastToEdit != nil {
            self.navigationLink = .library
            self.navigationLibraryLink = .cloud
            self.presentSheet(sheet: .deeplink(navigationInfo))
        } else if let navigationLibrary = navigationInfo.library {
            self.navigationLibraryLink = navigationLibrary
        } else if let eventId = navigationInfo.eventDetail {
            if let event = UpcomingEventService.shared.upcomingLivestreams.first(where: {
                $0.id == eventId.lowercased()
            }) {
                navigationLink = .home
                navigationPath.append(event)
            } else {
                logger.info("Event with id \(eventId) not found in upcomingLivestreams")
            }
        }
    }

    public func doPlusButton() {
        if userIsLoggedIn {
            presentSheet(sheet: .actions)
        } else {
            navigationLink = .home
        }
    }

    public func navigateToVideoLibrary() {
        self.dismiss()
        self.navigationLink = .library
        self.navigationLibraryLink = .cloud
    }

    public func navigateToEventDetail(eventId: String) {
        let navInfo = SwitcherNavigationInfo.navigationToEventDetail(eventId: eventId)
        applyNavigationInfo(navigationInfo: navInfo)
    }

    public func presentSheet(sheet: SwitcherNavigationActiveSheet) {
        self.dismiss()
        self.modalSheetPresentation = sheet
    }

    public func presentFullscreenSheet(sheet: SwitcherNavigationActiveFullScreenCover) {
        self.dismiss()
        self.modalFullScreenPresentation = sheet
    }

}

extension SwitcherNavigationViewModel: VideoLibraryValidationNotifier {
    func videoLibraryNotifyFull(modalCleanup: @escaping () async -> Void) {
        DispatchQueue.main.async {
            Task.init {
                await modalCleanup()
                self.presentSheet(sheet: .storageLimitAlert)
            }
        }
    }

    func videoLibraryNotifyNotLoggedIn(modalCleanup: @escaping () async -> Void) {
        DispatchQueue.main.async {
            Task.init {
                await modalCleanup()
                self.presentFullscreenSheet(sheet: .login)
            }
        }
    }

    func videoLibraryNotifyExpired(checkSeen: Bool = true, modalCleanup: @escaping () async -> Void) {
        guard !SSNUserAccount.shared.hasSeenExpiredPrompt  || !checkSeen else {
            return
        }

        if SSNUserAccount.shared.userInfo.shouldPromptWithIAS {
            SSNUserAccount.shared.hasSeenExpiredPrompt = true
            DispatchQueue.main.async {
                Task.init {
                    await modalCleanup()
                    self.presentSheet(sheet: .subscription)
                }
            }
        } else {
            SSNUserAccount.shared.hasSeenExpiredPrompt = true
            DispatchQueue.main.async {
                Task.init {
                    await modalCleanup()
                    self.presentSheet(sheet: .subscriptionExpiredAlert)
                }
            }
        }
    }
}

extension SwitcherNavigationViewModel: UserAccountDelegate {
    public func userAccountStateDidChange() {
        switch SSNUserAccount.shared.state {
        case .loggedIn,
                .gracePeriod:
            userIsLoggedIn = true
        case .loggedOut:
            userIsLoggedIn = false
        @unknown default:
            FatalLogger.shared.logFatalError()
        }

    }

    public func userAccountAuthDidStart() {}
    public func userAccountAuthDidSucceed() {}
    public func userAccountAuthDidFail(error: any Error) {}
    public func userAccountDidLogIn() {}
    public func userAccountDidLogOut() {}
    public func userAccountCheckDidEnd(success: Bool) {}
    public func userAccountDidRefreshToken() {}
}

extension SwitcherNavigationViewModel: SwiUserInfoDelegate {
    nonisolated func userInfoRefreshDidBegin() {
    }

    nonisolated func userInfoRefreshDidEnd(success: Bool) {
        ClarityLogger.shared.setUserId(SSNUserAccount.shared.userId)
#if CRASHLYTICS
        Crashlytics.crashlytics().setUserID(SSNUserAccount.shared.userId)
#endif
    }

    nonisolated func subscriptionSourceChanged() {
        DispatchQueue.main.async {
            // logged-in expired dashboard subscribers
            if self.userIsLoggedIn && // logged-in
                !SSNUserAccount.shared.accessGranted { // expired
                self.videoLibraryNotifyExpired(modalCleanup: {})
            }
        }
    }
}

extension SwitcherNavigationViewModel: MixerModelDisposeDelegate {

    func mixerModelWantToDispose(navigationInfo: SwitcherNavigationInfo) {
        self.applyNavigationInfo(navigationInfo: navigationInfo)
        self.rootViewLink = .transition
    }

    func mixerModelDidDispose() {
        DispatchQueue.main.async {
            self.exitStudio()
        }
    }
}

extension SwitcherNavigationViewModel: AgentCoordinatorDelegate {

    // MARK: - AgentCoordinatorDelegate

    func switchToValidationUI(completion: @escaping () -> Void) {
        withAnimation {
            self.rootViewLink = .cameraValidation
        } completion: {
            completion()
        }
        // exit the app when enter in background
        AppStateWatcher.shared.pushMode(.exitWhenInBackground)
    }

    func switchToWaitingUI(completion: @escaping () -> Void) {
        self.cameraViewModel = nil
        withAnimation {
            self.rootViewLink = .transition
        } completion: {
            completion()
        }
    }

    func switchToCameraUI(camera: Camera, completion: @escaping () -> Void) {
        let cameraModel = CameraViewModel(camera: camera)
        cameraModel.controllerName = agentCoordinator?.clientName
        cameraModel.exitDelegate = self
        cameraModel.cameraAngle = Int(agentCoordinator?.cameraAngle ?? 0)
        self.cameraViewModel = cameraModel
        withAnimation {
            self.rootViewLink = .camera
        } completion: {
            completion()
        }
        Analytics.shared.reportScreenVisit(name: "Camera")
        DispatchQueue.main.async {
            self.stopBackgroundTasks()
        }
    }

    func switchToIdleUI(completion: @escaping () -> Void) {
        withAnimation {
            self.rootViewLink = .main
        } completion: {
            completion()
            self.cameraViewModel = nil
            // exit the app when enter in background
            AppStateWatcher.shared.popMode()
        }
    }

    func cameraConfDidChange() {
        cameraViewModel?.cameraConfDidChange()
    }

    func cameraClientNameDidChange(_ name: String) {
        if rootViewLink == .cameraValidation {
            cameraViewModel?.controllerName = cameraViewModel?.controllerName // force to call updateUIViewController in CameraValidationView
        } else {
            cameraViewModel?.controllerName = name
        }
    }

    func cameraAngleDidChange(_ angle: Int32) {
        cameraViewModel?.cameraAngle = Int(angle)
    }

    func cameraMediaTransferDidStart() {
        cameraViewModel?.cameraTransfertStarted = true
    }

    func cameraMediaTransferProgressDidChange(_ progress: Float) {
        cameraViewModel?.cameraTransfertProgress = progress
    }

    func cameraMediaTransferDidStop() {
        cameraViewModel?.cameraTransfertStarted = false
    }

    func isRemoteConnectionAllowed() -> Bool {
        return rootViewLink.isRemoteConnectionEnabled
    }
}

extension SwitcherNavigationViewModel: CameraValidationDelegate {

    func cameraValidationAccepted() {
        agentCoordinator?.userAllows()
    }

    func cameraValidationRejected() {
        agentCoordinator?.userRejects()
    }

}

extension SwitcherNavigationViewModel: OnePane2CameraDelegate {

    func cameraAsksToClose() {
        agentCoordinator?.userRejects()
    }
}
