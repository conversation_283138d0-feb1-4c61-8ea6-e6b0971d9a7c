//
//  SwitcherRootView.swift
//  Cap-iOS
//
//  Created by <PERSON> on 03/10/2024.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//
import Foundation
import SwiftUI

struct SwitcherRootView: View {
    // MARK: - Properties
    var navigationViewModel = SwitcherNavigationViewModel.shared

    // MARK: - UI Content
    var body: some View {
        Group {
            switch navigationViewModel.rootViewLink {
            case .splashscreen:
                SwitcherSplashScreen {
                    DispatchQueue.main.async {
                        withAnimation {
                            navigationViewModel.rootViewLink = .main
                        }
                    }
                }
            case .main:
                SwitcherHomeView(navigationViewModel: navigationViewModel)
            case .transition:
                SwitcherWaitingView()
            case .switcher:
                if let env = navigationViewModel.env {
                    SwitcherMixerRootView(env: env, delegate: navigationViewModel)
                } else {
                    Text("No environment found")
                        .font(.largeTitle)
                        .foregroundStyle(.red)
                }
            case .cameraValidation:
                CameraValidationView(navigation: navigationViewModel)
                    .ignoresSafeArea()
            case .camera:
                if let cameraViewModel = navigationViewModel.cameraViewModel {
                    CameraView(cameraModel: cameraViewModel)
                        .interfaceOrientations(SwitcherOrientations.orientationsForCamera(cameraViewModel.camera))
                } else {
                    Text("No environment found")
                        .font(.largeTitle)
                        .foregroundStyle(.red)
                }
            }
        }
    }
}
