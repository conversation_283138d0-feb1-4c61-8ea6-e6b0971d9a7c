//
//  SwitcherNavigationType.swift
//  Cap-iOS
//
//  Created by <PERSON> on 08/10/2024.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//

import SwiftUI

extension Notification.Name {
    static let switcherShouldReloadCameraSettings = Notification.Name("ReloadCameraSettings")
    static let switcherShouldRefreshCloudVideos = Notification.Name("RefreshCloudVideos")
}

enum SwitcherNavigationRootLink: Int {
    case splashscreen
    case main
    case transition
    case switcher
    case cameraValidation
    case camera

    /// Boolean used to validate if a remote camera can connect to this device
    var isRemoteConnectionEnabled: Bool {
        return switch self {
        case .main: true
        default: false
        }
    }
}

enum SwitcherNavigationLink: Int, CaseIterable, Identifiable {
    case home
    case library

    var id: Int {
        return self.rawValue
    }

    var text: LocalizedStringKey {
        return switch self {
        case .home: "home.title"
        case .library: "library.title"
        }
    }

    var image: ImageResource {
        return switch self {
        case .home: .figmaHome
        case .library: .figmaVideoLibrary
        }
    }

    @ViewBuilder func getTabLabel(selectedLink: SwitcherNavigationLink) -> some View {
        Label(text, image: image)
            .foregroundStyle(selectedLink == self ? .gray100 : Color.textSecondary60)
    }
}

enum SwitcherNavigationLibraryLink: Int {
    case local = 1
    case cloud = 2

    var title: LocalizedStringKey {
        return switch self {
        case .local: "MyRecordingTableViewController.title"
        case .cloud: "library.title"
        }
    }

    var subtitle: LocalizedStringKey {
        return switch self {
        case .local: "videolibrary.selector.localrecording"
        case .cloud: "videolibrary.selector.cloudvideos"
        }
    }

    var devLog: String {
        return switch self {
        case .local: "Open Local Recordings"
        case .cloud: "Open Video Library Cloud"
        }
    }
}

enum SwitcherNavigationSource: String {
    case homeView = "Home View"
    case livestreamEvents = "Livestream Events"
    case summaryPanel = "Summary Panel"
    case outputsTab = "Outputs Tab"
}

enum SwitcherNavigationActiveSheet: Identifiable, Equatable {
    var id: String {
        return switch self {
        case .permissions: "permissions"
        case .importAssetsExtension: "importAssetsExtension"
        case .videoLibraryUploadExtension: "videoLibraryUploadExtension"
        case .videoLibraryUpload: "videoLibraryUpload"
        case .subscription: "subscription"
        case .subscriptionFromAccountPage: "subscriptionFromAccountPage"
        case .videoPhotoLibrarySelector: "videoPhotoLibrarySelector"
        case .actions: "actions"
        case .deeplink: "deeplink"
        case .whatsNew: "news"
        case .accountDeletion: "accountdeletion"
        case .scheduleNextEvent: "scheduleNextEvent"
        case .onBoardingSurvey: "onBoardingSurvey"
        case .exitSurvey: "exitSurvey"
        case .storageLimitAlert: "storageLimitAlert"
        case .subscriptionExpiredAlert: "subscriptionExpiredAlert"
        case .networkUnavailableAlert: "networkUnavailableAlert"
        case .menu: "menu"
        case .scheduleSocialPost: "scheduleSocialPost"
        }
    }
    case permissions // ask user for the permissions the first time the app opens
    case importAssetsExtension // import assets (from extension)
    case videoLibraryUploadExtension(urls: [URL]) // upload a video to the video library (from extension)
    case videoLibraryUpload(urls: [URL]) // upload videos to the video library (from home or video library)
    case subscription // the subscription page
    case subscriptionFromAccountPage // the subscription page (from account menu)
    case videoPhotoLibrarySelector // select a video from the photo library
    case actions // New livestream, New recording, Upload video (iPhone only)
    case deeplink(SwitcherNavigationInfo)
    case whatsNew
    case accountDeletion
    case scheduleNextEvent
    case scheduleSocialPost(ExistingBroadcasts) // create an event from existing broadcasts
    case onBoardingSurvey // show the onboarding survey
    case exitSurvey // show the onboarding survey
    case storageLimitAlert // alert for storage limit met
    case subscriptionExpiredAlert
    case networkUnavailableAlert
    case menu

    var exportSource: ExportSource? {
        return switch self {
        case .videoLibraryUploadExtension: .shareExtension
        case .videoLibraryUpload: .home
        default: nil
        }
    }
}

enum SwitcherNavigationActiveFullScreenCover: Int, Identifiable {
    var id: String { "\(self)" }
    case login
}
