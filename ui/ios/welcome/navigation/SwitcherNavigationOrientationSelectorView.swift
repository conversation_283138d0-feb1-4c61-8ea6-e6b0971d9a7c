//
//  SwitcherNavigationOrientationSelectorView.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 12.03.24.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//

import SwiftUI
import TipKit

// MARK: - SwitcherNavigationOrientationSelectorView
struct SwitcherNavigationOrientationSelectorView: View {

    // MARK: - Observed Properties
    @Bindable var navigationViewModel: SwitcherNavigationViewModel
    @ObservedObject var bcProfileLibrary = BCProfileLibrary.shared

    // MARK: - Binding Properties
    @StateObject var viewModel = UpcomingEventService.shared
    @State var isPortraitSelected: Bool
    @State private var broadcastMode: BCProfileType = .multiStreaming

    // MARK: - Properties
    let isLivestreaming: Bool

    // MARK: - Initializers
    init(navigationViewModel: SwitcherNavigationViewModel) {
        self.navigationViewModel = navigationViewModel
        self.isLivestreaming = BCProfileLibrary.shared.currentProfileType != BCProfileType.recordingType()

        if BCProfileLibrary.shared.currentProfileType == .practice {
            self.broadcastMode = .practice
        }

        self._isPortraitSelected = State(initialValue: OutputProfile.shared.targetAspectRatio.doubleValue < 1.0)
    }

    // MARK: - UI Content
    var body: some View {
        NavigationStack {
            VStack(spacing: 12) {
                broadcastHeader
                Spacer()
                orientationSelector
                enterStudioButton
            }
            .padding(.horizontal, 16)
            .switcherNavigationBar(
                title: isLivestreaming ? "" : "SwitcherNavigationActionSelectorView.rec",
                leftToolbarItem: SwitcherNavToolBarItemModel(type: .close, action: close)
            )
            .toolbar {
                if isLivestreaming {
                    ToolbarItem(placement: .principal) {
                        if !SSNUserAccount.shared.userInfo.isNewerThanJuly29th() {
                            broadcastModePicker
                                .popoverTip(PracticeModeSelectorTip())
                        } else {
                            broadcastModePicker
                        }
                    }
                }
            }
        }
    }

    // MARK: - UI Subviews
    @ViewBuilder
    private var broadcastHeader: some View {
        if isLivestreaming {
            switch broadcastMode {
            case .practice:
                VStack(spacing: 45) {
                    Text("SwitcherNavigationOrientationSelectorView.practice.text")
                        .foregroundStyle(Color.gray100)
                        .font(Font.titleSecondary)
                        .multilineTextAlignment(.center)
                        .padding()
                        .frame(maxWidth: 480)
                        .background(Color.cellBackground)
                        .cornerRadius(12)
                        .padding(.top, 12)
                        .fixedSize(horizontal: false, vertical: true)

                    PracticeModeSectionView(color: .textBrandDarkBlue,
                                            font: .buttonPrimaryStrong)
                }

            case .multiStreaming:
                let title = viewModel.selectedLivestreamEvent?.title
                let date  = viewModel.selectedLivestreamEvent?.startsAt?.scheduledEventFormatted()
                VStack(spacing: 0) {
                    Text(title ?? "SwitcherNavigationOrientationSelectorView.no.event.found.title".localized())
                        .foregroundStyle(Color.gray100)
                        .font(Font.listTitleKey)
                        .lineLimit(2)

                    Text(date ?? "SwitcherNavigationOrientationSelectorView.no.event.found.message".localized())
                        .foregroundStyle(Color.textSecondary60)
                        .font(Font.titleCaption)
                        .padding(.top, 8)
                }
                .multilineTextAlignment(.center)
                .padding()
                .frame(maxWidth: 480)
                .background(Color.cellBackground)
                .cornerRadius(12)
                .padding(.top, 12)

            default:
                EmptyView()
            }
        }
    }

    private var orientationSelector: some View {
        VStack {
            Text("UserMainView.ratio")
                .foregroundStyle(Color.textSecondary60)
                .font(Font.titleCaption)

            HStack(spacing: 8) {
                OrientationButtonView(
                    selected: !isPortraitSelected,
                    isPortrait: false,
                    action: selectOrientation)

                OrientationButtonView(
                    selected: isPortraitSelected,
                    isPortrait: true,
                    action: selectOrientation)
            }
            .padding(.bottom, 32)
        }
    }

    private var broadcastModePicker: some View {
        Menu(content: {
            Picker("", selection: $broadcastMode) {
                ForEach(BCProfileType.broadcastModes, id: \.self) { mode in
                    Text(mode.localizedName)
                        .tag(mode)
                }
            }
        }, label: {
            HStack(spacing: 8) {
                Text(broadcastMode.localizedName)
                    .font(.navigationBarButtonPrimary)

                Image(.figmaChevronDown)
            }
            .foregroundStyle(Color.gray100)
        })
        .onChange(of: broadcastMode) {_, mode in
            bcProfileLibrary.currentProfileType = mode
        }
    }

    private var enterStudioButton: some View {
        Button("SwitcherNavigationOrientationSelectorView.enter", action: navigationViewModel.enterStudio)
            .buttonStyle(FillButton(
                height: 48,
                maxWidth: 480,
                backgroundColor: Color.buttonPrimary2
            ))
            .padding(.bottom, 21)
            .disabled(broadcastMode == .practice &&
                      bcProfileLibrary.practiceModeState != .ready)
    }

    // MARK: - Private Functions
    /// Close the Orientation Selector
    private func close() {
        withAnimation {
            navigationViewModel.showOrientationSelector = false
        }
    }

    /// Select the orientation for the studio
    private func selectOrientation(isPortraitSelected: Bool) {
        self.isPortraitSelected = isPortraitSelected
        let targetAspectRatio = isPortraitSelected ? GmuRatioI32.nineBySixteen : GmuRatioI32.sixteenByNine
        OutputProfile.shared.targetAspectRatio = targetAspectRatio
    }
}
