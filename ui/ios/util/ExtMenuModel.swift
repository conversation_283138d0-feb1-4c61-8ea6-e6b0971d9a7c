//
//  ExtMenuModel.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON> on March 20, 2017.
//  Copyright © 2017 Switcher Inc. All rights reserved.
//

import Foundation
import UIKit

public class ExtMenuModel: NSObject {
    public var items = [ExtMenuItem]()
}

public struct ExtMenuItemOptions: OptionSet {

    public let rawValue: Int

    public init(rawValue: Int) {
        self.rawValue = rawValue
    }

    public static let staySelected        = ExtMenuItemOptions(rawValue: 1 << 0)
    public static let disclosureIndicator = ExtMenuItemOptions(rawValue: 1 << 1)
    public static let disabled            = ExtMenuItemOptions(rawValue: 1 << 2)
    public static let warningIndicator    = ExtMenuItemOptions(rawValue: 1 << 3)
}

public class ExtMenuItem: NSObject {
    private var delegates = [ExtMenuItemDelegateHolder]()

    func add(delegate: any ExtMenuItemDelegate) {
        let holder = ExtMenuItemDelegateHolder(delegate)
        delegates.append(holder)
    }

    func remove(delegate: any ExtMenuItemDelegate) {
        if let index = delegates.firstIndex(where: {$0.isHolding(delegate)}) {
            delegates.remove(at: index)
        }
    }

    public var isEnabled = true {
        didSet {
            for d in delegates {
                d.delegate.extMenuItemDidChangeEnableState(self)
            }
        }
    }
}

public class ExtMenuActiveItem: ExtMenuItem {
    public static let optionsDisabled = ExtMenuItemOptions.disabled.rawValue
    public static let optionsStaySelected = ExtMenuItemOptions.staySelected.rawValue
    public static let optionsDisclosureIndicator = ExtMenuItemOptions.disclosureIndicator.rawValue
    public static let optionsWarningIndicator = ExtMenuItemOptions.warningIndicator.rawValue

    public var title: String
    public var image: UIImage?
    public var options: ExtMenuItemOptions
    public var action: (() -> Void)?

    public var accessoryType: UITableViewCell.AccessoryType {
        if options.contains(.disclosureIndicator) {
            return .disclosureIndicator
        } else {
            return .none
        }
    }

    public init(
        title: String,
        image: UIImage?,
        options: ExtMenuItemOptions,
        isEnabled: Bool = true,
        action: (() -> Void)?
    ) {
        self.title = title
        self.image = image
        self.options = options
        self.action = action
        super.init()
        self.isEnabled = isEnabled
    }

    public convenience init(title: String, image: UIImage?, options: Int, isEnabled: Bool, action: (() -> Void)?) {
        self.init(
            title: title,
            image: image,
            options: ExtMenuItemOptions(rawValue: options),
            isEnabled: isEnabled,
            action: action
        )
    }
}

public class ExtMenuButtonItem: ExtMenuItem {
    public var title: String
    public var action: (() -> Void)?

    public init(title: String, isEnabled: Bool = true, action: (() -> Void)?) {
        self.title = title
        self.action = action
        super.init()
        self.isEnabled = isEnabled
    }
}

@objc public protocol ExtMenuItemDelegate: AnyObject {
    func extMenuItemDidChangeEnableState(_ item: ExtMenuItem)
}

private class ExtMenuItemDelegateHolder {
    public unowned var delegate: any ExtMenuItemDelegate
    var unmanagedDelegate: Unmanaged<any ExtMenuItemDelegate>

    public func isHolding(_ delegate: any ExtMenuItemDelegate) -> Bool {
        return unmanagedDelegate.takeUnretainedValue() === delegate
    }

    init(_ delegate: any ExtMenuItemDelegate) {
        self.delegate = delegate
        self.unmanagedDelegate = Unmanaged<any ExtMenuItemDelegate>.passUnretained(delegate)
    }
}
