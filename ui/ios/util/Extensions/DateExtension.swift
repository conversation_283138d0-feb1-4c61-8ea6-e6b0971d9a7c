//
//  DateExtension.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 28.03.24.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//

import Foundation
import SwiftUI

extension Date {
    func secondsSince(_ date: Date) -> Int {
        return Int(round(self.timeIntervalSince(date)))
    }

    func absSecondsSince(_ date: Date) -> Int {
        return abs(Int(round(self.timeIntervalSince(date))))
    }

    // Get a date range from "now + 5 min" to "now + 1 year"
    static func getEventSchedulingDateRange() -> ClosedRange<Date> {
        let startDate = Calendar.current.date(byAdding: .minute,
                                              value: 5,
                                              to: Date()) ?? Date()

        let endDate = Calendar.current.date(byAdding: .year,
                                            value: 1,
                                            to: Date()) ?? Date()

        return startDate...endDate
    }

    func videoLibraryFormatted(showTime: Bool = true) -> String {
        // TODO: Dates format handling needs a global rework, this will do for now
        let dateString = self.dateOnlyFormatted()
        if showTime {
            return "\(dateString) at \(self.shortenedTime())"
        } else {
            return dateString
        }
    }

    func scheduledEventFormatted() -> String {
        let date = self.customPosixString(type: .scheduledLivestreamEvent)
        return "\(date) at \(self.shortenedTime())"
    }

    // Only show the year if it's not the current year
    // EU output: 6 Nov
    //            3 Jan 2025
    // US output: Nov 6
    //            Jan 3, 2025
    func dateOnlyFormatted() -> String {
        let calendar = Calendar.current
        let currentYear = calendar.component(.year, from: Date())
        let dateYear = calendar.component(.year, from: self)

        let dateFormatter = DateFormatter()
        let dateTemplate = (dateYear == currentYear) ? "MMM d" : "MMM d yyyy"
        dateFormatter.setLocalizedDateFormatFromTemplate(dateTemplate)
        return dateFormatter.string(from: self)
    }

    // Show hours and minutes
    // EU output: 17:15
    // US output: 5:15 PM
    func shortenedTime() -> String {
        return self.formatted(date: .omitted, time: .shortened)
    }

    func getTimeAwayFromNowString() -> LocalizedStringKey {
        let calendar = Calendar.current
        let today = Date()

        // Calculate difference in days
        let components = calendar.dateComponents([ .minute, .hour, .day, .weekOfYear], from: today, to: self)
        if let weeksAway = components.weekOfYear, weeksAway > 0 {
            return weeksAway.getPluralOrSinglular(pluralKey: "event.time.away.weeks.plural \(weeksAway)",
                                                  singleKey: "event.time.away.weeks.singular")
        } else if let daysAway = components.day, daysAway > 0 {
            return daysAway.getPluralOrSinglular(pluralKey: "event.time.away.days.plural \(daysAway)",
                                                 singleKey: "event.time.away.days.singular")
        } else if let hoursAway = components.hour, hoursAway > 0 {
            return hoursAway.getPluralOrSinglular(pluralKey: "event.time.away.hours.plural \(hoursAway)",
                                                  singleKey: "event.time.away.hours.singular")
        } else if let minutesAway = components.minute, minutesAway > 0 {
            return minutesAway.getPluralOrSinglular(pluralKey: "event.time.away.minutes.plural \(minutesAway)",
                                                    singleKey: "event.time.away.minutes.singular")
        } else {
            return "event.time.away.now"
        }
    }

    func isJuly29OrLater() -> Bool {
        // Create July 29th at 00:00 for 2025
        var components = DateComponents()
        components.year = 2025
        components.month = 7
        components.day = 29
        components.hour = 0
        components.minute = 0
        components.second = 0

        let calendar = Calendar.current
        guard let july29 = calendar.date(from: components) else {
            return false
        }

        return self >= july29
    }

    func customPosixString(type: SwitcherDateFormatType = .scheduledLivestreamEvent,
                           timeZone: SwitcherTimeZone? = nil) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US_POSIX")
        if let timeZone = timeZone?.timeZone {
            formatter.timeZone = timeZone
        }
        formatter.dateFormat = type.format
        return formatter.string(from: self)
    }

}

extension String {
    func customPosixDate(type: SwitcherDateFormatType = .scheduledLivestreamEvent,
                         timeZone: SwitcherTimeZone? = nil) -> Date? {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US_POSIX")
        if let timeZone = timeZone?.timeZone {
            formatter.timeZone = timeZone
        }
        formatter.dateFormat = type.format
        return formatter.date(from: self)
    }
}

extension JSONDecoder.DateDecodingStrategy {
    static let customISO8601 = custom { decoder throws -> Date in
        let container = try decoder.singleValueContainer()
        let string = try container.decode(String.self)
        if let date = string.customPosixDate(type: .iso8601) {
            return date
        }
        throw DecodingError.dataCorruptedError(in: container, debugDescription: "Invalid date: \(string)")
    }
}

extension JSONEncoder.DateEncodingStrategy {
    static let customISO8601 = custom { date, encoder in
        var container = encoder.singleValueContainer()
        let string = date.customPosixString(type: .iso8601noMS)
        try container.encode(string)
    }
}

enum SwitcherDateFormatType {
    case scheduledLivestreamEvent
    case azure
    case clips
    case passes
    case iso8601
    case iso8601noMS

    var format: String {
        return switch self {
        case .scheduledLivestreamEvent: "E, MMM d"
        case .azure: "yyyy-MM-dd HH:mm:ss ZZZ"
        case .clips: "yyyy-MM-dd'T'HH:mm:ssZ"
        case .passes: "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
        case .iso8601: "yyyy-MM-dd'T'HH:mm:ss.SSSXXXXX"
        case .iso8601noMS: "yyyy-MM-dd'T'HH:mm:ssXXXXX"
        }
    }
}

enum SwitcherTimeZone {
    case gmt0

    var timeZone: TimeZone? {
        return switch self {
        case .gmt0: TimeZone(secondsFromGMT: 0)
        }
    }
}
