//
//  EventCreationService.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 26.06.2024.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//

import Foundation

private let logger = LsLogger(subsystem: "swi.ui", category: "EventCreationService")

@MainActor
class EventCreationService: EventService {
    // MARK: - Errors
    enum Errors: Error {
        case errorCreatingAutogeneratedEvent
        case errorCreatingLivestreamingEvent
        case noSelectedProfile
        case noMultistreamingSettings
        case outputCreationFailed
        case outputCreationNoEvent
        case incorrectBCProfileType
        case failedToCreateFacebook
        case missingAsset
        case pngDataConversionFailed
    }

    func tempFileURLForDefaultThumbnail() throws -> URL {
        let assetName = "event-cover-image"

        guard let image = UIImage(named: assetName) else { throw Errors.missingAsset }
        guard let pngData = image.pngData() else { throw Errors.pngDataConversionFailed }

        let uniqueName = "\(UUID().uuidString)-\(assetName).png"
        let tmpURL = FileManager.default.temporaryDirectory.appendingPathComponent(uniqueName)
        try pngData.write(to: tmpURL)

        return tmpURL
    }

    func uploadDefaultThumbnail() async throws -> String? {
        var thumbnailAssetId: String?

        let url = try tempFileURLForDefaultThumbnail()
        let defaultThumbnailAssetId = try await uploadThumbnailService?.startAsyncUploadThumbnail(url: url)
        thumbnailAssetId = defaultThumbnailAssetId

        return thumbnailAssetId
    }

    // MARK: - Create
    func createAutoGeneratedEvent(broadcastId: String,
                                  type: BCProfileType,
                                  name: String,
                                  width: Int,
                                  height: Int) async throws -> (attributes: BCProfileLibraryTypeAttributes,
                                                                practiceUrl: String?) {
        guard BCProfileTypeWrapper.supportsAutoGeneratedEvent(type),
              let projectId = userInfo.projectId else {
            throw Errors.incorrectBCProfileType
        }

        state = .progress

        let attr = type.attributes
        attr.selectedProfile?.videoFrameWidth = width
        attr.selectedProfile?.videoFrameHeight = height

        var practiceUrl: String?

        do {
            let broadcastV2 = try await broadcastsAPI.createSingletonBroadcast(projectId: projectId,
                                                                               type: type.apiBroadcastType)
            // Convert from V2 to V1
            let bc = broadcastV2.toLivestreamEvent().toBroadcastApiBroadcast()
            self.broadcast = bc

            // Set attribute data
            attr.selectedProfile?.broadcastId = bc.id
            attr.streamStatusUrl = userAccount.apiUrl(withSuffix: "api/Broadcasts/\(bc.id ?? "")/Status")
            attr.streamEndedPostUrl = userAccount.apiUrl(withSuffix: "api/Broadcasts/\(bc.id ?? "")/End")
            self.attr = attr

            try await createInput(broadcastId: bc.id ?? "")
            try await setVideoPlayerSettings()
            try await finalizeBroadcast()

            // Broadcast response may provide a more accurate share URL than the SwitcherStreamSettings response
            if let shareUrl = broadcastV2.meta?.shareUrl {
                practiceUrl = shareUrl
            }
        } catch {
            logger.warning("Create auto generated event failed: \(error)")
            state = .error
            throw Errors.errorCreatingAutogeneratedEvent
        }

        self.attr = attr
        state = .success

        return (attr, practiceUrl)
    }

    func createLivestreamingEvent(broadcastId: String,
                                  title: String,
                                  description: String,
                                  settings: SwitcherStreamSettings,
                                  collectionPlaylistIds: [String],
                                  outputs: [LivestreamDest],
                                  catalogData: CatalogLivestreamUserData,
                                  enableRecording: Bool,
                                  startsAt: Date?,
                                  coverImageUrl: String,
                                  thumbnailAssetId: String?,
                                  isExistingPost: Bool,
                                  usedSpeedTest: Bool,
                                  outsideMixer: Bool,
                                  source: SwitcherNavigationSource) async throws -> [OutputResult] {
        guard let streamingAttr = bcProfileLib.attributes(ofProfileType: .multiStreaming) else {
            StreamingProviderList.shared.requestFromCloud()
            state = .error
            throw Errors.noMultistreamingSettings
        }
        streamingAttr.selectedChannelName = title
        streamingAttr.selectedProfile = settings
        self.attr = streamingAttr

        state = .progress

        var defaultThumbnailAssetId: String?
        if thumbnailAssetId == nil {
            defaultThumbnailAssetId = try? await uploadDefaultThumbnail()
        }

        do {
            try await createBroadcastAndInput(broadcastId: broadcastId,
                                              title: title,
                                              description: description,
                                              catalogData: catalogData,
                                              enableRecording: enableRecording,
                                              startsAt: startsAt,
                                              thumbnailAssetId: thumbnailAssetId ?? defaultThumbnailAssetId)
            guard let broadcast = self.broadcast else {
                state = .error
                throw Errors.errorCreatingLivestreamingEvent
            }

            if catalogData.streamToCatalog {
                try await setVideoPlayerSettings()

                for collectionPlaylistId in collectionPlaylistIds {
                    let playlistBroadcastResponse = try await CloudRecordingsAPI.shared.addTo(
                        playlistId: collectionPlaylistId,
                        broadcastId: broadcast.id ?? ""
                    )

                    if let productId = catalogData.selectedOneTimePass?.id {
                        _ = try await StripeAPI.shared.setOneTimePassProductEntitlement(
                            productId: productId,
                            videoPlayerPlaylistBroadcastId: playlistBroadcastResponse.playlistBroadcast.id
                        )
                    }

                    if catalogData.emailRequired || catalogData.passwordRequired {
                        let playlistBroadcast = PlaylistBroadcast(
                            id: playlistBroadcastResponse.playlistBroadcast.id,
                            broadcastId: broadcast.id ?? "",
                            videoPlayerPlaylistId: collectionPlaylistId,
                            isEmailGatingEnabled: catalogData.emailRequired,
                            isPasswordGatingEnabled: catalogData.passwordRequired,
                            password: catalogData.passwordToView ?? nil,
                            ordinalRank: playlistBroadcastResponse.playlistBroadcast.ordinalRank
                        )

                        _ = try await VideoPlayersAPI.shared.setGatingOptions(
                            videoPlayerPlaylistBroadcastId: playlistBroadcastResponse.playlistBroadcast.id,
                            playlistBroadcast: playlistBroadcast
                        )

                    }
                }
            }

            let outputResults = try await createOutputs(outputs: outputs,
                                                        description: description,
                                                        startsAt: startsAt,
                                                        coverImageUrl: coverImageUrl,
                                                        isExistingPost: isExistingPost)

            // if all outputs have failed, we don't initialize the broadcast, but we delete the broadcast
            let succeddedOutputNumber = outputResults.count(where: { $0.success })

            if succeddedOutputNumber > 0 || catalogData.streamToCatalog {
                // maybe we should also delete the broadcast if this api fail
                try await initializeBroadcast()
            } else {
                // here we delete the broadcast, we don't care if this fail, but maybe we should ?
                try? await BroadcastsAPI.shared.deleteBroadcast(broadcast)
            }

            var properties: [String: Any] = [
                "name": title,
                "broadcastId": broadcast.id ?? "",
                "scheduledForLater": startsAt != nil,
                "existingScheduled": isExistingPost,
                "usedSpeedTest": usedSpeedTest,
                "outsideMixer": outsideMixer,
                "coverImageProvided": coverImageUrl != EventService.defaultThumbnail,
                "collectionsSpecified": catalogData.streamToCatalog && collectionPlaylistIds.count > 0,
                "source": source.rawValue
            ]

            if let startsAt {
                properties["scheduledTime"] = startsAt
                properties["scheduledTimeFromNow"] = Int(startsAt.timeIntervalSince(Date()))
            }

            Analytics.shared.reportEvent(name: "Event Creation",
                                         properties: properties)
            state = .success

            return outputResults

        } catch {
            if error.hasBeenCancelled {
                if let broadcast = self.broadcast {
                    Task {
                        try? await BroadcastsAPI.shared.deleteBroadcast(broadcast)
                    }
                }
                state = .idle
                return []
            } else {
                logger.warning("Create livestreaming event failed: \(error)")
                state = .error
                throw Errors.errorCreatingLivestreamingEvent
            }
        }
    }

    private func createBroadcastAndInput(broadcastId: String,
                                         title: String,
                                         description: String? = nil,
                                         catalogData: CatalogLivestreamUserData? = nil,
                                         enableRecording: Bool,
                                         startsAt: Date? = nil,
                                         thumbnailAssetId: String? = nil) async throws {

        let categories = catalogData?.categories.map { $0.toApiCategory() }
        let broadcastTemplate = BroadcastsAPIBroadcast(
            id: broadcastId,
            title: title,
            description: description,
            startsAt: startsAt,
            projectId: userInfo.projectId,
            enableRecording: enableRecording,
            enableSwitcherPlayer: catalogData?.streamToCatalog ?? false,
            enableLiveShopping: catalogData?.enableLiveShopping ?? false,
            thumbnailAssetId: thumbnailAssetId,
            categories: categories,
            showInCatalog: catalogData?.streamToCatalog ?? false,
            showPremiereCountdown: catalogData?.enableCountdownForThumbnail ?? false)

        // Create broadcast
        self.broadcast = try await broadcastsAPI.createBroadcast(broadcastTemplate)
        guard let broadcast = self.broadcast else {
            throw Errors.errorCreatingLivestreamingEvent
        }
        attr?.selectedProfile?.broadcastId = broadcast.id
        attr?.streamStatusUrl = userAccount.apiUrl(withSuffix: "api/Broadcasts/\(broadcast.id ?? "")/Status")
        attr?.streamEndedPostUrl = userAccount.apiUrl(withSuffix: "api/Broadcasts/\(broadcast.id ?? "")/End")

        // Attach links if any to broadcast
        if let webLinks = catalogData?.webLinks, !webLinks.isEmpty {
            try? await WebLinksAPI.shared.postWebLink(broadcast: broadcast.id ?? "",
                                                      weblinks: webLinks.map({ $0.toWebLinkApiResult() }))
        }

        // Create and setup input
        try await createInput(broadcastId: broadcast.id ?? "")
    }

    private func createInput(broadcastId: String) async throws {
        // Create and setup input
        input = try await broadcastsAPI.createInput(broadcastId: broadcastId)

        guard let input = self.input else {
            throw Errors.errorCreatingLivestreamingEvent
        }
        attr?.selectedProfile?.url = input.rtmps?.url
        attr?.selectedProfile?.rtmpStream = input.rtmps?.streamKey
    }

    private func setVideoPlayerSettings() async throws {
        guard let settings = attr?.selectedProfile else {
            throw Errors.noSelectedProfile
        }

        attr?.selectedProfile = try await streamSettingsAPI.postProviderSettings("VideoPlayer", settings: settings)
    }

    private func createOutputs(outputs: [LivestreamDest],
                               description: String,
                               startsAt: Date?,
                               coverImageUrl: String,
                               isExistingPost: Bool) async throws -> [OutputResult] {
        guard !outputs.isEmpty else {
            return []
        }

        // Run output setup in parallel
        let successResults = try await withThrowingTaskGroup(of: OutputResult.self) { group in
            for output in outputs {
                switch output.destination {
                case .facebook:
                    if let facebookUserData = output.userData as? FacebookLivestreamUserData {
                        group.addTask {
                            return try await self.createFacebookOutput(liveDest: output,
                                                                       output: facebookUserData,
                                                                       description: description,
                                                                       startsAt: startsAt,
                                                                       coverImageUrl: coverImageUrl,
                                                                       isExistingPost: isExistingPost)
                        }
                    } else {
                        let msg = "Facebook event should have a user data of type FacebookLivestreamUserData"
                        FatalLogger.shared.logFatalError(msg)
                    }
                case .youtube:
                    if let youtubeUserData = output.userData as? YoutubeLivestreamUserData {
                        group.addTask {
                            return try await self.createYoutubeOutput(liveDest: output,
                                                                      output: youtubeUserData,
                                                                      description: description,
                                                                      startsAt: startsAt,
                                                                      coverImageUrl: coverImageUrl,
                                                                      isExistingPost: isExistingPost)
                        }
                    } else {
                        let msg = "Youtube event should have a user data of type YoutubeLivestreamUserData"
                        FatalLogger.shared.logFatalError(msg)
                    }

                case .twitch:
                    if let twitchUserData = output.userData as? TwitchLivestreamUserData,
                       let channelName = broadcast?.title {
                        let userDataId = twitchUserData.id
                        let category = twitchUserData.category
                        let ingestServer = twitchUserData.ingestServer
                        let id = output.id
                        let title = output.outputResultTitle
                        let username = twitchUserData.name
                        let permalinkUrl = twitchUserData.permalinkUrl
                        group.addTask {
                            let success = try await self.createTwitchOutput(id: userDataId,
                                                                            category: category,
                                                                            username: username,
                                                                            permalinkUrl: permalinkUrl,
                                                                            ingestServer: ingestServer,
                                                                            channelName: channelName,
                                                                            twitchData: twitchUserData)
                            return OutputResult(id: id, success: success, title: title)
                        }
                    } else {
                        let msg = "Twitch event should have a user data of type TwitchLivestreamUserData"
                        FatalLogger.shared.logFatalError(msg)
                    }
                case .customRtmp:
                    if let customRtmpUserData = output.userData as? CustomRtmpLivestreamUserData {
                        let userDataId = customRtmpUserData.id
                        let id = output.id
                        let title = output.outputResultTitle
                        group.addTask {
                            let success = try await self.createRtmpOutput(customRtmpId: userDataId)
                            return OutputResult(id: id, success: success, title: title)
                        }
                    } else {
                        let msg = "Custom rtmp event should have a user data of type CustomRtmpLivestreamUserData"
                        FatalLogger.shared.logFatalError(msg)
                    }
                }
            }

            // Collect the results
            var successResults = [OutputResult]()
            for try await result in group {
                successResults.append(result)
            }

            return successResults
        }

        return successResults
    }

    private func createFacebookOutput(liveDest: LivestreamDest,
                                      output: FacebookLivestreamUserData,
                                      description: String?,
                                      startsAt: Date?,
                                      coverImageUrl: String,
                                      isExistingPost: Bool) async throws -> OutputResult {
        do {
            guard let title = attr?.selectedChannelName,
                  let description = description,
                  let broadcastId = broadcast?.id else {
                throw SwitcherAPIError.invalidRequest
            }

            guard let settings = attr?.selectedProfile else {
                throw Errors.noSelectedProfile
            }

            var facebookBroadcast: FacebookBroadcast
            if let startsAt = startsAt,
                startsAt > Date() {
                facebookBroadcast = FacebookBroadcast(
                    title: title,
                    description: description,
                    privacy: output.facebookSelectedEventPrivacy.apiValue,
                    stopOnDeleteStream: true,
                    contentTags: output.contentTagIds,
                    eventParams: .init(
                        cover: isExistingPost ? nil : coverImageUrl,
                        startTime: Int(startsAt.timeIntervalSince1970)
                    ),
                    crosspostingActions: output.crosspostingActions
                )
            } else {
                // Go live now
                facebookBroadcast = FacebookBroadcast(
                    title: title,
                    description: description,
                    privacy: output.facebookSelectedEventPrivacy.apiValue,
                    status: .liveNow,
                    stopOnDeleteStream: true,
                    contentTags: output.contentTagIds,
                    crosspostingActions: output.crosspostingActions
                )
            }

            var fbBroadcast: FacebookBroadcastResult
            if isExistingPost {
                facebookBroadcast.id = output.id
                // This ensures we dont overwrite data with default values
                facebookBroadcast.privacy = nil
                facebookBroadcast.contentTags = nil
                guard let result = try await facebookAPI.updateBroadcast(facebookBroadcast, accessToken: nil) else {
                    return OutputResult(liveDest: liveDest, success: false)
                }
                fbBroadcast = result
            } else {
                do {
                    let broadcastCreatedResult = try await facebookAPI.createBroadcast(facebookBroadcast,
                                                                                       userId: output.id)
                    fbBroadcast = broadcastCreatedResult
                } catch {
                    let errorMessage = (error as? FacebookError)?.userMessage
                    let matchErrors = [
                        "(#200) Permissions error",
                        "(#200) Subject does not have permission to create live video on this page",
                        "Permissions error",
                        "An active access token must be used to query information about the current user."
                    ]

                    if let errorMessage,
                        matchErrors.contains(errorMessage) {
                        return OutputResult(liveDest: liveDest,
                                            success: false,
                                            reason: "event.creation.error.facebook".localized())
                    }
                    return OutputResult(liveDest: liveDest,
                                        success: false,
                                        reason: errorMessage)
                }
            }
            // Only pages need comment moderation
            if output.type == .pages {
                if let isDiscussionEnabled = output.pageDiscussionCommenting,
                    output.selectedCommentModeration != .default || isDiscussionEnabled {
                    // Fetch access token for page
                    let accessToken = try await facebookAPI.fetchAccessToken(userId: liveDest.userData.id).accessToken
                    // Update the comment moderation settings
                    try await facebookAPI.updateCommentModeration(accessToken: accessToken,
                                                                  broadcastId: fbBroadcast.id,
                                                                  commentModerationSettings: output.commentModeration)
                }
            }

            var newSettings = settings
            newSettings.name = "Facebook"
            newSettings.broadcastId = broadcastId
            newSettings.channelName = title
            newSettings.facebookVideoId = fbBroadcast.id
            newSettings.facebookEdge = output.type == .timeline ? "Me" : "Page"
            newSettings.facebookEdgeId = output.id
            newSettings.emulateFMLEUserAgent = nil
            newSettings.version = nil
            newSettings.videoKeyFrameInterval = nil

            let splittedUrl = fbBroadcast.secureStreamUrl.split(separator: "/rtmp/")
            guard splittedUrl.count == 2,
                  let urlFirstPart = splittedUrl.first,
                  let urlLastPart = splittedUrl.last else {
                logger.error("Invalid Facebook secure stream url: \(fbBroadcast.secureStreamUrl)")
                throw SwitcherAPIError.invalidRequest
            }
            newSettings.url = urlFirstPart + "/rtmp/"
            newSettings.rtmpStream = String(urlLastPart)
            newSettings.permalinkUrl = "https://www.facebook.com\(fbBroadcast.permalinkUrl)"

            let metadata: FacebookMetadata = .init(contentTags: output.contentTagIds,
                                                   usedCrossposting: !output.crosspostingActions.isEmpty,
                                                   edgeName: output.name,
                                                   thumbnailUrl: liveDest.userData.thumbnailUrl,
                                                   userData: output)
            if let encoded = try? JSONEncoder().encode(metadata),
               let meta = String(data: encoded, encoding: .utf8) {
                newSettings.meta = meta
            }

            newSettings = try await facebookAPI.postSettings(settings: newSettings)

            var streamDestination = StreamDestination()
            streamDestination.name = title
            streamDestination.isDefault = true
            streamDestination.streamDestinationId = output.id
            streamDestination.streamDestinationType = newSettings.facebookEdge
            streamDestination = try await streamDestinationsAPI.postStreamDestination(streamDestination)

            let result = try await broadcastsAPI.createOutput(broadcastId: broadcastId, settings: newSettings)

            return OutputResult(liveDest: liveDest, success: result.enabled ?? false)
        } catch {
            if error.hasBeenCancelled {
                throw error
            } else {
                return OutputResult(liveDest: liveDest, success: false)
            }
        }
    }

    private func createYoutubeOutput(liveDest: LivestreamDest,
                                     output: YoutubeLivestreamUserData,
                                     description: String?,
                                     startsAt: Date?,
                                     coverImageUrl: String,
                                     isExistingPost: Bool) async throws -> OutputResult {
        do {
            let youtubeId = output.id

            guard let title = attr?.selectedChannelName,
                  let settings = attr?.selectedProfile,
                  let broadcastId = broadcast?.id else {
                return OutputResult(liveDest: liveDest, success: false)
            }

            var ytBC: YoutubeBroadcast
            let data = YoutubeBroadcast(id: youtubeId,
                                        title: title,
                                        description: description,
                                        privacy: output.youtubeSelectedEventPrivacy,
                                        kids: output.youtubeForKids,
                                        allowEmbedding: output.youtubeAllowEmbedding,
                                        startsAt: startsAt ?? Date.now)
            if isExistingPost {
                ytBC = try await youtubeAPI.updateBroadcast(broadcast: data)
            } else {
                do {
                    let youtubeBroadcast = try await youtubeAPI.createBroadcast(broadcast: data)
                    ytBC = youtubeBroadcast
                } catch {
                    let message = (error as? YoutubeError)?.userMessage
                    return OutputResult(liveDest: liveDest, success: false, reason: message)
                }
            }

            let callLivestream = try await youtubeAPI.createLivestream(id: youtubeId, title: title)

            guard let youtubeLivestream = callLivestream.0 else {
                let message = callLivestream.1?.userMessage
                return OutputResult(liveDest: liveDest, success: false, reason: message)
            }

            _ = try await YoutubeAPI.shared.bind(broadcast: ytBC, to: youtubeLivestream)

            guard let rtmpStream = youtubeLivestream.cdn.ingestionInfo?.streamName else {
                throw SwitcherAPIError.invalidRequest
            }

            guard let livestreamUrl = youtubeLivestream.cdn.ingestionInfo?.ingestionAddress else {
                throw SwitcherAPIError.invalidRequest
            }

            var newSettings = settings
            newSettings.broadcastId = broadcastId
            newSettings.channelName = title
            newSettings.youtubeStreamId = youtubeLivestream.id
            newSettings.youtubeBroadcastId = ytBC.id
            newSettings.rtmpStream = rtmpStream
            newSettings.url = livestreamUrl
            newSettings.permalinkUrl = "https://www.youtube.com/watch?v=\(ytBC.id)"

            let metadata: YoutubeMetadata = .init(username: liveDest.userData.name, userData: output)
            if let encoded = try? JSONEncoder().encode(metadata),
               let meta = String(data: encoded, encoding: .utf8) {
                newSettings.meta = meta
            }

            // this api add the id, user editable, isDirectToprovider, isdeleted, concurrencyToken, discriminator
            newSettings = try await youtubeAPI.postSettings(settings: newSettings)

            if let url = URL(string: coverImageUrl), coverImageUrl != EventService.defaultThumbnail {
                do {
                    try await YoutubeAPI.shared.setThumbnailImage(videoId: ytBC.id, imageUrl: url)
                } catch {
                    if let youtubeErr = error as? YoutubeError {
                        switch youtubeErr {
                        case .general:
                            /* TODO: - This will fall through for now as creating a thumbnail does not truly
                             prevent a youtube broadcast from being created, warning potentially in future
                            return OutputDestinationResult(liveDest: liveDest,
                                                           success: false,
                                                           reason: error.error.message)
                             */
                            break
                        }
                    }
                }
            }

            let result = try await broadcastsAPI.createOutput(broadcastId: broadcastId, settings: newSettings)

            return OutputResult(liveDest: liveDest, success: result.enabled ?? false)
        } catch {
            if error.hasBeenCancelled {
                throw error
            } else if let ytError = error as? YoutubeError {
                return OutputResult(liveDest: liveDest, success: false, reason: ytError.userMessage)
            } else {
                return OutputResult(liveDest: liveDest, success: false)
            }
        }
    }

    private func createTwitchOutput(id: String,
                                    category: TwitchCategory?,
                                    username: String?,
                                    permalinkUrl: String?,
                                    ingestServer: TwitchIngestServer?,
                                    channelName: String,
                                    twitchData: TwitchLivestreamUserData) async throws -> Bool {
        // Get the selected profile
        do {
            guard let broadcastId = broadcast?.id else {
                throw Errors.errorCreatingLivestreamingEvent
            }
            guard let settings = attr?.selectedProfile else {
                throw Errors.noSelectedProfile
            }

            // Get the twitch stream key
            let twitchStreamKeys = try await TwitchAPI.shared.getStreamKey(broadcasterId: id).data
            guard let streamKey = twitchStreamKeys.first?.streamKey else {
                throw Errors.errorCreatingLivestreamingEvent
            }

            try await TwitchAPI.shared.patchBroadcast(broadcasterId: id,
                                                      title: channelName,
                                                      categoryId: category?.id,
                                                      includeCategory: true)

            // Update the settings
            let newSettings = settings
            // If the use selected an ingest url use it
            if let ingestUrl = ingestServer?.ingestUrl {
                newSettings.url = ingestUrl
            } else { // else fetch the ingest servers and use the first
                // TODO: - Confirm the first is the by default the closest
                if let ingestUrl = try await TwitchAPI.shared.getIngestServers().ingests.first?.ingestUrl {
                    newSettings.url = ingestUrl
                } else {
                    throw Errors.errorCreatingLivestreamingEvent
                }
            }
            newSettings.broadcastId = broadcastId
            newSettings.permalinkUrl = permalinkUrl
            newSettings.channelName = channelName
            newSettings.rtmpStream = streamKey

            let metadata: TwitchMetadata = .init(username: username, broadcasterId: id, userData: twitchData)
            if let encoded = try? JSONEncoder().encode(metadata),
               let meta = String(data: encoded, encoding: .utf8) {
                newSettings.meta = meta
            }

            // Post settings
            let twitchBroadcast = try await TwitchAPI.shared.postSettings(settings: newSettings)
            // Create output
            let result = try await broadcastsAPI.createOutput(broadcastId: broadcastId, settings: twitchBroadcast)

            return result.enabled ?? false
        } catch {
            if error.hasBeenCancelled {
                throw error
            } else {
                return false
            }
        }

    }

    private func createRtmpOutput(customRtmpId: String) async throws -> Bool {
        do {
            guard let settings = bcProfileLib.getCustomRtmpSettings(withId: customRtmpId),
                  let broadcastId = broadcast?.id else {
                return false
            }
            var clonedSettings = settings.clone()
            clonedSettings.isDirectToProvider = false
            clonedSettings.userEditable = false
            clonedSettings.broadcastId = broadcastId
            clonedSettings = try await streamSettingsAPI.postSettings(clonedSettings)
            let outputResult = try await broadcastsAPI.createOutput(broadcastId: broadcastId, settings: clonedSettings)
            return outputResult.enabled ?? false
        } catch {
            if error.hasBeenCancelled {
                throw error
            } else {
                return false
            }
        }
    }

    private func finalizeBroadcast() async throws {
        guard var broadcast = broadcast,
            let input = input else {
            throw Errors.errorCreatingLivestreamingEvent
        }
        broadcast.inputId = input.id
        broadcast.broadcastStatus = .ready
        broadcast.createdAt = nil
        broadcast.updatedAt = nil
        self.broadcast = broadcast
        try await broadcastsAPI.putBroadcast(broadcast, createTwilioSyncResource: false)
    }

    private func initializeBroadcast() async throws {
        guard let broadcastId = broadcast?.id,
            let attr = attr,
            let settings = attr.selectedProfile,
            let input = input else {
            throw Errors.errorCreatingLivestreamingEvent
        }

        attr.selectedProfile = try await broadcastsAPI.initializeBroadcast(
            broadcastId: broadcastId,
            data: BroadcastsAPIInitialize(input: input,
                                          setting: settings)
        )
        self.attr = attr
    }

    func fetchExistingStreams() async -> ExistingBroadcasts {
        var result: ExistingBroadcasts = .init()
        do {
            if let projectId = SSNUserAccount.shared.userInfo.projectId {
                let providers: [StreamingProvider]
                providers = try await StreamingProvidersAPI.shared.getStreamingProviders().streamingProviders ?? []
                result.streamingProviders = providers

                let broadcasts = try await BroadcastsAPI.shared.getBroadcasts(projectId: projectId, status: .ready)

                if providers.contains(where: { $0.id == "facebook" && $0.isLinked == true }) {
                    let fbBroadcasts = try await fetchFacebookBroadcasts()
                    result.fbBroadcasts = fbBroadcasts.filter(filterUsedFacebookBroadcasts(broadcasts: broadcasts))
                }
                if providers.contains(where: { $0.id == "youtube" && $0.isLinked == true }) {
                    let youtubeBroadcasts = try await fetchYoutubeBroadcasts()
                    result.ytBroadcasts = youtubeBroadcasts.filter(filterUsedYoutubeBroadcasts(broadcasts: broadcasts))
                }
            }

        } catch {
            result.error = error
        }
        return result
    }

    private func fetchFacebookBroadcasts() async throws -> [FacebookBroadcast] {
        try await FacebookAPI.shared.getBroadcasts()
    }

    private func fetchYoutubeBroadcasts() async throws -> [YoutubeBroadcast] {
        try await YoutubeAPI.shared.getBroadcasts()
    }

    private func filterUsedFacebookBroadcasts(broadcasts: [BroadcastsAPIBroadcast]) -> ((FacebookBroadcast) -> (Bool)) {
        return { fbBroadcast in
            !broadcasts.contains(where: { switcherBroadcast in
                if let settings = switcherBroadcast.streamSettings {
                    return settings.contains(where: { $0.facebookVideoId == fbBroadcast.id })
                } else {
                    return true
                }
            })
        }
    }

    private func filterUsedYoutubeBroadcasts(broadcasts: [BroadcastsAPIBroadcast]) -> ((YoutubeBroadcast) -> (Bool)) {
        return { ytBroadcast in
            !broadcasts.contains(where: { switcherBroadcast in
                if let settings = switcherBroadcast.streamSettings {
                    return settings.contains(where: { $0.youtubeBroadcastId == ytBroadcast.id })
                } else {
                    return true
                }
            })
        }
    }
}
