//
//  TextAndGraphicsListIcons.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 12.11.19.
//  Copyright © 2019 Switcher Inc. All rights reserved.
//

import UIKit

class TextAndGraphicsListIcons: NSObject {

    public static let iconWidth: CGFloat = 20

    public static let titleTitle = NSLocalizedString("TextAndGraphicsListTableViewController.choice.title",
                                                     comment: "choice title")
    public static let titleImage = ExtMenuTableViewController.formattedIcon(named: "tg-title-icon",
                                                                            width: iconWidth)

    public static let lowerThirdTitle  = NSLocalizedString("TextAndGraphicsListTableViewController.choice.lower-third",
                                                           comment: "choice lowerthird")
    public static let lowerThirdImage  = ExtMenuTableViewController.formattedIcon(named: "tg-lower-third-icon",
                                                                                  width: iconWidth)

    public static let animatedTextTitle  =
        NSLocalizedString("TextAndGraphicsListTableViewController.choice.animated-text",
                          comment: "choice animated text")
    public static let animatedTextImage  = ExtMenuTableViewController.formattedIcon(named: "tg-animated-text-icon",
                                                                                    width: iconWidth)

    public static let timersTitle  = NSLocalizedString("TextAndGraphicsListTableViewController.choice.timers",
                                                       comment: "choice timers")
    public static let timersImage  = ExtMenuTableViewController.formattedIcon(named: "tg-timers-icon",
                                                                              width: iconWidth)

    public static let platformOverlayTitle       =
        NSLocalizedString("TextAndGraphicsListTableViewController.choice.platform-overlay",
                          comment: "choice social-overlay")
    public static let platformOverlayImage       = ExtMenuTableViewController
        .formattedIcon(named: "medialist-platform-icon",
                       width: iconWidth)

    public static let broadcastNotificationTitle       =
        NSLocalizedString("TextAndGraphicsListTableViewController.choice.broadcast-notification",
                          comment: "choice broadcast-notification")
    public static let broadcastNotificationImage       = ExtMenuTableViewController
        .formattedIcon(named: "tg-broadcast-notification-icon",
                       width: iconWidth)

    public static let cornerBugTitle      =
        NSLocalizedString("TextAndGraphicsListTableViewController.choice.corner-bug",
                          comment: "choice corner-bug")
    public static let cornerBugImage      = ExtMenuTableViewController.formattedIcon(named: "tg-corner-bug-icon",
                                                                                     width: iconWidth)

    public static let imageOverlayTitle      =
        NSLocalizedString("TextAndGraphicsListTableViewController.choice.image-overlay",
                          comment: "choice image-overlay")
    public static let imageOverlayImage      = ExtMenuTableViewController.formattedIcon(named: "tg-image-overlay-icon",
                                                                                        width: iconWidth)

    public static let imageLowerThirdTitle      =
        NSLocalizedString("TextAndGraphicsListTableViewController.choice.image-lower-third",
                          comment: "choice image-lower-third")
    public static let imageLowerThirdImage      = ExtMenuTableViewController
        .formattedIcon(named: "tg-image-lower-third-icon",
                       width: iconWidth)
    public static let imageSlideshow      = ExtMenuTableViewController
        .formattedIcon(named: "tg-image-slideshow",
                       width: iconWidth)
}
