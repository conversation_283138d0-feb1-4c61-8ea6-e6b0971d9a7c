//
//  ImageOverlaySelectTypeView.swift
//  Cap-iOS
//
//  Created by <PERSON> on 13/06/2025.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import SwiftUI

private struct ImageOverlayRowView: View {

    let title: LocalizedStringKey
    let image: UIImage
    let action: (() -> Void)

    var body: some View {
        HStack {
            Image(uiImage: image)
            Text(title)
                .textCase(.uppercase)
                .foregroundStyle(Color(LookConf.textColorTableView))
            Spacer()
            Image(.figmaChevronRight)
                .font(Font.system(size: 15, weight: .semibold))
                .foregroundColor(Color.listArrowAccessory)
        }
        .frame(height: 50)
        .contentShape(Rectangle())
        .onTapGesture {
            action()
        }
    }

}

struct ImageOverlaySelectTypeView: View {

    let prodFlow: TextAndGraphicsProdFlow

    var body: some View {
        List {

            ImageOverlayRowView(title: "TextAndGraphicsListTableViewController.choice.corner-bug",
                                image: TextAndGraphicsListIcons.cornerBugImage) {
                prodFlow.next(type: .cornerBug)
            }

            ImageOverlayRowView(title: "TextAndGraphicsListTableViewController.choice.slideshow",
                                image: TextAndGraphicsListIcons.imageSlideshow) {
                prodFlow.next(type: .slideshow)
            }

            ImageOverlayRowView(title: "TextAndGraphicsListTableViewController.choice.image-lower-third",
                                image: TextAndGraphicsListIcons.imageLowerThirdImage) {
                prodFlow.next(type: .imageLowerThird)
            }
        }
        .listStyle(.plain)
        .switcherNavigationBar(title: "TextAndGraphicsListTableViewController.choice.image-overlay",
                               hideBackButton: false)
    }
}
