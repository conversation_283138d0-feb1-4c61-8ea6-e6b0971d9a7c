//
//  MediaListIcons.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 12.11.19.
//  Copyright © 2019 Switcher Inc. All rights reserved.
//

import UIKit

class MediaListIcons {

    static let iconWidth: CGFloat = 20

    static let audioFromVideoTitle = NSLocalizedString("MediaListTableViewController.choice.audio-from-video",
                                                       comment: "choice media")
    static let audioFromVideoImage = ExtMenuTableViewController.formattedIcon(named: "medialist-myrecordings-icon",
                                                                              width: iconWidth,
                                                                              color: UIColor(named: "iconColor"))

    static let photoLibraryTitle = NSLocalizedString("MediaListTableViewController.choice.photo-library",
                                                     comment: "choice media")
    static let photoLibraryImage = ExtMenuTableViewController.formattedIcon(named: "medialist-photolibrary-icon",
                                                                            width: iconWidth,
                                                                            color: UIColor(named: "iconColor"))

    static let switcherStudioImportedAudioTitle =
        NSLocalizedString("MediaListTableViewController.choice.audio-imported",
                          comment: "choice imported audio")
    static let switcherStudioImportedAudioImage = ExtMenuTableViewController
        .formattedIcon(named: "medialist-importedmedia-icon",
                       width: iconWidth,
                       color: UIColor(named: "iconColor"))

    static let switcherStudioImportedPhotoTitle =
        NSLocalizedString("MediaListTableViewController.choice.photo-imported",
                          comment: "choice imported media")
    static let switcherStudioImportedPhotoImage = ExtMenuTableViewController
        .formattedIcon(named: "medialist-importedmedia-icon",
                       width: iconWidth,
                       color: UIColor(named: "iconColor"))

    static let switcherStudioImportedVideoTitle =
        NSLocalizedString("MediaListTableViewController.choice.video-imported",
                          comment: "choice imported media")
    static let switcherStudioImportedVideoImage = ExtMenuTableViewController
        .formattedIcon(named: "medialist-importedmedia-icon",
                       width: iconWidth,
                       color: UIColor(named: "iconColor"))

    static let sampleTitle = NSLocalizedString("MediaListTableViewController.choice.samples", comment: "choice samples")
    static let sampleImage = ExtMenuTableViewController.formattedIcon(named: "medialist-sample-icon",
                                                                      width: iconWidth,
                                                                      color: UIColor(named: "iconColor"))

    static let backgroundTitle = NSLocalizedString("MediaListTableViewController.choice.backgrounds",
                                                   comment: "choice backgrounds")
    static let backgroundImage = ExtMenuTableViewController.formattedIcon(named: "medialist-backgrounds-icon",
                                                                          width: iconWidth,
                                                                          color: UIColor(named: "iconColor"))

    static let gradientTitle = NSLocalizedString("MediaListTableViewController.choice.gradient",
                                                 comment: "choice gradient backgrounds")
    static let gradientImage = ExtMenuTableViewController.formattedIcon(named: "medialist-gradient-icon",
                                                                        width: iconWidth,
                                                                        color: UIColor(named: "iconColor"))

    static let platformTitle = NSLocalizedString("MediaListTableViewController.choice.platform-icons",
                                                 comment: "choice social icons")
    static let platformImage = ExtMenuTableViewController.formattedIcon(named: "medialist-platform-icon",
                                                                        width: iconWidth,
                                                                        color: UIColor(named: "iconColor"))

    static let cloudTitle = NSLocalizedString("MediaListTableViewController.choice.switcher-cloud",
                                              comment: "choice switcher-cloud")
    static let cloudImage = ExtMenuTableViewController.formattedIcon(named: "medialist-cloud-icon",
                                                                     width: iconWidth,
                                                                     color: UIColor(named: "iconColor"))

    static let iospickerTitle = NSLocalizedString("MediaListTableViewController.choice.ios-picker",
                                                  comment: "choice ios picker")
    static let iospickerImage = ExtMenuTableViewController.formattedIcon(named: "medialist-iospicker-icon",
                                                                         width: iconWidth,
                                                                         color: UIColor(named: "iconColor"))

    static let myRecordingsTitle = NSLocalizedString("MediaListTableViewController.choice.my-recordings",
                                                     comment: "choice imported media")
    static let myRecordingsImage = ExtMenuTableViewController.formattedIcon(named: "medialist-myrecordings-icon",
                                                                            width: iconWidth,
                                                                            color: UIColor(named: "iconColor"))

    static let brandProfileTitle = NSLocalizedString("menu.brand-profile.title", comment: "menu item")
    static let brandProfileImage = ExtMenuTableViewController.formattedIcon(named: "menu-brand-profile-icon",
                                                                            width: iconWidth,
                                                                            color: UIColor(named: "iconColor"))

    static let noImageTitle = NSLocalizedString("MediaListTableViewController.choice.no-image",
                                                comment: "choice no image")
    static let noImageIcon = ExtMenuTableViewController.formattedIcon(named: "medialist-no-image-icon",
                                                                      width: iconWidth,
                                                                      color: UIColor(named: "iconColor"))

    static let patternsTitle = NSLocalizedString("MediaListTableViewController.choice.patterns",
                                                 comment: "choice patterns")
    static let patternsImage = ExtMenuTableViewController.formattedIcon(named: "medialist-backgrounds-icon",
                                                                        width: iconWidth,
                                                                        color: UIColor(named: "iconColor"))
    static let framesTitle = NSLocalizedString("MediaListTableViewController.choice.frames", comment: "choice frames")
    static let framesImage = ExtMenuTableViewController.formattedIcon(named: "medialist-sample-icon",
                                                                      width: iconWidth,
                                                                      color: UIColor(named: "iconColor"))
}

enum GenericLogoType {
    case facebook
    case twitter
    case instagram
    case youtube
}
