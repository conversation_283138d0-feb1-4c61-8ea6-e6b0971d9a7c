//
//  PhotoProdFlow.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 04.06.19.
//  Copyright © 2019 RecoLive Sàrl. All rights reserved.
//

import UIKit
import SwiftUI
import InternalBridging
import MobileCoreServices
import Photos
import Combine
import UniformTypeIdentifiers

/**
 * PhotoProdFlow is the class that manage production flow for photos
 * coming from Photo Library, Switcher Studio App, Samples, Switcher Cloud
 */
@MainActor
class PhotoProdFlow: MediaProdFlow,
                     AssetGridDelegate,
                     VMakerEditorViewControllerDelegate,
                     LocalMediaTableViewDelegate,
                     CloudAssetsListViewControllerDelegate,
                     AssetBuilderDelegate,
                     UIDocumentPickerDelegate,
                     AlbumListTableViewDelegate,
                     BackgroundsCollectionDelegate,
                     GradientGeneratorDelegate,
                     MediaGridViewControllerDelegate {

    let vContext: VContext?
    let primaryTypes: [MediaSourceType]
    let secondaryTypes: [MediaSourceType]

    init(vContext: VContext?,
         targetAspectRatio: GmuRatioI32,
         mainMixer: MainMixer?,
         insertionRank: MultilevelRank?,
         primaryTypes: [MediaSourceType] = [],
         secondaryTypes: [MediaSourceType] = []) {
        self.vContext = vContext
        var types: [MediaSourceType] = []
        if primaryTypes.isEmpty {
            guard secondaryTypes.isEmpty else { FatalLogger.shared.logFatalError() }
            types = [.photoLibrary, .switcherStudioPhotoImported, .iosPicker, .cloudSwitcher, .sample]
        } else {
            types = primaryTypes
        }
        guard types != [.cloudSwitcher] else { FatalLogger.shared.logFatalError() }
        if !(SSNUserAccount.shared.userInfo?.isCloudAssetAllowed ?? false) {
            types = types.filter { $0 != .cloudSwitcher }
        }
        self.primaryTypes = types
        self.secondaryTypes = secondaryTypes
        super.init(targetAspectRatio: targetAspectRatio, insertionRank: insertionRank, mainMixer: mainMixer)
    }

    var photoBuilder: PhotoAssetBuilder? {
        get {
            if let builder = assetBuilder {
                return (builder as! PhotoAssetBuilder)
            }
            return nil
        }
        set { assetBuilder = newValue }
    }

    public var firstViewControllerTitle: String = NSLocalizedString("MediaListTableViewController.photo.title",
                                                                    comment: "list sources medias title photo")

    private var preloadingContext: PhotoAssetBuilder.PreloadingContext? {
        if let vc = vContext {
            return .init(vContext: vc, aspectRatio: targetAspectRatio.doubleValue)
        } else {
            return nil
        }
    }

    // MARK: - Init

    deinit {
        // print("PhotoProdFlow::deinit")
    }

    // MARK: - Private

    private enum PresentationMode { case navigation, modal }

    private func createViewController(for type: MediaSourceType) -> (UIViewController, PresentationMode) {
        switch type {
        case .photoLibrary:
            let grid: AssetGridViewController = AssetGridViewController(mediaType: .photo)
            grid.delegate = self
            return (grid, .navigation)
        case .switcherStudioPhotoImported:
            let localPhotoList = LocalMediaTableViewController(mediaType: .photo)
            localPhotoList.delegate = self
            return (localPhotoList, .navigation)
        case .sample:
            let samplePattern = MediaGridViewModel.MediaItem(
                url: "res:test-image",
                name: NSLocalizedString("SamplesCollectionViewController.choice.pattern",
                                        comment: "pattern sample")
            )
            let sampleSlide = MediaGridViewModel.MediaItem(
                url: "res:sample-slide",
                name: NSLocalizedString("SamplesCollectionViewController.choice.slide",
                                        comment: "slide sample")
            )
            let sampleCornerBug = MediaGridViewModel.MediaItem(
                url: "res:app-icon",
                name: NSLocalizedString("SamplesCollectionViewController.choice.cornerbug",
                                        comment: "cornerbug sample")
            )
            let fullscreenImageName = NSLocalizedString("SamplesCollectionViewController.choice.image-fullscreen",
                                                        comment: "image fullscreen sample")
            let sampleImageFullscreen = MediaGridViewModel.MediaItem(
                url: "res:sample-image",
                name: "\(fullscreenImageName) 1"
            )
            let sampleVerticalImageFullscreen = MediaGridViewModel.MediaItem(
                url: "res:sample-image-v",
                name: "\(fullscreenImageName) 2"
            )
            let mediaGridModel = MediaGridViewModel()
            mediaGridModel.items = [
                samplePattern,
                sampleSlide,
                sampleCornerBug,
                sampleImageFullscreen,
                sampleVerticalImageFullscreen
            ]
            mediaGridModel.delegate = self
            let sampleVC = MediaGridViewController(viewModel: mediaGridModel)
            sampleVC.title = MediaListIcons.sampleTitle
            return (sampleVC, .navigation)
        case .background:
            let bgVC = BackgroundsCollectionViewController()
            bgVC.delegate = self
            return (bgVC, .navigation)
        case .gradient:
            let gradientVC = GradientGeneratorViewController(preview: GradientPreviewerViewController(),
                                                             previewAspectRatio: 16.0 / 9.0)
            gradientVC.delegate = self
            return (gradientVC, .navigation)
        case .cloudSwitcher:
            let cloudVC = CloudAssetsListViewController(assetType: .image, targetAspectRatio: targetAspectRatio)
            cloudVC.delegate = self
            return (cloudVC, .navigation)
        case .iosPicker:
            let documentPicker = UIDocumentPickerViewController(forOpeningContentTypes: [UTType.png, UTType.jpeg],
                                                                asCopy: true)
            documentPicker.allowsMultipleSelection = false
            documentPicker.delegate = self
            documentPicker.modalPresentationStyle = .pageSheet
            return (documentPicker, .modal)
        case .logoPlatform:
            let platform = self.createPlatformSelectionComponent()
            return (platform, .navigation)
        case .logoBrandProfile:
            let mediaGridModel = MediaGridViewModel()
            mediaGridModel.delegate = self
            let vc = MediaGridViewController(viewModel: mediaGridModel)
            let dataSource = BrandProfileManager.shared.$brandProfile
                .map {
                    $0?.logos.keys.map { MediaGridViewModel.MediaItem(url: MediaUtil.url(mid: $0)) } ?? []
                }
            dataSource.assign(to: &mediaGridModel.$items)
            let config = NSLocalizedString("BrandProfileSubtableController.action.configure", comment: "toolbar button")
            vc.navigationItem.rightBarButtonItem = UIBarButtonItem(title: config, style: .plain, target: self,
                                                                   action: #selector(configureBrandProfileAction))
            vc.title = MediaListIcons.brandProfileTitle
            return (vc, .navigation)
        case .pattern:
            let pattern1 = MediaGridViewModel.MediaItem(url: "res:pattern-1", transparencyFillColor: .black)
            let mediaGridModel = MediaGridViewModel()
            mediaGridModel.items = [pattern1]
            mediaGridModel.delegate = self
            let patternsVC = MediaGridViewController(viewModel: mediaGridModel)
            patternsVC.title = MediaListIcons.patternsTitle
            return (patternsVC, .navigation)
        case .frame:
            let mediaGridModel = MediaGridViewModel()
            let pattern1 = MediaGridViewModel.MediaItem(url: "res:pattern-1", transparencyFillColor: .black)
            mediaGridModel.items = [pattern1]
            mediaGridModel.delegate = self
            let patternsVC = MediaGridViewController(viewModel: mediaGridModel)
            patternsVC.title = MediaListIcons.patternsTitle
            return (patternsVC, .navigation)
        default:
            FatalLogger.shared.logFatalError()
        }
    }

    private func createMenuItem(for type: MediaSourceType) -> ExtMenuActiveItem {

        assert(SSNUserAccount.shared.userInfo.isCloudAssetAllowed || type != .cloudSwitcher)

        // TODO: replace by an array

        switch type {
        case .photoLibrary:
            return ExtMenuActiveItem(title: MediaListIcons.photoLibraryTitle,
                                     image: MediaListIcons.photoLibraryImage,
                                     options: [.disclosureIndicator, .staySelected],
                                     action: { [weak self] in self!.selectMedia(.photoLibrary) })

        case .switcherStudioPhotoImported:
            return ExtMenuActiveItem(title: MediaListIcons.switcherStudioImportedPhotoTitle,
                                     image: MediaListIcons.switcherStudioImportedPhotoImage,
                                     options: [.disclosureIndicator, .staySelected],
                                     action: { [weak self] in self!.selectMedia(.switcherStudioPhotoImported) })

        case .iosPicker:
            return ExtMenuActiveItem(title: MediaListIcons.iospickerTitle,
                                     image: MediaListIcons.iospickerImage,
                                     options: [.disclosureIndicator],
                                     action: { [weak self] in self!.selectMedia(.iosPicker) })

        case .cloudSwitcher:
            return ExtMenuActiveItem(title: MediaListIcons.cloudTitle,
                                     image: MediaListIcons.cloudImage,
                                     options: [.disclosureIndicator, .staySelected],
                                     action: { [weak self] in self!.selectMedia(.cloudSwitcher) })

        case .logoBrandProfile:
            return ExtMenuActiveItem(title: MediaListIcons.brandProfileTitle,
                                     image: MediaListIcons.brandProfileImage,
                                     options: [.disclosureIndicator, .staySelected],
                                     action: { [weak self] in self!.selectMedia(.logoBrandProfile) })

        case .background:
            return ExtMenuActiveItem(title: MediaListIcons.backgroundTitle,
                                     image: MediaListIcons.backgroundImage,
                                     options: [.disclosureIndicator, .staySelected],
                                     action: { [weak self] in self!.selectMedia(.background) })

        case .gradient:
            return ExtMenuActiveItem(title: MediaListIcons.gradientTitle,
                                     image: MediaListIcons.gradientImage,
                                     options: [.disclosureIndicator, .staySelected],
                                     action: { [weak self] in self!.selectMedia(.gradient) })

        case .logoPlatform:
            return ExtMenuActiveItem(title: MediaListIcons.platformTitle,
                                     image: MediaListIcons.platformImage,
                                     options: [.disclosureIndicator, .staySelected],
                                     action: { [weak self] in self!.selectMedia(.logoPlatform) })

        case .sample:
            return ExtMenuActiveItem(title: MediaListIcons.sampleTitle,
                                     image: MediaListIcons.sampleImage,
                                     options: [.disclosureIndicator, .staySelected],
                                     action: { [weak self] in self!.selectMedia(.sample) })

        case .pattern:
            return ExtMenuActiveItem(title: MediaListIcons.patternsTitle,
                                     image: MediaListIcons.patternsImage,
                                     options: [.disclosureIndicator, .staySelected],
                                     action: { [weak self] in self!.selectMedia(.pattern) })
        case .frame:
            return ExtMenuActiveItem(title: MediaListIcons.framesTitle,
                                     image: MediaListIcons.framesImage,
                                     options: [.disclosureIndicator, .staySelected],
                                     action: { [weak self] in self!.selectMedia(.frame) })
        case .noImage:
            return ExtMenuActiveItem(title: MediaListIcons.noImageTitle,
                                     image: MediaListIcons.noImageIcon,
                                     options: [.staySelected],
                                     action: { [weak self] in self!.selectMedia(.noImage) })
        default:
            FatalLogger.shared.logFatalError()
        }
    }

    private func addMoreButton(to vc: UIViewController) {
        vc.navigationItem
            .rightBarButtonItem =
            UIBarButtonItem(title: NSLocalizedString("prod-flow.photo.button.more.short", comment: "navbar button"),
                            style: .plain,
                            target: self,
                            action: #selector(moreAction))
    }

    // MARK: - ****** OVERRIDE PARENT ******

    // MARK: ViewController

    override func createFirstViewController() -> UIViewController? {
        if primaryTypes == [.logoPlatform] {
            let (vc, presentation) = createViewController(for: .logoPlatform)
            assert(presentation == .navigation)
            if !secondaryTypes.isEmpty {
                addMoreButton(to: vc)
            }
            return vc
        } else {
            var menuItems: [ExtMenuItem] = primaryTypes.map { createMenuItem(for: $0) }
            if !secondaryTypes.isEmpty {
                menuItems
                    .append(ExtMenuButtonItem(title: NSLocalizedString("prod-flow.photo.button.more.long",
                                                                       comment: "tableview row button"),
                                              action: { [weak self] in self!.moreAction() }))
            }
            let menu = OverlayableMenuTableViewController(items: menuItems)
            menu.look = LookConf.brightLook
            menu.title = firstViewControllerTitle
            return menu
        }
    }

    // MARK: - ****** DELEGATES ******

    // MARK: Select Media

    func selectMedia(_ type: MediaSourceType) {
        if type == .noImage {
            self.lastStep()
        } else {
            let (vc, mode) = self.createViewController(for: type)
            if mode == .navigation {
                self.next(viewController: vc)
            } else {
                self.presentOver(viewController: vc)
            }
        }
    }

    // MARK: Logos/Icons List

    func createPlatformSelectionComponent() -> UIViewController {
        let socialPickerView = SocialPlatformSelectionView()
            .onSelectedPlatform { url in
                self.preparePhotoEditor(imageUrl: url)
            }
        let hosting = SocialPlatformViewController(rootView: socialPickerView)
        return hosting
    }

    // MARK: AssetGridDelegate

    func assetGridDidSelectAsset(_ asset: PHAsset) {
        if asset.mediaType == PHAssetMediaType.image {
            self.preparePhotoEditorForPHAsset(asset: asset)
        } else {
            let type = asset.mediaType.rawValue
            print("PhotoProdFlow::assetGridDidSelectAsset asset selected is not an image but \(type)")
        }
    }

    func assetGridDisplayAlbumList() {
        let albumListVC = AlbumListTableViewController(mediaType: .photo)
        albumListVC.delegate = self
        self.next(viewController: albumListVC)
    }

    // MARK: VMakerEditorViewControllerDelegate

    func vmakerEditorDidAppear(_ editor: VMakerEditorViewController) {
        // Do nothing for Photo
    }

    func vmakerEditorDidDisappear(_ editor: VMakerEditorViewController) {
        // in case if we go back from the editor to choice list
        // the builder finish the job by destroying image, artwork and all elements related
        // but the prod flow is still there, and we don't stop it
        if let builder = self.photoBuilder {
            builder.finish()
        }
    }

    // called when the user press <Cancel> or <Done>
    // quit ArtworkEditorViewController
    func vmakerEditorDidEndEditing(_ editor: VMakerEditorViewController, success: Bool) {

        // there is no cancel button
        assert(success)

        #if SAVE_AS_EMBEDDED_ARTWORK
        var props = ThumbnailGenerationProperties()
        props.final_width = 960
        props.final_height = 540
        props.in_not_out = true
        props.level = 1
        let thumbnail: ArtworkThumbnail? = editor.generateThumbnail(properties: props)
        #else
        let thumbnail: ArtworkThumbnail? = editor.generateThumbnail()
        #endif

        if let art = photoBuilder!.artwork {
            art.thumbnail = thumbnail
        } else {
            print("PhotoProdFlow::vmakerEditorDidEndEditing artwork is nil")
        }

        self.terminate()
    }

    // MARK: LocalMediaTableViewDelegate

    func localMediaTableView(_ collection: LocalMediaTableViewController, didSelectAsset: URL) {
        print("PhotoProdFlow::localMediaCollectionDelegate selected item \(didSelectAsset)")

        let resolvedUrl = UrlResolver.default.weaklyResolve(didSelectAsset)

        if resolvedUrl != nil {
            let ext = resolvedUrl?.pathExtension.lowercased() ?? "extension not found"
            print("PhotoProdFlow::resolvedUrl extension \(ext)")

            self.preparePhotoEditor(imageUrl: didSelectAsset)
        } else {
            print("PhotoProdFlow::localMediaCollection asset selected is not an image")
        }
    }

    // MARK: GradientGeneratorDelegate

    func gradientGeneratorDidCreateFile(imageUrl: URL) {
        self.preparePhotoEditor(imageUrl: imageUrl)
    }

    // MARK: BackgroundsCollectionDelegate

    func backgroundDidSelectFile(filename: String, mid: String) {
        self.preparePhotoEditorForBundlePhoto(filename: filename, mid: mid)
    }

    func backgroundDidSelectSubtype(subtype: BackgroundType) {
        let bgVC = BackgroundsCollectionViewController(bgType: subtype, isSubtype: true)
        bgVC.delegate = self
        self.next(viewController: bgVC)
    }

    // MARK: CloudAssetsListViewControllerDelegate

    #if CONFIG_SWITCHER_CLOUD

    func cloudAssetsListDidFinish() {
        self.terminate()
    }

    func cloudAssetsDidSelect(asset: CloudAsset) {
        print("PhotoProdFlow::cloudAssetsDidSelectDelegate selected item \(asset.url), MID \(asset.id)")

        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        self.preparePhotoEditorForCloudAsset(cloudAsset: asset)
    }

    #endif

    // MARK: - AssetBuilderDelegate

    func assetBuilderDidSucceed(_: GenericAssetBuilder) {
        if self.photoBuilder != nil {
            self.lastStep()
        } else {
            print("PhotoProdFlow::assetBuilderDidSucceed no builder available")
        }
    }

    func assetBuilderDidAbortByUser(_: GenericAssetBuilder) {
        if let builder = self.photoBuilder {
            builder.dispose()
            self.photoBuilder = nil
        }
    }

    // MARK: - UIDocumentPickerDelegate

    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        guard urls.count > 0 else {
            return
        }
        debugPrint("PhotoProdFlow::didPickDocumentsAt \(urls[0])")
        guard let url = MediaLibrary.shared.importFile(url: urls[0]) else {
            return
        }
        self.preparePhotoEditor(imageUrl: url)
    }

    func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
        // print("documentPicker:: documentPickerWasCancelled")
    }

    // MARK: - AlbumListTableViewDelegate

    func albumListDidSelectAlbum(_ album: AlbumModel) {
        let grid: AssetGridViewController = AssetGridViewController(mediaType: .photo, album: album)
        grid.delegate = self
        self.next(viewController: grid)
    }

    // MARK: - ***** UTILITY *****

    public static func fileExtensionForUTI(uti: String?) -> String? {
        if "public.jpeg" == uti {
            return "jpg"
        }
        if "public.png" == uti {
            return "png"
        }
        return "?"
    }

    // MARK: - PhotoBuilder

    func preparePhotoEditor(imageUrl: URL) {
        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        // create and start photo builder with sourceURL
        let options: PhotoAssetBuilderOptions = self.mustAssetBeSavedBeforeLastStep ? .saveAssetWhenLoading : []
        if imageUrl.scheme!.lowercased() == "res" {
            photoBuilder = ResPhotoAssetBuilder(url: imageUrl, photoType: .image,
                                                overlayable: self.currentViewController as! (any Overlayable),
                                                targetAspectRatio: targetAspectRatio, options: options,
                                                preloadingContext: preloadingContext)
        } else if let mid = MediaUtil.mid(url: imageUrl), EmbeddedAssetStock.shared.asset(mid: mid) != nil {
            photoBuilder = EmbeddedAssetBuilder(mid: mid, photoType: .image,
                                                overlayable: self.currentViewController as! (any Overlayable),
                                                targetAspectRatio: targetAspectRatio, options: options,
                                                preloadingContext: preloadingContext)
        } else {
            photoBuilder = LocalPhotoAssetBuilder(srcUrl: imageUrl, photoType: .image,
                                                  overlayable: self.currentViewController as? (any Overlayable),
                                                  targetAspectRatio: targetAspectRatio, options: options,
                                                  preloadingContext: preloadingContext)
        }
        photoBuilder!.delegate = self
        photoBuilder!.start()
    }

    func preparePhotoEditorForBundlePhoto(filename: String, mid: String) {
        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        let options: PhotoAssetBuilderOptions = .saveAssetWhenLoading
        // create and start photo builder with bundle filename and mid
        photoBuilder = BundlePhotoAssetBuilder(fileName: filename, mid: mid, photoType: .image,
                                               overlayable: self.currentViewController as! (any Overlayable),
                                               targetAspectRatio: targetAspectRatio, options: options,
                                               preloadingContext: preloadingContext)

        photoBuilder!.delegate = self
        photoBuilder!.start()
    }

    func preparePhotoEditorForPHAsset(asset: PHAsset) {
        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        // create and start photo builder with asset
        let options: PhotoAssetBuilderOptions = self.mustAssetBeSavedBeforeLastStep ? .saveAssetWhenLoading : []
        photoBuilder = PHLibPhotoAssetBuilder(assetPH: asset, photoType: .image,
                                              overlayable: self.currentViewController as! (any Overlayable),
                                              targetAspectRatio: targetAspectRatio, options: options,
                                              preloadingContext: preloadingContext)
        photoBuilder!.delegate = self
        photoBuilder!.start()
    }

    #if CONFIG_SWITCHER_CLOUD
    func preparePhotoEditorForCloudAsset(cloudAsset: CloudAsset) {
        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        // create and start photo builder with cloudAsset
        let options: PhotoAssetBuilderOptions = self.mustAssetBeSavedBeforeLastStep ? .saveAssetWhenLoading : []
        photoBuilder = SwitcherCloudPhotoAssetBuilder(cloudAsset: cloudAsset, photoType: .image,
                                                      overlayable: self.currentViewController as! (any Overlayable),
                                                      targetAspectRatio: targetAspectRatio, options: options,
                                                      preloadingContext: preloadingContext)
        photoBuilder!.delegate = self
        photoBuilder!.start()
    }
    #endif

    // MARK: - customizable last step

    // for PhotoPicker, we need the asset to be saved during loading process
    var mustAssetBeSavedBeforeLastStep: Bool {
        FatalLogger.shared.logFatalError("should be override by subclasses")
    }

    // last step
    // for PhotoPicker : we just need to get Url
    // for PhotoArtwork : we get an artwork and display photo editor
    func lastStep() {
        FatalLogger.shared.logFatalError("should be override by subclasses")
    }

    // MARK: - MediaGridViewControllerDelegate

    func mediaGridDidSelect(url: URL) {
        self.preparePhotoEditor(imageUrl: url)
    }

    // MARK: - Actions

    @objc private func configureBrandProfileAction() {
        let vc = HelpWebPageViewController(settingsEntryKey: .brandProfileUrl)
        vc.title = NSLocalizedString("menu.brand-profile.title", comment: "menu item")
        vc.cancelOption = .cancelButton
        vc.helpWebPageDelegate = self
        vc.isModalInPresentation = true
        let nav = ExtendedNavigationController()
        nav.pushViewController(vc, animated: false)
        nav.modalPresentationStyle = .formSheet
        navigationController?.present(nav, animated: true, completion: nil)
        Analytics.shared.reportScreenVisit(name: "Brand Profile Editor", properties: ["from": "photo-prod-flow"])
    }

    @objc private func moreAction() {
        if let vc = currentViewController as? OverlayableMenuTableViewController, primaryTypes.count > 1 {
            // a menu is already visible - let's update it instead of pushing a new one
            var types = primaryTypes + secondaryTypes
            // put "No Image" at the end
            if let index = types.firstIndex(of: .noImage) {
                types.move(fromOffsets: IndexSet(integer: index), toOffset: types.count)
            }
            let menuItems = types.map { createMenuItem(for: $0) }
            vc.items = menuItems
        } else {
            let menuItems = secondaryTypes.map { createMenuItem(for: $0) }
            let menu = OverlayableMenuTableViewController(items: menuItems)
            menu.look = LookConf.brightLook
            menu.title = firstViewControllerTitle
            self.next(viewController: menu)
        }
    }
}

extension PhotoProdFlow: HelpWebPageDelegate {
    func helpWebPageAsksToDismiss(_ helpWebPage: HelpWebPageViewController) {
        helpWebPage.dismiss(animated: true, completion: nil)
        BrandProfileManager.shared.refresh()
    }
}
