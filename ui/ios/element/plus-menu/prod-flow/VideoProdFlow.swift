//
//  VideoProdFlow.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 18.06.19.
//  Copyright © 2019 RecoLive Sàrl. All rights reserved.
//

import UIKit
import InternalBridging
import MobileCoreServices
import Photos

enum PHAssetCustomError: Error, CustomStringConvertible {
    case notSupported
    case notLocal
    case noValidSource
    case notResolvedUrl
    case noAVAsset
    case cancelRequest

    var description: String {
        switch self {
        // Use Internationalization, as appropriate.
        case .notSupported: return  NSLocalizedString("PHAssetCustomError.notSupported.msg", comment: "")
        case .notLocal: return NSLocalizedString("PHAssetCustomError.notLocal.msg", comment: "")
        case .noValidSource: return NSLocalizedString("PHAssetCustomError.noValidSource.msg", comment: "")
        case .notResolvedUrl: return NSLocalizedString("PHAssetCustomError.notResolvedUrl.msg", comment: "")
        case .noAVAsset: return NSLocalizedString("PHAssetCustomError.noAVAsset.msg", comment: "")
        case .cancelRequest: return NSLocalizedString("PHAssetCustomError.cancelRequest.msg", comment: "")
        }
    }
}

/**
 ** VideoProdFlow is the class that manage production flow for videos
 ** coming from Photo Library, Switcher Studio App, Samples, Switcher Cloud
 **/
class VideoProdFlow: MediaProdFlow, AssetGridDelegate, LocalMediaTableViewDelegate,
    CloudAssetsListViewControllerDelegate, VMakerEditorViewControllerDelegate, AssetBuilderDelegate,
    UIDocumentPickerDelegate, AlbumListTableViewDelegate {

    var videoBuilder: VideoAssetBuilder? {
        get {
            if let builder = assetBuilder {
                return (builder as! VideoAssetBuilder)
            }
            return nil
        }
        set { assetBuilder = newValue }
    }

    // MARK: - Init

    deinit {
        // print("VideoProdFlow::deinit")
    }

    // MARK: - ****** OVERRIDE PARENT ******

    // MARK: ViewController
    override func createFirstViewController() -> UIViewController? {
        return createMenu()
    }

    override func terminate() {
        guard let builder = self.assetBuilder else {
            FatalLogger.shared.logFatalError("MediaProdFlow::terminate AssetBuilder is nil, can't save source")
        }
        builder.saveSource(insertionRank: self.insertionRank!)

        super.terminate()
    }

    // MARK: - VIDEO EDITOR

    private func pushVideoEditor() {
        guard videoBuilder != nil else {
            FatalLogger.shared.logFatalError("VideoProdFlow::pushVideoEditor error, no videoBuilder")
        }
        if let playedSource = videoBuilder!.playedSource {
            eventuallyApplyBrandProfile(to: playedSource)
            // create video editor
            let vc = PlayedVideoEditorViewController(playedSource: playedSource,
                                                     mainMixer: mainMixer,
                                                     targetAspectRatio: targetAspectRatio)
            vc.delegate = self
            vc.hasDoneButton = true
            vc.look = LookConf.brightLook

            // display video editor
            if videoBuilder != nil {
                self.next(viewController: vc)
            } else {
                print("VideoProdFlow::pushArtworkEditor NO BUILDER")
            }
        } else {
            FatalLogger.shared.logFatalError("VideoProdFlow::pushVideoEditor error, no played source")
        }

    }

    func selectMedia(_ type: MediaSourceType) {
        switch type {
        case .photoLibrary:
            let grid: AssetGridViewController = AssetGridViewController(mediaType: .video)
            grid.delegate = self
            self.next(viewController: grid)
        case .switcherStudioVideoImported:
            let localPhotoList = LocalMediaTableViewController(mediaType: .video)
            localPhotoList.delegate = self
            self.next(viewController: localPhotoList)
        case .switcherStudioVideoMyRecording:
            let localPhotoList = LocalMediaTableViewController(mediaType: .video, onlyRecordedVideos: true)
            localPhotoList.delegate = self
            self.next(viewController: localPhotoList)
        case .cloudSwitcher:
            #if CONFIG_SWITCHER_CLOUD
            // self.showAlertForCloud()
            let cloudVC = CloudAssetsListViewController(assetType: .video, targetAspectRatio: targetAspectRatio)
            cloudVC.delegate = self
            self.next(viewController: cloudVC)
            #endif
        case .iosPicker:
            let documentPicker =
                UIDocumentPickerViewController(forOpeningContentTypes: [UTType.quickTimeMovie, UTType.mpeg4Movie],
                                               asCopy: true)
            documentPicker.allowsMultipleSelection = false
            documentPicker.delegate = self
            documentPicker.modalPresentationStyle = .pageSheet
            self.presentOver(viewController: documentPicker)
        case .sample:
            let mediaGridViewModel = MediaGridViewModel()
            let sampleVideo1 = MediaGridViewModel.VideoItem(
                url: "res:sample-video",
                name: NSLocalizedString("SamplesCollectionViewController.choice.video-cereals",
                                        comment: "video sample"),
                thumbnail: "sample-video-thumbnail.png")
            let sampleVideo2 = MediaGridViewModel.VideoItem(
                url: "res:sample-video-v",
                name: NSLocalizedString("SamplesCollectionViewController.choice.video-ocean",
                                        comment: "video sample"),
                thumbnail: "sample-video-v-thumbnail.png")
            mediaGridViewModel.items = [sampleVideo1, sampleVideo2]
            mediaGridViewModel.delegate = self
            let sampleVC = MediaGridViewController(viewModel: mediaGridViewModel)
            sampleVC.title = MediaListIcons.sampleTitle
            self.next(viewController: sampleVC)
        default:
            print("VideoProdFlow::mediaListDidSelectPhotoType delegate \(type) not implemented")
        }
    }

    // MARK: - ****** DELEGATES ******

    // MARK: AssetGridDelegate

    func assetGridDidSelectAsset(_ asset: PHAsset) {

        if asset.mediaType == PHAssetMediaType.video {
            self.prepareVideoEditorForPHAsset(asset: asset)
        } else {
            print("VideoProdFlow::assetGridDidSelectAsset asset selected is not a video but \(asset.mediaType) ")
        }
    }

    func assetGridDisplayAlbumList() {
        let albumListVC = AlbumListTableViewController(mediaType: .video)
        albumListVC.delegate = self
        self.next(viewController: albumListVC)
    }

    // MARK: LocalMediaTableViewDelegate

    func localMediaTableView(_ tableView: LocalMediaTableViewController, didSelectAsset: URL) {
        print("VideoProdFlow::localMediaTableViewDelegate selected item \(didSelectAsset)")

        self.prepareVideoEditor(srcUrl: didSelectAsset)
    }

    // MARK: CloudAssetsListViewControllerDelegate

    #if CONFIG_SWITCHER_CLOUD

    func cloudAssetsListDidFinish() {
        self.terminate()
    }

    func cloudAssetsDidSelect(asset: CloudAsset) {
        print("VideoProdFlow::cloudAssetsDidSelectDelegate selected item \(asset.url), MID \(asset.id)")

        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        self.displayVideoEditorForCloudAsset(cloudAsset: asset)
    }

    #endif

    // MARK: VMakerEditorViewControllerDelegate

    func vmakerEditorDidAppear(_ editor: VMakerEditorViewController) {

    }

    func vmakerEditorDidDisappear(_ editor: VMakerEditorViewController) {
        // in case if we go back from the editor to choice list
        // the builder finish the job by destroying image, artwork and all elements related
        // but the prod flow is still there, and we don't stop it
        if let builder = self.videoBuilder {
            builder.finish()
        }
    }

    // called when the user press <Cancel> or <Done>
    // quit ArtworkEditorViewController
    func vmakerEditorDidEndEditing(_ editor: VMakerEditorViewController, success: Bool) {

        // there is no cancel button
        assert(success)

        self.videoBuilder?.playedSource?.thumbnail = editor.generateThumbnail()

        self.terminate()
    }

    // MARK: - AssetBuilderDelegate

    func assetBuilderDidSucceed(_: GenericAssetBuilder) {
        if self.videoBuilder != nil {
            self.pushVideoEditor()
        } else {
            print("VideoProdFlow::assetBuilderDidSucceed no builder available")
        }
    }

    func assetBuilderDidAbortByUser(_: GenericAssetBuilder) {
        if let builder = self.videoBuilder {
            builder.dispose()
            self.videoBuilder = nil
        }
    }

    // MARK: - UIDocumentPickerDelegate

    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        guard urls.count > 0 else {
            return
        }
        print("VideoProdFlow::didPickDocumentsAt \(urls[0])")

        /*
         * Moving files into the Media Library could take time if these files
         * are on the same logical disk.
         * However, it seems that the UIDocumentPickerViewController takes in
         * charge the copy and makes the files available in the app inbox.
         * So, here, we just have to move it in the Media library, which is
         * super fast.
         */

        // Before opening it we need to move the file into the Media Library
        guard let url = MediaLibrary.shared.importFile(url: urls[0]) else {
            return
        }

        // Open video editor
        self.prepareVideoEditor(srcUrl: url)
    }

    func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
        // print("documentPicker:: documentPickerWasCancelled ")
    }

    // MARK: - AlbumListTableViewDelegate

    func albumListDidSelectAlbum(_ album: AlbumModel) {
        let grid: AssetGridViewController = AssetGridViewController(mediaType: .video, album: album)
        grid.delegate = self
        self.next(viewController: grid)
    }

    // MARK: - VideoBuilder

    func prepareVideoEditor(srcUrl: URL) {
        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        // create and start video builder with url
        guard let overlayable = self.currentViewController as? (any Overlayable) else {
            FatalLogger.shared.logFatalError()
        }
        videoBuilder = LocalVideoAssetBuilder(srcUrl: srcUrl, overlayable: overlayable,
                                              targetAspectRatio: targetAspectRatio)
        videoBuilder!.delegate = self
        videoBuilder!.start()
    }

    func prepareVideoEditorForRes(url: URL) {
        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")

        videoBuilder = ResVideoAssetBuilder(url: url,
                                            overlayable: self.currentViewController as! (any Overlayable),
                                            targetAspectRatio: targetAspectRatio)

        videoBuilder!.delegate = self
        videoBuilder!.start()
    }

    func prepareVideoEditorForPHAsset(asset: PHAsset) {
        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        // create and start video builder asset
        videoBuilder = PHLibVideoAssetBuilder(assetPH: asset,
                                              overlayable: self.currentViewController as! (any Overlayable),
                                              targetAspectRatio: targetAspectRatio)
        videoBuilder!.delegate = self
        videoBuilder!.start()
    }

    #if CONFIG_SWITCHER_CLOUD
    func displayVideoEditorForCloudAsset(cloudAsset: CloudAsset) {
        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        // create and start video builder asset
        videoBuilder = SwitcherCloudVideoAssetBuilder(cloudAsset: cloudAsset,
                                                      overlayable: self.currentViewController as! (any Overlayable),
                                                      targetAspectRatio: targetAspectRatio)
        videoBuilder!.delegate = self
        videoBuilder!.start()
    }
    #endif

    // MARK: - Menu Video list choices

    private func createMenu() -> OverlayableMenuTableViewController {

        var menuItems = [ExtMenuItem]()

        // MEDIAS FROM PHOTOLIB
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.photoLibraryTitle,
                                           image: MediaListIcons.photoLibraryImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self!.selectMedia(.photoLibrary)
        }))

        // SWITCHER STUDIO IMPORTED VIDEOS
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.switcherStudioImportedVideoTitle,
                                           image: MediaListIcons.switcherStudioImportedVideoImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self!.selectMedia(.switcherStudioVideoImported)
        }))

        // MY RECORDINGS
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.myRecordingsTitle,
                                           image: MediaListIcons.myRecordingsImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self!.selectMedia(.switcherStudioVideoMyRecording)
        }))

        // IOS PICKER
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.iospickerTitle,
                                           image: MediaListIcons.iospickerImage,
                                           options: [.disclosureIndicator],
                                           action: { [weak self] in
            self!.selectMedia(.iosPicker)
        }))

        // CLOUD

        // Add Cloud choice if user is allowed to access it
        #if CONFIG_SWITCHER_PRO
        let userInfo = SSNUserAccount.shared.userInfo!
        if userInfo.isCloudAssetAllowed {
            menuItems.append(ExtMenuActiveItem(title: MediaListIcons.cloudTitle,
                                               image: MediaListIcons.cloudImage,
                                               options: [.disclosureIndicator, .staySelected],
                                               action: { [weak self] in
                self!.selectMedia(.cloudSwitcher)
            }))
        }
        #endif

        // SAMPLE
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.sampleTitle,
                                           image: MediaListIcons.sampleImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self!.selectMedia(.sample)
        }))

        let menu = OverlayableMenuTableViewController(items: menuItems)
        menu.look = LookConf.brightLook
        menu.title = NSLocalizedString("MediaListTableViewController.video.title",
                                       comment: "list sources medias title video")
        return menu
    }
}

#if !CONFIG_SWITCHER_CLOUD
protocol CloudAssetsListViewControllerDelegate {
}
#endif

// MARK: MediaGridViewControllerDelegate

extension VideoProdFlow: MediaGridViewControllerDelegate {
    func mediaGridDidSelect(url: URL) {
        self.prepareVideoEditorForRes(url: url)
    }
}
