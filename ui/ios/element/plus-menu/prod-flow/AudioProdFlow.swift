//
//  AudioProdFlow.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 30.11.20.
//  Copyright © 2020 Switcher Inc. All rights reserved.
//

import UIKit
import InternalBridging
import MobileCoreServices
import Photos

/**
 ** AudioProdFlow is the class that manage production flow for videos
 ** coming from Photo Library, Switcher Studio App, Samples, Switcher Cloud
 **/
class AudioProdFlow: MediaProdFlow {

    var audioBuilder: AudioAssetBuilder? {
        get {
            if let builder = assetBuilder {
                return (builder as! AudioAssetBuilder)
            }
            return nil
        }
        set { assetBuilder = newValue }
    }

    private var isSelectingAudioFromVideo: Bool = false

    // Default tag for an audio file
    // use mainly for samples to autofill tag cell
    private var defaultTag: String = ""

    init(insertionRank: MultilevelRank?) {
        super.init(targetAspectRatio: .invalid, insertionRank: insertionRank, mainMixer: nil)
    }

    // MARK: - ****** OVERRIDE PARENT ******

    // MARK: ViewController
    override func createFirstViewController() -> UIViewController? {
        return createMenu()
    }

    override func terminate() {
        guard let builder = self.assetBuilder else {
            FatalLogger.shared.logFatalError("AudioProdFlow::terminate AssetBuilder is nil, can't save source")
        }
        builder.saveSource(insertionRank: self.insertionRank!)

        super.terminate()
    }

    // MARK: - Menu Audio list choices

    private func createMenu() -> OverlayableMenuTableViewController {

        var menuItems = [ExtMenuItem]()

        // IMPORTED AUDIO
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.switcherStudioImportedAudioTitle,
                                           image: MediaListIcons.switcherStudioImportedAudioImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
                                                self!.isSelectingAudioFromVideo = false
                                                self!.selectMedia(.switcherStudioAudioImported)
                                            }))

        // AUDIO FROM VIDEO
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.audioFromVideoTitle,
                                           image: MediaListIcons.audioFromVideoImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
                                                self!.isSelectingAudioFromVideo = false
                                                self!.selectMedia(.audioFromVideo)
                                            }))

        // AUDIO FROM MY RECORDINGS
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.myRecordingsTitle,
                                           image: MediaListIcons.myRecordingsImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self!.selectMedia(.switcherStudioAudioMyRecording)
        }))

        // IOS PICKER - File / USB drive
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.iospickerTitle,
                                           image: MediaListIcons.iospickerImage,
                                           options: [.disclosureIndicator],
                                           action: { [weak self] in
                                                self!.isSelectingAudioFromVideo = false
                                                self!.selectMedia(.iosPicker)
                                            }))

        // CLOUD

        // Add Cloud choice if user is allowed to access it
        #if CONFIG_SWITCHER_PRO
        let userInfo = SSNUserAccount.shared.userInfo!
        if userInfo.isCloudAssetAllowed {
            menuItems.append(ExtMenuActiveItem(title: MediaListIcons.cloudTitle,
                                               image: MediaListIcons.cloudImage,
                                               options: [.disclosureIndicator, .staySelected],
                                               action: { [weak self] in
                self!.isSelectingAudioFromVideo = false
                self!.selectMedia(.cloudSwitcher)
            }))
        }
        #endif

        // SAMPLES
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.sampleTitle,
                                           image: MediaListIcons.sampleImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
                                                self!.isSelectingAudioFromVideo = false
                                                self!.selectMedia(.sample)
                                            }))

        let menu = OverlayableMenuTableViewController(items: menuItems)
        menu.look = LookConf.brightLook
        menu.title = NSLocalizedString("MediaListTableViewController.audio.title",
                                       comment: "list sources medias title audio")
        return menu
    }

    private func selectMedia(_ type: MediaSourceType) {
        self.defaultTag = ""

        switch type {
        case .audioFromVideo:
            let vc = self.createMenuAudioFromVideo()
            self.next(viewController: vc)
        case .photoLibrary:
            let grid: AssetGridViewController = AssetGridViewController(mediaType: .video)
            grid.delegate = self
            self.next(viewController: grid)
        case .switcherStudioAudioImported:
            let localPhotoList = LocalMediaTableViewController(mediaType: .audio)
            localPhotoList.delegate = self
            self.next(viewController: localPhotoList)
        case .switcherStudioVideoImported:
            let localPhotoList = LocalMediaTableViewController(mediaType: .video)
            localPhotoList.delegate = self
            self.next(viewController: localPhotoList)
        case .switcherStudioAudioMyRecording:
            let localPhotoList = LocalMediaTableViewController(mediaType: .audio, onlyRecordedVideos: true)
            localPhotoList.delegate = self
            self.next(viewController: localPhotoList)
        case .switcherStudioVideoMyRecording:
            let localPhotoList = LocalMediaTableViewController(mediaType: .video, onlyRecordedVideos: true)
            localPhotoList.delegate = self
            self.next(viewController: localPhotoList)
        case .cloudSwitcher:
            #if CONFIG_SWITCHER_CLOUD
            let cloudVC = CloudAssetsListViewController(assetType: isSelectingAudioFromVideo ? .video : .audio,
                                                        targetAspectRatio: targetAspectRatio)
            cloudVC.delegate = self
            self.next(viewController: cloudVC)
            #endif
        case .iosPicker:
            var allowedTypesList: [UTType] = [UTType.audio]
            if isSelectingAudioFromVideo {
                allowedTypesList = [UTType.quickTimeMovie, UTType.mpeg4Movie]
            }
            let documentPicker = UIDocumentPickerViewController(forOpeningContentTypes: allowedTypesList, asCopy: true)
            documentPicker.allowsMultipleSelection = false
            documentPicker.delegate = self
            documentPicker.modalPresentationStyle = .pageSheet
            self.presentOver(viewController: documentPicker)
        case .sample:
            let vc = AudioSamplesTableViewController()
            vc.delegate = self
            self.next(viewController: vc)
        default:
            print("AudioProdFlow::mediaListDidSelectPhotoType delegate \(type) not implemented")
        }
    }

    // MARK: - AudioBuilder

    func prepareAudioEditor(srcUrl: URL) {
        assert(self.currentViewController is any Overlayable, "currentViewController is Overlayable")
        // create and start audio builder with url
        guard let overlayable = self.currentViewController as? any Overlayable else {
            FatalLogger.shared.logFatalError("should have a controller overlayable")
        }
        audioBuilder = LocalAudioAssetBuilder(srcUrl: srcUrl, overlayable: overlayable)
        audioBuilder!.delegate = self
        audioBuilder!.start()
    }

    func prepareAudioEditorForBundleAudio(filename: String, mid: String) {
        assert(self.currentViewController is any Overlayable, "currentViewController is Overlayable")
        audioBuilder = BundleAudioAssetBuilder(fileName: filename, mid: mid,
                                               overlayable: self.currentViewController as! any Overlayable)
        audioBuilder!.delegate = self
        audioBuilder!.start()
    }

    func prepareAudioEditorForPHAsset(asset: PHAsset) {
        assert(self.currentViewController is any Overlayable, "currentViewController is Overlayable")
        // create and start audio builder asset
        audioBuilder = PHLibAudioAssetBuilder(assetPH: asset, overlayable: self.currentViewController as! any Overlayable)
        audioBuilder!.delegate = self
        audioBuilder!.start()
    }

    #if CONFIG_SWITCHER_CLOUD
    func prepareAudioEditorForCloudAsset(cloudAsset: CloudAsset) {
        assert(self.currentViewController is any Overlayable, "currentViewController is Overlayable")
        // create and start audio builder asset
        audioBuilder = SwitcherCloudAudioAssetBuilder(cloudAsset: cloudAsset,
                                                      overlayable: self.currentViewController as! any Overlayable)
        audioBuilder!.delegate = self
        audioBuilder!.start()
    }
    #endif

    private func pushAudioEditor() {
        guard audioBuilder != nil else {
            FatalLogger.shared.logFatalError("AudioProdFlow::pushAudioEditor error, no audioBuilder")
        }
        if let playedSource = audioBuilder!.playedSource {
            // create audio editor
            let vc = AudioEditorViewController(mixer: mainMixer,
                                               playedSource: playedSource,
                                               defaultTag: self.defaultTag)
            vc.delegate = self
            vc.hasDoneButton = true
            vc.look = LookConf.brightLook

            self.next(viewController: vc)

        } else {
            FatalLogger.shared.logFatalError("AudioProdFlow::pushVideoEditor error, no played source")
        }

    }

    // MARK: - Audio from video

    private func createMenuAudioFromVideo() -> OverlayableMenuTableViewController {

        isSelectingAudioFromVideo = true

        var menuItems = [ExtMenuItem]()

        // MEDIAS FROM PHOTOLIB
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.photoLibraryTitle,
                                           image: MediaListIcons.photoLibraryImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self!.selectMedia(.photoLibrary)
        }))

        // SWITCHER STUDIO IMPORTED VIDEOS
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.switcherStudioImportedVideoTitle,
                                           image: MediaListIcons.switcherStudioImportedVideoImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self!.selectMedia(.switcherStudioVideoImported)
        }))

        // MY RECORDINGS
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.myRecordingsTitle,
                                           image: MediaListIcons.myRecordingsImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self!.selectMedia(.switcherStudioVideoMyRecording)
        }))

        // IOS PICKER
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.iospickerTitle,
                                           image: MediaListIcons.iospickerImage,
                                           options: [.disclosureIndicator],
                                           action: { [weak self] in
            self!.selectMedia(.iosPicker)
        }))

        // CLOUD

        // Add Cloud choice if user is allowed to access it
        #if CONFIG_SWITCHER_PRO
        let userInfo = SSNUserAccount.shared.userInfo!
        if userInfo.isCloudAssetAllowed {
            menuItems.append(ExtMenuActiveItem(title: MediaListIcons.cloudTitle,
                                               image: MediaListIcons.cloudImage,
                                               options: [.disclosureIndicator, .staySelected],
                                               action: { [weak self] in
                self!.selectMedia(.cloudSwitcher)
            }))
        }
        #endif

        let menu = OverlayableMenuTableViewController(items: menuItems)
        menu.look = LookConf.brightLook
        menu.title = NSLocalizedString("MediaListTableViewController.choice.audio-from-video",
                                       comment: "title audio from video")
        return menu
    }

}

// MARK: - ****** DELEGATES ******

// MARK: - UIDocumentPickerDelegate
extension AudioProdFlow: UIDocumentPickerDelegate {

    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        guard urls.count > 0 else {
            return
        }
        print("AudioProdFlow::didPickDocumentsAt \(urls[0])")

        /*
         * Moving files into the Media Library could take time if these files
         * are on the same logical disk.
         * However, it seems that the UIDocumentPickerViewController takes in
         * charge the copy and makes the files available in the app inbox.
         * So, here, we just have to move it in the Media library, which is
         * super fast.
         */

        // Before opening it we need to move the file into the Media Library
        guard let url = MediaLibrary.shared.importFile(url: urls[0]) else {
            return
        }

        // Open video editor
        self.prepareAudioEditor(srcUrl: url)
    }

    func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
        // print("documentPicker:: documentPickerWasCancelled ")
    }
}

// MARK: - AssetGridDelegate
extension AudioProdFlow: AssetGridDelegate {
    func assetGridDidSelectAsset(_ asset: PHAsset) {
        if asset.mediaType == PHAssetMediaType.video {
            self.prepareAudioEditorForPHAsset(asset: asset)
        } else {
            print("AudioProdFlow::assetGridDidSelectAsset asset selected is not a video but \(asset.mediaType) ")
        }
    }

    func assetGridDisplayAlbumList() {
        let albumListVC = AlbumListTableViewController(mediaType: .video)
        albumListVC.delegate = self
        self.next(viewController: albumListVC)
    }
}

// MARK: - AlbumListTableViewDelegate
extension AudioProdFlow: AlbumListTableViewDelegate {
    func albumListDidSelectAlbum(_ album: AlbumModel) {
        let grid: AssetGridViewController = AssetGridViewController(mediaType: .video, album: album)
        grid.delegate = self
        self.next(viewController: grid)
    }
}

// MARK: - AssetBuilderDelegate
extension AudioProdFlow: AssetBuilderDelegate {
    func assetBuilderDidSucceed(_: GenericAssetBuilder) {
        if self.audioBuilder != nil {
            print("AudioProdFlow::assetBuilderDidSucceed push Video Editor ")
            self.pushAudioEditor()
        } else {
            print("AudioProdFlow::assetBuilderDidSucceed no builder available")
        }
    }

    func assetBuilderDidAbortByUser(_: GenericAssetBuilder) {
        if let builder = self.audioBuilder {
            builder.dispose()
            self.audioBuilder = nil
        }
    }
}

// MARK: - AudioEditorViewControllerDelegate
extension AudioProdFlow: AudioEditorViewControllerDelegate {
    func audioEditorDidEndEditing(_ controller: AudioEditorViewController, success: Bool) {

        assert(success)

        // update builder played source with the one updated in the view controller
        // with tag and volume
        if let builder = self.audioBuilder {
            builder.playedSource = controller.playedSource
        }
        self.terminate()
    }

}

// MARK: - AudioSamplesTableViewControllerDelegate
extension AudioProdFlow: AudioSamplesTableViewControllerDelegate {

    func audioSamplesDidSelect(_ controller: AudioSamplesTableViewController, audioModel: AudioFileModel) {
        self.defaultTag = audioModel.title

        self.prepareAudioEditorForBundleAudio(filename: audioModel.filename, mid: audioModel.mid)
    }

}

// MARK: - LocalMediaTableViewDelegate
extension AudioProdFlow: LocalMediaTableViewDelegate {

    func localMediaTableView(_ tableView: LocalMediaTableViewController, didSelectAsset: URL) {
        print("AudioProdFlow::localMediaTableViewDelegate selected item \(didSelectAsset)")
        self.prepareAudioEditor(srcUrl: didSelectAsset)
    }
}

// MARK: - CloudAssetsListViewControllerDelegate
extension AudioProdFlow: CloudAssetsListViewControllerDelegate {

    #if CONFIG_SWITCHER_CLOUD

    func cloudAssetsListDidFinish() {
        self.terminate()
    }

    func cloudAssetsDidSelect(asset: CloudAsset) {
        print("AudioProdFlow::cloudAssetsDidSelectDelegate selected item \(asset.url), MID \(asset.id)")

        assert(self.currentViewController is any Overlayable, "currentViewController is Overlayable")
        self.prepareAudioEditorForCloudAsset(cloudAsset: asset)
    }

    #endif
}
