//
//  ShoppingProdFlow.swift
//  Cap-iOS
//
//  Created by marjo<PERSON> <PERSON><PERSON><PERSON> on 02.09.21.
//  Copyright © 2021 Switcher Inc. All rights reserved.
//

import UIKit

class ShoppingProdFlow: BaseProdFlow {

    // for mmart (if mmfx, artwork is nil)
    private var artwork: Artwork?

    private var type: ShoppingCardType = .none

    // MARK: - Init

    init(mainMixer: MainMixer, targetAspectRatio: GmuRatioI32, insertionRank: MultilevelRank?) {
        super.init(targetAspectRatio: targetAspectRatio, insertionRank: insertionRank, mainMixer: mainMixer)
    }

    override func createFirstViewController() -> UIViewController? {
        let menu = createMenu()
        return menu
    }

    // Create sub menu to display camera as background, image as background, etc...
    private func createMenu() -> OverlayableMenuTableViewController {

        var menuItems = [ExtMenuItem]()

        // CAMERA AS BACKGROUND
        menuItems.append(ExtMenuActiveItem(title: ShoppingListIcons.bgCameraTitle,
                                           image: ShoppingListIcons.bgCameraImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
                self!.reportScreenVisit("Plus Menu Card With Camera As Background")
                self!.selectShoppingCardType(.cameraAsBackground)
        }))

        // IMAGE AS BACKGROUND
        menuItems.append(ExtMenuActiveItem(title: ShoppingListIcons.bgImageTitle,
                                           image: ShoppingListIcons.bgImageImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
                self!.reportScreenVisit("Plus Menu Card With Image As Background")
                self!.selectShoppingCardType(.imageAsBackground)
        }))

        // MULTIVIEW WITH TEXT
        menuItems.append(ExtMenuActiveItem(title: ShoppingListIcons.multiviewTextTitle,
                                           image: ShoppingListIcons.multiviewTextImage,
                                           options: [.disclosureIndicator],
                                           action: { [weak self] in
                self!.reportScreenVisit("Plus Menu Card Multiview")
                self!.selectShoppingCardType(.multiviewWithText)
        }))

        // LIVE SELLING SAMPLE
        menuItems.append(ExtMenuActiveItem(title: ShoppingListIcons.liveSellingSamplesTitle,
                                           image: ShoppingListIcons.liveSellingSamplesImage,
                                           options: [.disclosureIndicator],
                                           action: { [weak self] in
                self!.reportScreenVisit("Plus Menu Card Live Selling")
                self!.selectShoppingCardType(.liveSellingSamples)
        }))

        let menu = OverlayableMenuTableViewController(items: menuItems)
        menu.look = LookConf.brightLook
        menu.title = NSLocalizedString("ShoppingList.submenu.title", comment: "")
        return menu
    }

    func selectShoppingCardType(_ type: ShoppingCardType) {
        self.type = type

        let collection = ShoppingCollectionViewController(type: type, targetAspectRatio: self.targetAspectRatio)
        collection.delegate = self
        self.next(viewController: collection)
    }

    private func reportScreenVisit(_ name: String) {
        let props = [
            "targetAspectRatio": TargetAspectRatioUtil.ratioToString(targetAspectRatio)
        ]
        Analytics.shared.reportScreenVisit(name: name, properties: props)
    }
}

@objc public enum ShoppingCardType: Int {
    case cameraAsBackground
    case imageAsBackground
    case multiviewWithText
    case liveSellingSamples
    case dev
    case demo
    case none

    func toString() -> String {
        switch self {
        case .cameraAsBackground:
            return "card/camera"
        case .imageAsBackground:
            return "card/image"
        case .multiviewWithText:
            return "card/multiview"
        case .liveSellingSamples:
            return "card/sample"
        case .dev:
            return "card/dev"
        case .demo:
            return "card/demo"
        case .none:
            return "card/none"
        }
    }
}

extension ShoppingProdFlow: ArtworkCollectionDelegate {

    func artworkCollectionDidSelectEffect(_ effect: Effect) {
        let ratios = AspectRatioCompatibilitySet()
        ratios.insert(self.targetAspectRatio)
        effect.supportedAspectRatios = ratios
        effect.applyOnProgram = true
        effect.section = nil
        effect.preferredXmlFileName = "Multiview.mmfx"

        /*
         * Today, templates should contain properties explicitely set to their preferred values
        let vPropHolder = effect.vPropHolder!
        vPropHolder.beginChange()
        vPropHolder.setAllPropsToPreferred()
        vPropHolder.endChange()
         */

        eventuallyApplyBrandProfile(to: effect, isCard: true)

        let editor = MultiviewEditorViewController(mainMixer: mainMixer!,
                                                   targetAspectRatio: targetAspectRatio,
                                                   multiview: effect)
        editor.delegate = self
        editor.hasDoneButton = true
        editor.look = LookConf.brightLook

        self.next(viewController: editor)
    }

    func artworkCollectionDidSelectArtwork(_ artwork: Artwork) {

        artwork.section = nil
        artwork.preferredXmlFileName = "Card.mmart"
        self.artwork = artwork

        eventuallyApplyBrandProfile(to: artwork, isCard: true)

        // Push artwork editor
        let vc = ArtworkEditorViewController(artwork: artwork,
                                             mainMixer: mainMixer,
                                             targetAspectRatio: targetAspectRatio)
        vc.look = LookConf.brightLook
        vc.hasDoneButton = true
        vc.delegate = self
        vc.isApplyOnPreviewOptionHidden = isApplyOnPreviewOptionHidden
        self.next(viewController: vc)
    }

    func artworkCollectionDidSelectPlayedSource(_ playedSource: PlayedSource) {
        playedSource.section = nil
        playedSource.preferredXmlFileName = "Timer.mmsrc"

        eventuallyApplyBrandProfile(to: playedSource, isCard: true)

        let vc = PlayedVideoEditorViewController(playedSource: playedSource,
                                                 mainMixer: mainMixer,
                                                 targetAspectRatio: targetAspectRatio)
        vc.look = LookConf.brightLook
        vc.delegate = self
        vc.isApplyOnPreviewOptionHidden = isApplyOnPreviewOptionHidden
        vc.hasDoneButton = true
        self.next(viewController: vc)
    }
}

extension ShoppingProdFlow: VMakerEditorViewControllerDelegate {
    func vmakerEditorDidAppear(_ editor: VMakerEditorViewController) {
        // Do nothing
    }

    func vmakerEditorDidDisappear(_ editor: VMakerEditorViewController) {
        // Do nothing
    }

    // called when the user press <Cancel> or <Done>
    // quit ArtworkEditorViewController
    func vmakerEditorDidEndEditing(_ editor: VMakerEditorViewController, success: Bool) {

        // there is no cancel button
        assert(success)

        let props: [String: Any] = [
            "target-ratio": TargetAspectRatioUtil.ratioToString(targetAspectRatio),
            "from": type.toString()
        ]

        if let ed = editor as? MultiviewEditorViewController {

            let mview = ed.multiview
            mview.xmlFileUrl = nil
            mview.mid = nil
            mview.rank = self.insertionRank!
            mview.thumbnail = ed.generateThumbnail()
            mview.save() // TODO: save in a background queue

            BaseProdFlow.reportEvent(coreElement: mview, properties: props)

        } else if let ed = editor as? PlayedVideoEditorViewController {

            let playedSource = ed.playedSource
            playedSource.rank = self.insertionRank!
            playedSource.xmlFileUrl = nil
            playedSource.mid = nil
            playedSource.thumbnail = ed.generateThumbnail()
            playedSource.save() // TODO: save in a background queue

            BaseProdFlow.reportEvent(coreElement: playedSource, properties: props)

        } else if let art = self.artwork {
            let thumbnail: ArtworkThumbnail? = editor.generateThumbnail()

            art.thumbnail = thumbnail

            art.rank = self.insertionRank!

            #if SAVE_AS_EMBEDDED_ARTWORK
            guard let xmlFilePath = art.xmlFilePath
                else { print("ShoppingProdFlow::terminate error no xmlFilePath for artwork");return;}
            let name = URL(fileURLWithPath: xmlFilePath).lastPathComponent
            let path = "\(MediaList.shared.mediaDirPath)/\(name)"
            let destUrl = URL(fileURLWithPath: path)
            art.xmlFileUrl = destUrl
            #else
            art.xmlFileUrl = nil
            #endif
            art.mid = nil

            // TODO: move to creation
            let ass = AspectRatioCompatibilitySet()
            ass.insert(self.targetAspectRatio)
            art.supportedAspectRatios = ass

            art.save()

            BaseProdFlow.reportEvent(coreElement: art, properties: props)

        } else {
            print("ShoppingProdFlow::terminate artwork is nil")
        }

        self.terminate()
    }
}

class ShoppingListIcons: NSObject {

    public static let iconWidth: CGFloat = 20

    public static let bgCameraTitle = NSLocalizedString("ShoppingList.choice.camera-as-background",
                                                        comment: "choice camera as back")
    public static let bgCameraImage = ExtMenuTableViewController.formattedIcon(named: "card--camera-as-back",
                                                                               width: iconWidth)

    public static let bgImageTitle = NSLocalizedString("ShoppingList.choice.image-as-background",
                                                       comment: "choice image as background")
    public static let bgImageImage = ExtMenuTableViewController.formattedIcon(named: "card--image-as-back",
                                                                              width: iconWidth)

    public static let multiviewTextTitle = NSLocalizedString("ShoppingList.choice.multiview",
                                                             comment: "choice multiview text")
    public static let multiviewTextImage = ExtMenuTableViewController.formattedIcon(named: "card--multiview",
                                                                                    width: iconWidth)

    public static let liveSellingSamplesTitle = NSLocalizedString("ShoppingList.choice.live-selling-samples",
                                                                  comment: "choice live selling samples")
    public static let liveSellingSamplesImage = ExtMenuTableViewController
        .formattedIcon(named: "assetprod-virtualselling-icon",
                       width: iconWidth)

}
