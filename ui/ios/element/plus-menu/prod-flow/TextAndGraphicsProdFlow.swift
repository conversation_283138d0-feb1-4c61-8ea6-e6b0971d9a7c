//
//  TextAndGraphicsProdFlow.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 15.07.19.
//  Copyright © 2019 RecoLive Sàrl. All rights reserved.
//

import UIKit
import InternalBridging
import MobileCoreServices
import Photos
import SwiftUI

public enum TextAndGraphicsType {
    case none
    case title
    case lowerThird
    case animatedText
    case timers
    case platformOverlay
    case broadcastNotification
    case cornerBug
    case slideshow
    case imageOverlay
    case imageOverlayNEW
    case imageLowerThird
}

class TextAndGraphicsProdFlow: BaseProdFlow, ArtworkListDelegate, VMakerEditorViewControllerDelegate, AssetGridDelegate,
    LocalMediaTableViewDelegate, MediaGridViewControllerDelegate, CloudAssetsListViewControllerDelegate,
    AssetBuilderDelegate, AlbumListTableViewDelegate, UIDocumentPickerDelegate {

    private var artwork: Artwork?

    // photo builder for image overlay and corner bug / logo
    private var photoBuilder: PhotoAssetBuilder?

    // current type of text and graphics selected : title, lower third, corner bug / logo, etc
    private var currentType: TextAndGraphicsType = .none

    // shortcut: text and graphics type that you want access directly
    public var shortcut: TextAndGraphicsType = .none

    // MARK: - Init

    init(mainMixer: MainMixer, targetAspectRatio: GmuRatioI32, insertionRank: MultilevelRank?) {
        super.init(targetAspectRatio: targetAspectRatio, insertionRank: insertionRank, mainMixer: mainMixer)
    }

    deinit {
        // print("TextAndGraphicsProdFlow::deinit")
    }

    // MARK: - ViewController

    override func createFirstViewController() -> UIViewController? {
        return self.createViewControllerForTextAndGraphicType(shortcut)
    }

    override func terminate() {

        if currentType == .imageOverlay  || currentType == .imageLowerThird {
            // Save artwork for image overlay and corner bug
            if let builder = self.photoBuilder {
                builder.saveSource(insertionRank: self.insertionRank!)
                builder.finish()
                self.photoBuilder = nil
            } else {
                FatalLogger.shared.logFatalError("photobuilder is nil, can't save source")
            }
        } else {
            // Save artwork for titles, lowerthirds, socialOverlays, donationOverlays, broadcast notifications
            if let art = self.artwork, let vPropHolder = art.vPropHolder, let vMaker = vPropHolder.vMaker,
               vMaker is VMakerShapeComposition {
                art.rank = self.insertionRank!

                #if SAVE_AS_EMBEDDED_ARTWORK
                guard let xmlFilePath = art.xmlFilePath
                    else { print("TextAndGraphicsWorkflow::terminate error no xmlFilePath for artwork");return;}
                let name = URL(fileURLWithPath: xmlFilePath).lastPathComponent
                let path = "\(MediaList.shared.mediaDirPath)/\(name)"
                let destUrl = URL(fileURLWithPath: path)
                art.xmlFileUrl = destUrl
                #else
                art.xmlFileUrl = nil
                #endif
                art.mid = nil

                // TODO: move to creation
                let ass = AspectRatioCompatibilitySet()
                ass.insert(self.targetAspectRatio)
                art.supportedAspectRatios = ass

                art.save()

                BaseProdFlow.reportEvent(coreElement: art)
            }
        }

        super.terminate()
    }

    // MARK: - ****** ARTWORK ******

    public func pushArtworkEditor(artwork: Artwork) {
        self.artwork = artwork
        eventuallyApplyBrandProfile(to: artwork)

        let vc = ArtworkEditorViewController(artwork: artwork, mainMixer: mainMixer,
                                             targetAspectRatio: targetAspectRatio)
        vc.look = LookConf.brightLook
        vc.hasDoneButton = true
        vc.delegate = self
        vc.isApplyOnPreviewOptionHidden = isApplyOnPreviewOptionHidden

        self.next(viewController: vc)
    }

    // MARK: - ***** Delegates *****

    // MARK: ArtworkListDelegate

    func artworkListDidSelect(_ artwork: Artwork) {
        self.pushArtworkEditor(artwork: artwork)
    }

    // MARK: VMakerEditorViewControllerDelegate

    func vmakerEditorDidAppear(_ editor: VMakerEditorViewController) {
        // Do nothing for text and graphics
    }

    func vmakerEditorDidDisappear(_ editor: VMakerEditorViewController) {
        // in case if we go back from the editor to choice list
        // the builder finish the job by destroying image, artwork and all elements related
        // but the prod flow is still there, and we don't stop it
        if let builder = self.photoBuilder {
            builder.finish()
        }
    }

    // called when the user press <Cancel> or <Done>
    // quit ArtworkEditorViewController
    func vmakerEditorDidEndEditing(_ editor: VMakerEditorViewController, success: Bool) {

        // there is no cancel button
        assert(success)

        if let art = self.artwork {
            #if SAVE_AS_EMBEDDED_ARTWORK
            /* marti
             var props = ThumbnailGenerationProperties()
             props.final_width = 960
             props.final_height = 180
             props.crop_top = 333
             props.crop_bottom = 27
             props.in_not_out = true
             props.level = 1
             */
            /* emma
             var props = ThumbnailGenerationProperties()
             props.final_width = 960
             props.final_height = 180
             props.crop_top = 356
             props.crop_bottom = 4
             props.in_not_out = true
             props.level = 1
             */
            /* atlanta
             var props = ThumbnailGenerationProperties()
             props.final_width = 960
             props.final_height = 180
             props.crop_top = 330
             props.crop_bottom = 30
             props.in_not_out = true
             props.level = 1
             */
            /* gomez
             var props = ThumbnailGenerationProperties()
             props.final_width = 960
             props.final_height = 180
             props.crop_top = 325
             props.crop_bottom = 35
             props.in_not_out = true
             props.level = 1
             */
            /* petrova
             var props = ThumbnailGenerationProperties()
             props.final_width = 960
             props.final_height = 180
             props.crop_top = 360
             props.crop_bottom = 0
             props.in_not_out = true
             props.level = 1
             */
            /* social overlays
             var props = ThumbnailGenerationProperties()
             props.final_width = 960
             props.final_height = 172
             props.crop_top = 360
             props.crop_bottom = 8
             props.in_not_out = true
             props.level = 1
             */
            /* donation overlays
             var props = ThumbnailGenerationProperties()
             props.final_width = 960
             props.final_height = 172
             props.crop_top = 360
             props.crop_bottom = 8
             props.in_not_out = true
             props.level = 1
             */
            /*
             var props = ThumbnailGenerationProperties()
             props.final_width = 960
             props.final_height = 180
             props.crop_top = 360
             props.in_not_out = true
             props.level = 1
             */
            /* full size thumbnail */
            var props = ThumbnailGenerationProperties()
            props.final_width = 960
            props.final_height = 540
            props.in_not_out = true
            props.level = 1

            let thumbnail: ArtworkThumbnail? = editor.generateThumbnail(properties: props)
            #else
            let thumbnail: ArtworkThumbnail? = editor.generateThumbnail()
            #endif

            art.thumbnail = thumbnail
        } else {
            print("TextAndGraphicsProdFlow::vmakerEditorDidEndEditing artwork is nil")
        }

        self.terminate()
    }

    // MARK: - Images Delegates

    // MARK: Select Media

    func selectMedia(_ type: MediaSourceType) {
        switch type {
        case .photoLibrary:
            let grid: AssetGridViewController = AssetGridViewController(mediaType: .photo)
            grid.delegate = self
            self.next(viewController: grid)

        case .switcherStudioPhotoImported:
            let localPhotoList = LocalMediaTableViewController(mediaType: .photo)
            localPhotoList.delegate = self
            self.next(viewController: localPhotoList)

        case .switcherStudioPhotoMyRecording:
            let localPhotoList = LocalMediaTableViewController(mediaType: .photo)
            localPhotoList.delegate = self
            self.next(viewController: localPhotoList)

        case .sample:
            let mediaGridViewModel = MediaGridViewModel()
            let samplePattern = MediaGridViewModel.MediaItem(
                url: "res:test-image",
                name: NSLocalizedString("SamplesCollectionViewController.choice.pattern",
                                        comment: "pattern sample")
            )
            let sampleSlide = MediaGridViewModel.MediaItem(
                url: "res:sample-slide",
                name: NSLocalizedString("SamplesCollectionViewController.choice.slide",
                                        comment: "slide sample")
            )
            let sampleCornerBug = MediaGridViewModel.MediaItem(
                url: "res:app-icon",
                name: NSLocalizedString("SamplesCollectionViewController.choice.cornerbug",
                                        comment: "cornerbug sample")
            )
            let fullscreenImageName = NSLocalizedString("SamplesCollectionViewController.choice.image-fullscreen",
                                                        comment: "image fullscreen sample")
            let sampleImageFullscreen = MediaGridViewModel.MediaItem(
                url: "res:sample-image",
                name: "\(fullscreenImageName) 1")
            let sampleVerticalImageFullscreen = MediaGridViewModel.MediaItem(
                url: "res:sample-image-v",
                name: "\(fullscreenImageName) 2")
            mediaGridViewModel.items = [
                samplePattern,
                sampleSlide,
                sampleCornerBug,
                sampleImageFullscreen,
                sampleVerticalImageFullscreen
            ]
            mediaGridViewModel.delegate = self
            let sampleVC = MediaGridViewController(viewModel: mediaGridViewModel)
            sampleVC.title = MediaListIcons.sampleTitle
            self.next(viewController: sampleVC)

        case .logoBrandProfile:
            let mediaGridModel = MediaGridViewModel()
            mediaGridModel.delegate = self
            let vc = MediaGridViewController(viewModel: mediaGridModel)
            let dataSource = BrandProfileManager.shared.$brandProfile
                .map {
                    $0?.logos.keys.map { MediaGridViewModel.MediaItem(url: MediaUtil.url(mid: $0)) } ?? []
                }
            dataSource.assign(to: &mediaGridModel.$items)
            vc.title = MediaListIcons.brandProfileTitle
            self.next(viewController: vc)

        case .logoPlatform:
            let platform = self.createPlatformSelectionComponent()
            self.next(viewController: platform)

        case .cloudSwitcher:
            // self.showAlertForCloud()
            let cloudVC = CloudAssetsListViewController(assetType: .image, targetAspectRatio: targetAspectRatio)
            cloudVC.delegate = self
            self.next(viewController: cloudVC)

        case .iosPicker:
            let documentPicker = UIDocumentPickerViewController(forOpeningContentTypes: [UTType.png, UTType.jpeg],
                                                                asCopy: true)
            documentPicker.allowsMultipleSelection = false
            documentPicker.delegate = self
            documentPicker.modalPresentationStyle = .pageSheet
            self.presentOver(viewController: documentPicker)

        default:
            print("TextAndGraphicsProdFlow::mediaListDidSelectPhotoType delegate \(type) not implemented")
        }
    }

    // MARK: AssetGridDelegate

    func assetGridDidSelectAsset(_ asset: PHAsset) {
        if asset.mediaType == PHAssetMediaType.image {
            assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
            self.preparePhotoEditorForPHAsset(asset: asset)
        } else {
            let type = asset.mediaType.rawValue
            print("TextAndGraphicsProdFlow::assetGridDidSelectAsset asset selected is not an image but \(type)")
        }
    }

    func assetGridDisplayAlbumList() {
        let albumListVC = AlbumListTableViewController(mediaType: .photo)
        albumListVC.delegate = self
        self.next(viewController: albumListVC)
    }

    // MARK: LocalMediaTableViewDelegate

    func localMediaTableView(_ tableView: LocalMediaTableViewController, didSelectAsset: URL) {
        print("TextAndGraphicsProdFlow::LocalMediaTableViewDelegate selected item \(didSelectAsset)")

        let resolvedUrl = UrlResolver.default.weaklyResolve(didSelectAsset)

        if resolvedUrl != nil {
            let ext = resolvedUrl?.pathExtension.lowercased() ?? "no extension found"
            print("TextAndGraphicsProdFlow::resolvedUrl extension \(ext)")
            assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
            // create and start photo builder with sourceURL
            self.preparePhotoEditor(imageUrl: didSelectAsset)
        } else {
            print("TextAndGraphicsProdFlow::localMediaCollection asset selected is not an image")
        }
    }

    // MARK: MediaGridViewControllerDelegate

    func mediaGridDidSelect(url: URL) {
        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        // create and start photo builder with sourceURL
        self.preparePhotoEditor(imageUrl: url)
    }

    // MARK: CloudAssetsListViewControllerDelegate

    func cloudAssetsListDidFinish() {
        self.terminate()
    }

    func cloudAssetsDidSelect(asset: CloudAsset) {
        print("TextAndGraphicsProdFlow::cloudAssetsDidSelectDelegate selected item \(asset.url), MID \(asset.id)")

        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        self.preparePhotoEditorForCloudAsset(cloudAsset: asset)
    }

    // MARK: - AlbumListTableViewDelegate

    func albumListDidSelectAlbum(_ album: AlbumModel) {
        let grid: AssetGridViewController = AssetGridViewController(mediaType: .photo, album: album)
        grid.delegate = self
        self.next(viewController: grid)
    }

    // MARK: - UIDocumentPickerDelegate

    func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
        guard urls.count > 0 else {
            return
        }
        debugPrint("TextAndGraphicsProdFlow::didPickDocumentsAt  \(urls[0])")
        guard let url = MediaLibrary.shared.importFile(url: urls[0]) else {
            return
        }
        self.preparePhotoEditor(imageUrl: url)
    }

    func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
        // print("documentPicker:: documentPickerWasCancelled ")
    }

    // MARK: - AssetBuilderDelegate

    func assetBuilderDidSucceed(_: GenericAssetBuilder) {

        guard let builder = self.photoBuilder else {
            print("TextAndGraphicsProdFlow::assetBuilderDidSucceed error photoBuilder not available")
            return
        }

        builder.createArtwork()

        guard let art = builder.artwork else {
            print("TextAndGraphicsProdFlow::assetBuilderDidSucceed error photoBuilder.artwork not available")
            return
        }

        self.pushArtworkEditor(artwork: art)

    }

    func assetBuilderDidAbortByUser(_: GenericAssetBuilder) {
        if let builder = self.photoBuilder {
            builder.dispose()
            self.photoBuilder = nil
        }
    }

    // MARK: - PhotoBuilder

    func preparePhotoEditor(imageUrl: URL) {
        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        // create and start photo builder with sourceURL
        if imageUrl.scheme!.lowercased() == "res" {
            guard let overlayable = self.currentViewController as? (any Overlayable) else {
                FatalLogger.shared.logFatalError("should have a controller overlayable")
            }
            photoBuilder = ResPhotoAssetBuilder(url: imageUrl,
                                                photoType: self.getPhotoTypeFromTextAndGraphicsType(currentType),
                                                overlayable: overlayable, targetAspectRatio: targetAspectRatio,
                                                options: [], preloadingContext: nil)
        } else if let mid = MediaUtil.mid(url: imageUrl), EmbeddedAssetStock.shared.asset(mid: mid) != nil {
            photoBuilder = EmbeddedAssetBuilder(mid: mid,
                                                photoType: self.getPhotoTypeFromTextAndGraphicsType(currentType),
                                                overlayable: self.currentViewController as! (any Overlayable),
                                                targetAspectRatio: targetAspectRatio, options: [],
                                                preloadingContext: nil)
        } else {
            guard let overlayable = self.currentViewController as? (any Overlayable) else {
                FatalLogger.shared.logFatalError("should have a controller overlayable")
            }
            photoBuilder = LocalPhotoAssetBuilder(srcUrl: imageUrl,
                                                  photoType: self.getPhotoTypeFromTextAndGraphicsType(currentType),
                                                  overlayable: overlayable, targetAspectRatio: targetAspectRatio,
                                                  options: [], preloadingContext: nil)
        }
        photoBuilder!.delegate = self
        photoBuilder!.start()
    }

    func preparePhotoEditorForPHAsset(asset: PHAsset) {
        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        // create and start photo builder with asset
        photoBuilder = PHLibPhotoAssetBuilder(assetPH: asset,
                                              photoType: self.getPhotoTypeFromTextAndGraphicsType(currentType),
                                              overlayable: self.currentViewController as! (any Overlayable),
                                              targetAspectRatio: targetAspectRatio, options: [], preloadingContext: nil)
        photoBuilder!.delegate = self
        photoBuilder!.start()
    }

    func preparePhotoEditorForCloudAsset(cloudAsset: CloudAsset) {
        assert(self.currentViewController is (any Overlayable), "currentViewController is Overlayable")
        // create and start photo builder with cloudAsset
        photoBuilder = SwitcherCloudPhotoAssetBuilder(cloudAsset: cloudAsset,
                                                      photoType: self.getPhotoTypeFromTextAndGraphicsType(currentType),
                                                      overlayable: self.currentViewController as! (any Overlayable),
                                                      targetAspectRatio: targetAspectRatio, options: [],
                                                      preloadingContext: nil)
        photoBuilder!.delegate = self
        photoBuilder!.start()
    }

    // MARK: - UINavigationControllerDelegate

    override func navigationController(_ navigationController: UINavigationController,
                                       willShow viewController: UIViewController, animated: Bool) {

        if let builder = self.photoBuilder, builder.overlayable === currentViewController {
            /*
             * The user is leaving the panel that displays the loading
             * operations. We are not sure if the user pressed the back button
             * or just started a swipe back gesture.
             * In any case, we need to cancel the builder and dismiss the
             * overlay in order to prevent the builded to end loading during the
             * back transition, causing a new view controller to be pushed.
             */
            builder.dispose()
        }

        super.navigationController(navigationController, willShow: viewController, animated: animated)
    }

    override public func navigationController(_ navigationController: UINavigationController,
                                              didShow viewController: UIViewController, animated: Bool) {

        let count = navigationController.viewControllers.count
        if let builder = self.photoBuilder,
           count >= 2 && navigationController.viewControllers[count - 2] === builder.overlayable {
            /*
             * We just pushed a new view controller on top of the view
             * controller which has the loading overlay. We can now dismiss the
             * overlay in order to be ready if the user navigate back.
             */
            builder.dispose()
        }

        if let builder = self.photoBuilder, viewController === builder.overlayable {
            /*
             * We moved back to the view controller that started the builder.
             * So the builder is no longer useful.
             */
            self.photoBuilder = nil
        }

        super.navigationController(navigationController, didShow: viewController, animated: animated)
    }

    // MARK: - Menu TextAndGraphics list choices

    public func next(type: TextAndGraphicsType) {
        let vc = createViewControllerForTextAndGraphicType(type)
        self.next(viewController: vc)
    }

    private func createMenu() -> OverlayableMenuTableViewController {

        var menuItems = [ExtMenuItem]()

        // TITLES
        menuItems.append(ExtMenuActiveItem(title: TextAndGraphicsListIcons.titleTitle,
                                           image: TextAndGraphicsListIcons.titleImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.next(type: .title)
        }))

        // LOWER THIRD
        menuItems.append(ExtMenuActiveItem(title: TextAndGraphicsListIcons.lowerThirdTitle,
                                           image: TextAndGraphicsListIcons.lowerThirdImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.next(type: .lowerThird)
        }))

        // ANIMATED TEXT
        menuItems.append(ExtMenuActiveItem(title: TextAndGraphicsListIcons.animatedTextTitle,
                                           image: TextAndGraphicsListIcons.animatedTextImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.next(type: .animatedText)
        }))

        // TIMERS
        menuItems.append(ExtMenuActiveItem(title: TextAndGraphicsListIcons.timersTitle,
                                           image: TextAndGraphicsListIcons.timersImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.next(type: .timers)
        }))

        // SOCIAL OVERLAY
        menuItems.append(ExtMenuActiveItem(title: TextAndGraphicsListIcons.platformOverlayTitle,
                                           image: TextAndGraphicsListIcons.platformOverlayImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.next(type: .platformOverlay)
        }))

        // BROADCAST NOTIFICATION
        // Display broadcast notifications only for horizontal mode
        let targetAspectRatio = OutputProfile.shared.targetAspectRatio
        if targetAspectRatio.numer > targetAspectRatio.denom {
            menuItems.append(ExtMenuActiveItem(title: TextAndGraphicsListIcons.broadcastNotificationTitle,
                                               image: TextAndGraphicsListIcons.broadcastNotificationImage,
                                               options: [.disclosureIndicator, .staySelected],
                                               action: { [weak self] in
                self?.next(type: .broadcastNotification)
            }))
        }

        // CORNER BUG
        menuItems.append(ExtMenuActiveItem(title: TextAndGraphicsListIcons.cornerBugTitle,
                                           image: TextAndGraphicsListIcons.cornerBugImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.next(type: .cornerBug)
        }))

        // IMAGE OVERLAY
        menuItems.append(ExtMenuActiveItem(title: TextAndGraphicsListIcons.imageOverlayTitle,
                                           image: TextAndGraphicsListIcons.imageOverlayImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.next(type: .imageOverlay)
        }))

        // IMAGE LOWER THIRD
        menuItems.append(ExtMenuActiveItem(title: TextAndGraphicsListIcons.imageLowerThirdTitle,
                                           image: TextAndGraphicsListIcons.imageLowerThirdImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.next(type: .imageLowerThird)
        }))

        let menu = OverlayableMenuTableViewController(items: menuItems)
        menu.look = LookConf.brightLook
        menu
            .title =
            NSLocalizedString(NSLocalizedString("TextAndGraphicsListTableViewController.title",
                                                comment: "list text and graphics"),
                              comment: "vc title")
        return menu

    }

    // MARK: - Utils

    func getPhotoTypeFromTextAndGraphicsType(_ type: TextAndGraphicsType) -> PhotoAssetType {
        if type == .imageOverlay {
            return .centerLogo
        } else if type == .cornerBug {
            return .cornerlogo
        } else if type == .imageLowerThird {
            return .lowerThird
        } else {
            return .none
        }
    }

    private func createViewControllerForTextAndGraphicType(_ type: TextAndGraphicsType) -> UIViewController {
        self.currentType = type

        if type == .none {
            // Display text and graphics list
            let textAndGraphicsList = createMenu()
            return textAndGraphicsList
        } else {
            let editor: ArtworkListViewController
            switch type {
            case .title:
                let collection = TitleCollectionViewController()
                collection.delegate = self
                return collection
            case .lowerThird:
                let collection = LowerThirdCollectionViewController()
                collection.delegate = self
                return collection
            case .animatedText:
                let collection = AnimatedTextCollectionViewController()
                collection.delegate = self
                return collection
            case .timers:
                let collection = TimersCollectionViewController()
                collection.delegate = self
                return collection
            case .platformOverlay:
                let collection = PlatformOverlayCollectionViewController()
                collection.delegate = self
                return collection
            case .broadcastNotification:
                editor = BroadcastNotificationListViewController()
                editor.delegate = self
                return editor
            case .imageOverlay:
                let menu: OverlayableMenuTableViewController = createMenuPhoto()
                menu.title = TextAndGraphicsListIcons.imageOverlayTitle
                return menu
            case .cornerBug:
                return UIHostingController(rootView: CornerBugCreationView(artwork: nil,
                                                                           frameAspectRatio: targetAspectRatio,
                                                                           mainMixer: mainMixer!, doneAction: {
                    self.terminate()
                }))
            case .slideshow:
                let templateName = targetAspectRatio.isPortrait ? "tpl-v-slideshow.mmart" : "tpl-h-slideshow.mmart"
                let templateUrl = gmu_apl_resource_url(templateName)
                let artwork = Artwork(fromXMLFileUrl: templateUrl!)!
                return UIHostingController(rootView: ArtworkCreationView(artwork: artwork,
                                                                         isEditing: false,
                                                                         frameAspectRatio: targetAspectRatio,
                                                                         mainMixer: mainMixer!,
                                                                         doneAction: {
                    self.terminate()
                }))
            case .imageOverlayNEW:
                return UIHostingController(rootView: ImageOverlaySelectTypeView(prodFlow: self))

            case .imageLowerThird:
                let menu: OverlayableMenuTableViewController = createMenuPhoto()
                menu.title = TextAndGraphicsListIcons.imageLowerThirdTitle
                return menu
            default:
                print("TextAndGraphicsProdFlow::gotoTextAndGraphicType type:\(type) not implemented")
                return UIViewController()
            }
        }
    }

    // MARK: - Menu Photo list choices

    private func createMenuPhoto() -> OverlayableMenuTableViewController {

        var menuItems = [ExtMenuItem]()

        // MEDIAS FROM PHOTOLIB
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.photoLibraryTitle,
                                           image: MediaListIcons.photoLibraryImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.selectMedia(.photoLibrary)
        }))

        // SWITCHER STUDIO IMPORTED PHOTO
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.switcherStudioImportedPhotoTitle,
                                           image: MediaListIcons.switcherStudioImportedPhotoImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.selectMedia(.switcherStudioPhotoImported)
        }))

        // IOS PICKER
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.iospickerTitle,
                                           image: MediaListIcons.iospickerImage,
                                           options: [.disclosureIndicator],
                                           action: { [weak self] in
            self?.selectMedia(.iosPicker)
        }))

        // CLOUD

        // Add Cloud choice if user is allowed to access it
        let userInfo = SSNUserAccount.shared.userInfo!
        if userInfo.isCloudAssetAllowed {
            menuItems.append(ExtMenuActiveItem(title: MediaListIcons.cloudTitle,
                                               image: MediaListIcons.cloudImage,
                                               options: [.disclosureIndicator, .staySelected],
                                               action: { [weak self] in
                self?.selectMedia(.cloudSwitcher)
            }))
        }

        // BRAND PROFILE
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.brandProfileTitle,
                                           image: MediaListIcons.brandProfileImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.selectMedia(.logoBrandProfile)
        }))

        // PLATFORM ICONS
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.platformTitle,
                                           image: MediaListIcons.platformImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.selectMedia(.logoPlatform)
        }))

        // SAMPLE
        menuItems.append(ExtMenuActiveItem(title: MediaListIcons.sampleTitle,
                                           image: MediaListIcons.sampleImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.selectMedia(.sample)
        }))

        let menu = OverlayableMenuTableViewController(items: menuItems)
        menu.look = LookConf.brightLook
        menu.title = NSLocalizedString("MediaListTableViewController.photo.title", comment: "list sources medias photo")
        return menu

    }

    // MARK: - Logos/Icons List

    func createPlatformSelectionComponent() -> UIViewController {
        let socialPickerView = SocialPlatformSelectionView().onSelectedPlatform { url in
            self.preparePhotoEditor(imageUrl: url)
        }
        let hosting = SocialPlatformViewController(rootView: socialPickerView)
        return hosting
    }

}

// MARK: - ArtworkCollectionDelegate

extension TextAndGraphicsProdFlow: ArtworkCollectionDelegate {

    func artworkCollectionDidSelectEffect(_ effect: Effect) {
        // No multiview in Text & Graphics
    }

    func artworkCollectionDidSelectPlayedSource(_ playedSource: PlayedSource) {
        // No played source here
    }

    func artworkCollectionDidSelectArtwork(_ artwork: Artwork) {
        self.pushArtworkEditor(artwork: artwork)
    }
}
