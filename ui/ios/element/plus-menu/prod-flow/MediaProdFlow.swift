//
//  MediaProdFlow.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 18.06.19.
//  Copyright © 2019 RecoLive Sàrl. All rights reserved.
//

import UIKit
import Photos
import InternalBridging

public enum MediaBasicType {
    case unknown
    case photo
    case video
    case audio
}

enum MediaSourceType {
    case photoLibrary
    case audioFromVideo
    case switcherStudioAudioImported
    case switcherStudioPhotoImported
    case switcherStudioVideoImported
    case switcherStudioAudioMyRecording
    case switcherStudioPhotoMyRecording
    case switcherStudioVideoMyRecording
    case sample
    case background
    case gradient
    case logoPlatform
    case logoBrandProfile
    case iosPicker
    case cloudSwitcher
    case frame
    case pattern
    case noImage

    static var defaultMediaSourceTypes: [MediaSourceType] {
        return [
            .photoLibrary,
            .switcherStudioPhotoImported,
            .iosPicker,
            .cloudSwitcher,
            .logoBrandProfile,
            .background,
            .gradient,
            .logoPlatform,
            .sample,
            .noImage
        ]
    }
}

public struct MediaTypeModel {
    let image: ImageResource
    let name: String
    let type: MediaSourceType
}

/**
 ** MediaProdFlow is the parent class for VideoProdFlow and PhotoProdFlow
 ** the flow is almost the same
 ** (1). Show a list of medias sources :
 **     - Photo Library
 **     - Switcher Studio Application: Imported Media
 **     - Switcher Studio Application: Recordings
 **     - Samples (only for Photo)
 **     - Switcher Cloud
 ** (2). Then show list or grid of photos or videos with same look to select one media
 ** (3). Show editor for Photo or for Video
 **/
@MainActor
class MediaProdFlow: BaseProdFlow {

    var assetBuilder: GenericAssetBuilder?

    // MARK: - Init

    deinit {
        // print("MediaProdFlow::deinit")
        assetBuilder = nil
    }

    // MARK: - ****** OVERRIDE PARENT ******

    override func terminate() {
        self.assetBuilder?.finish()
        self.assetBuilder = nil
        super.terminate()
    }

    // MARK: - UINavigationControllerDelegate

    override func navigationController(
        _ navigationController: UINavigationController,
        willShow viewController: UIViewController,
        animated: Bool
    ) {

        if let builder = self.assetBuilder, builder.overlayable === currentViewController {
            /*
             * The user is leaving the panel that displays the loading
             * operations. We are not sure if the user pressed the back button
             * or just started a swipe back gesture.
             * In any case, we need to cancel the builder and dismiss the
             * overlay in order to prevent the builded to end loading during the
             * back transition, causing a new view controller to be pushed.
             */
            builder.dispose()
        }

        super.navigationController(navigationController, willShow: viewController, animated: animated)
    }

    override public func navigationController(
        _ navigationController: UINavigationController,
        didShow viewController: UIViewController,
        animated: Bool
    ) {

        let c = navigationController.viewControllers.count
        if let builder = self.assetBuilder,
           c >= 2 && navigationController.viewControllers[c - 2] === builder.overlayable {
            /*
             * We just pushed a new view controller on top of the view
             * controller which has the loading overlay. We can now dismiss the
             * overlay in order to be ready if the user navigate back.
             */
            builder.dispose()
        }

        if let builder = self.assetBuilder, viewController === builder.overlayable {
            /*
             * We moved back to the view controller that started the builder.
             * So the builder is no longer useful.
             */
            self.assetBuilder = nil
        }

        super.navigationController(navigationController, didShow: viewController, animated: animated)
    }
}
