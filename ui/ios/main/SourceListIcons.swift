//
//  SourceListIcons.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 03.03.20.
//  Copyright © 2020 Switcher Inc. All rights reserved.
//

import UIKit

class SourceListIcons: NSObject {

    public static let iconWidth: CGFloat = 20

    public static let shareCameraTitle = NSLocalizedString("SourceListIcons.source.camera", comment: "source camera")
    public static let shareCameraImage = ExtMenuTableViewController.formattedIcon(named: "source-share-camera-icon",
                                                                                  width: iconWidth)

    public static let shareHdmiTitle = NSLocalizedString("seemo.addsource.hdmi.title", comment: "seemo camera")
    public static let shareHdmiImage = ExtMenuTableViewController.formattedIcon(named: "source-share-seemo",
                                                                                width: iconWidth)

    public static let shareMobileScreenTitle  = NSLocalizedString("SourceListIcons.source.mobile-screen",
                                                                  comment: "source mobile screen")
    public static let shareMobileScreenImage  = ExtMenuTableV<PERSON>Controller
        .formattedIcon(named: "source-share-mobile-screen-icon",
                       width: iconWidth)

    public static let shareDesktopScreenTitle  = NSLocalizedString("SourceListIcons.source.desktop-screen",
                                                                   comment: "source desktop screen")
    public static let shareDesktopScreenImage  = ExtMenuTableViewController
        .formattedIcon(named: "source-share-desktop-screen-icon",
                       width: iconWidth)

    public static let connectUrlTitle  = NSLocalizedString("SourceListIcons.source.connect-url",
                                                           comment: "source connect url")
    public static let connectUrlImage  = ExtMenuTableViewController.formattedIcon(named: "source-connect-url-icon",
                                                                                  width: iconWidth)

    public static let videochatTitle  = NSLocalizedString("SourceListIcons.source.videochat",
                                                          comment: "source videochat")
    public static let videochatImage  = ExtMenuTableViewController.formattedIcon(named: "source-videochat-icon",
                                                                                 width: iconWidth)

}
