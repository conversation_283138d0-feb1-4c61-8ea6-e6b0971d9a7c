//
//  SourceInformationsTableViewController.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 03.03.20.
//  Copyright © 2020 Switcher Inc. All rights reserved.
//

import UIKit

public enum SourceInformationType {
    case camera
    case mobileScreen
    case desktopScreen
    case videoChat
}

@objc public protocol SourceInformationsTableViewDelegate: AnyObject {
    func sourceDidSelectConnectByURL()
    func sourceDidSelectSwitcherCastMacOSDownloadLink()
    func sourceDidSelectSwitcherCastWinDownloadLink()
}

class SourceInformationsTableViewController: UITableViewController {

    var delegate: (any SourceInformationsTableViewDelegate)?

    private let instructionsCellId = "InstructionsCellId"
    private let connectUrlCellId = "ConnectUrlCellId"
    private let downloadLinkCellId = "DownloadLinkCellId"

    private let sectionInstructions = 0
    private let sectionSecondary = 1

    private let instructionsCameraList =
        [NSLocalizedString("SourceInformationsTableViewController.instructions.camera-1",
                           comment: "camera instruction 1"),
         NSLocalizedString(
             "SourceInformationsTableViewController.instructions.camera-2",
             comment: "camera instruction 2"
         ),
         NSLocalizedString(
             "SourceInformationsTableViewController.instructions.camera-3",
             comment: "camera instruction 3"
         ),
         NSLocalizedString(
             "SourceInformationsTableViewController.instructions.camera-4",
             comment: "camera instruction 4"
         )]

    private let instructionsMobileScreenList =
        [NSLocalizedString("SourceInformationsTableViewController.instructions.mobile-screen-1",
                           comment: "mobile-screen instruction 1"),
         NSLocalizedString(
             "SourceInformationsTableViewController.instructions.mobile-screen-2",
             comment: "mobile-screen instruction 2"
         ),
         NSLocalizedString(
             "SourceInformationsTableViewController.instructions.mobile-screen-3",
             comment: "mobile-screen instruction 3"
         ),
         NSLocalizedString(
             "SourceInformationsTableViewController.instructions.mobile-screen-4",
             comment: "mobile-screen instruction 4"
         )]

    private let instructionsDesktopScreenList =
        [NSLocalizedString("SourceInformationsTableViewController.instructions.desktop-screen-1",
                           comment: "mobile-screen instruction 1"),
         NSLocalizedString(
             "SourceInformationsTableViewController.instructions.desktop-screen-2",
             comment: "mobile-screen instruction 2"
         ),
         NSLocalizedString(
             "SourceInformationsTableViewController.instructions.desktop-screen-3",
             comment: "mobile-screen instruction 3"
         ),
         NSLocalizedString(
             "SourceInformationsTableViewController.instructions.desktop-screen-4",
             comment: "mobile-screen instruction 4"
         )]

    private let linkDesktopScreenList =
        [NSLocalizedString("SourceInformationsTableViewController.download-link.desktop-mac",
                           comment: "desktop-screen link 1"),
         NSLocalizedString(
             "SourceInformationsTableViewController.download-link.desktop-win",
             comment: "desktop-screen link 2"
         )]

    var sourceType: SourceInformationType = .camera

    public init(sourceType: SourceInformationType) {
        self.sourceType = sourceType
        super.init(style: UITableView.Style.grouped)
    }

    override init(style: UITableView.Style) {
        super.init(style: UITableView.Style.grouped)
    }

    required init?(coder aDecoder: NSCoder) {
        FatalLogger.shared.logFatalError("init(coder:) has not been implemented")
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        var sourceTypeTitle = ""
        switch sourceType {
        case .camera:
            sourceTypeTitle = NSLocalizedString(
                "SourceInformationsTableViewController.title.camera",
                comment: "camera title source instructions"
            )
        case .mobileScreen:
            sourceTypeTitle = NSLocalizedString(
                "SourceInformationsTableViewController.title.mobile-screen",
                comment: "mobile-screen title source instructions"
            )
        case .desktopScreen:
            sourceTypeTitle = NSLocalizedString(
                "SourceInformationsTableViewController.title.desktop-screen",
                comment: "desktop-screen title source instructions"
            )
        default:
            sourceTypeTitle = "default"
        }
        self.title = sourceTypeTitle

        self.tableView.isPrefetchingEnabled = false // potentially not needed
        self.tableView.register(UITableViewCell.self, forCellReuseIdentifier: instructionsCellId)
        self.tableView.register(UITableViewCell.self, forCellReuseIdentifier: connectUrlCellId)
        self.tableView.register(UITableViewCell.self, forCellReuseIdentifier: downloadLinkCellId)
    }

    // MARK: - Table view data source

    override func numberOfSections(in tableView: UITableView) -> Int {
        var sections = 2
        if sourceType == .camera || sourceType == .mobileScreen {
            sections += 1
        }
        return sections
    }

    override func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if section == sectionInstructions {
            return instructionsCameraList.count
        } else if section == sectionSecondary && sourceType == .desktopScreen {
            return 2
        } else if section == 2 {
            return 1
        }
        return 1
    }

    override func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {

        let cell = tableView.dequeueReusableCell(withIdentifier: instructionsCellId, for: indexPath)
        cell.textLabel!.numberOfLines = 0
        cell.textLabel!.lineBreakMode = .byWordWrapping

        if indexPath.section == sectionInstructions {
            cell.textLabel!.text = self.getInstructionsForCurrentSourceType(step: indexPath.row)
        } else if indexPath.section == sectionSecondary {
            if sourceType == .camera || sourceType == .mobileScreen {
                cell.textLabel!.text = NSLocalizedString("SourceInformationsTableViewController.connect-url.title",
                                                         comment: "connect by url")
            } else if sourceType == .desktopScreen {
                cell.textLabel!.text = linkDesktopScreenList[indexPath.row]
            }
        } else if indexPath.section == 2 {
                cell.textLabel!.text = NSLocalizedString("SourceInformationsTableViewController.camera-extra",
                                                         comment: "additional text if logged in")
                cell.textLabel?.textAlignment = .left
                cell.textLabel?.font = UIFont.systemFont(ofSize: 15, weight: .regular)
        }
        return cell
    }

    public override func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        if (sourceType == .camera || sourceType == .mobileScreen) && indexPath.section == 1 {
            self.delegate?.sourceDidSelectConnectByURL()
        } else if sourceType == .desktopScreen  && indexPath.section == 1 {
            if indexPath.row == 0 {
                self.delegate?.sourceDidSelectSwitcherCastMacOSDownloadLink()
            } else if indexPath.row == 1 {
                self.delegate?.sourceDidSelectSwitcherCastWinDownloadLink()
            }
        }
        tableView.deselectRow(at: indexPath, animated: true)
    }

    override func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {

        var sectionName: String = ""
        switch section {
        case sectionInstructions:
            sectionName = NSLocalizedString(
                "SourceInformationsTableViewController.instructions.header",
                comment: "instructions section title"
            )
        case sectionSecondary:
            if sourceType == .desktopScreen { sectionName = NSLocalizedString(
                "SourceInformationsTableViewController.download-link.title",
                comment: "download link desktop section title"
            ) } else if sourceType == .camera || sourceType == .mobileScreen {
                sectionName = NSLocalizedString(
                    "SourceInformationsTableViewController.connect-url.header",
                    comment: "connect url section title"
                )
            }
        default:
            sectionName = ""
        }
        return sectionName.uppercased()
    }

    override func tableView(_ tableView: UITableView, shouldHighlightRowAt indexPath: IndexPath) -> Bool {
        if indexPath.section == 0 {
             return false
        }
        return true
    }

    // MARK: - Utils

    private func getInstructionsForCurrentSourceType(step: Int) -> String {

        var instructionLine: String = ""
        if self.sourceType == .camera {
            instructionLine = instructionsCameraList[step]
        } else if self.sourceType == .mobileScreen {
            instructionLine = instructionsMobileScreenList[step]
        } else if self.sourceType == .desktopScreen {
            instructionLine = instructionsDesktopScreenList[step]
        }

        let humanCountStep: Int = step + 1
        return "\(humanCountStep). \(instructionLine)"
    }

}
