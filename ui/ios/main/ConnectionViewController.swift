//
//  ConnectionViewController.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON><PERSON> on 03.03.20.
//  Copyright © 2020 Switcher Inc. All rights reserved.
//

import UIKit
import SwiftUI
import InternalBridging

@objc public protocol ConnectionViewControllerDelegate: AnyObject {
    func connectionDidConnectByURL(requestsConnectionTo url: URL, hostName: String)
}

struct ConnectionView: UIViewControllerRepresentable {

    let delegate: any ConnectionViewControllerDelegate

    func makeUIViewController(context: Context) -> ConnectionViewController {
        let nc = ConnectionViewController()
        nc.delegateConnection = delegate
        return nc
    }

    func updateUIViewController(_ uiViewController: ConnectionViewController, context: Context) {
    }

}

@objc public class ConnectionViewController: ExtendedNavigationController, SourceInformationsTableViewDelegate,
    URLConnectionControllerDelegate {

    // ExtMenuTableViewController -> <PERSON><PERSON> created programmatically
    private var child: UIViewController?

    var delegateConnection: (any ConnectionViewControllerDelegate)?

    public static let kSwitcherCastMacOSDownloadLink: String =
        "https://apps.apple.com/us/app/switcher-cast/id1238109306?mt=12"
    public static let kSwitcherCastWinDownloadLink: String =
        "https://dashboard.switcherstudio.com/download-switchercast"

    // MARK: - Init

    @objc public init() {
        super.init(nibName: nil, bundle: nil)
        child = createSourceTypeListMenu()
        viewControllers = [child!]
        allowAllOrientations()
        isModalInPresentation = true
    }

    required init?(coder aDecoder: NSCoder) {
        FatalLogger.shared.logFatalError("init(coder:) has not been implemented")
    }

    override public var childForStatusBarHidden: UIViewController? {
        return nil
    }

    override public var prefersStatusBarHidden: Bool {
        return true
    }

    override public func viewDidLoad() {
        super.viewDidLoad()

        navigationBar.isHidden = false
    }

    // MARK: - Create Menu Source Type List

    private func createSourceTypeListMenu() -> ExtMenuTableViewController {

        var menuItems = [ExtMenuItem]()

        menuItems.append(ExtMenuActiveItem(title: SourceListIcons.shareCameraTitle,
                                           image: SourceListIcons.shareCameraImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.displayInformations(sourceType: .camera)
        }))

        menuItems.append(ExtMenuActiveItem(title: SourceListIcons.shareHdmiTitle,
                                           image: SourceListIcons.shareHdmiImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.displayHdmiInformations()
        }))

        menuItems.append(ExtMenuActiveItem(title: SourceListIcons.shareMobileScreenTitle,
                                           image: SourceListIcons.shareMobileScreenImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.displayInformations(sourceType: .mobileScreen)
        }))

        menuItems.append(ExtMenuActiveItem(title: SourceListIcons.shareDesktopScreenTitle,
                                           image: SourceListIcons.shareDesktopScreenImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: { [weak self] in
            self?.displayInformations(sourceType: .desktopScreen)
        }))

        /*menuItems.append(ExtMenuActiveItem(title: SourceListIcons.videochatTitle,
                                           image: SourceListIcons.videochatImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action:
            {
               //TODO
        }))*/

        menuItems.append(ExtMenuActiveItem(title: SourceListIcons.connectUrlTitle,
                                           image: SourceListIcons.connectUrlImage,
                                           options: [.disclosureIndicator, .staySelected],
                                           action: {
            self.displayConnectbyUrl()
        }))

        let menu = ExtMenuTableViewController(items: menuItems)
        menu.look = LookConf.brightLook
        menu.title = NSLocalizedString("SourceListIcons.menu.title", comment: "vc title")
        menu.navigationItem
            .leftBarButtonItem =
            UIBarButtonItem(title: NSLocalizedString("global-action.cancel.title", comment: "button"),
                            style: .plain, target: self,
                            action: #selector(self.dismissAction))

        return menu
    }

    @objc private func dismissAction() {
        self.dismiss(animated: true, completion: nil)
    }

    // MARK: - Misc

    private func displayInformations(sourceType: SourceInformationType) {
        let vc = SourceInformationsTableViewController(sourceType: sourceType)
        vc.delegate = self
        vc.navigationItem.rightBarButtonItem = UIBarButtonItem(barButtonSystemItem: .done, target: self,
                                                               action: #selector(self.dismissAction))
        self.pushViewController(vc, animated: true)
    }

    private func displayHdmiInformations() {
        let vc = UIHostingController(rootView: SeemoAddSourceHelperView())
        self.pushViewController(vc, animated: true)
    }

    private func displayConnectbyUrl() {
        let vc = URLConnectionController()
        vc.delegate = self
        self.pushViewController(vc, animated: true)
    }

    private func displayShareUI(forUrl: String) {
        let objectToShare: URL = URL(string: forUrl)!
        let activityViewController = UIActivityViewController(activityItems: [objectToShare],
                                                              applicationActivities: nil)
        /*activityViewController.excludedActivityTypes = [UIActivity.ActivityType.airDrop,
         UIActivity.ActivityType.postToFacebook, UIActivity.ActivityType.postToTwitter, UIActivity.ActivityType.mail]*/

        if let popoverController = activityViewController.popoverPresentationController {
            popoverController.sourceRect = CGRect(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2,
                                                  width: 0, height: 0)
            popoverController.sourceView = self.view
            popoverController.permittedArrowDirections = UIPopoverArrowDirection(rawValue: 0)
        }

        self.present(activityViewController, animated: true, completion: nil)
    }

    public func urlConnectionController(_ controller: URLConnectionController, requestsConnectionTo url: URL,
                                        hostName: String) {
        self.delegateConnection?.connectionDidConnectByURL(requestsConnectionTo: url, hostName: hostName)
        self.dismiss(animated: true, completion: nil)
    }

    // MARK: - SourceInformationsTableViewDelegate

    public func sourceDidSelectConnectByURL() {
        self.displayConnectbyUrl()
    }

    public func sourceDidSelectSwitcherCastMacOSDownloadLink() {
        self.displayShareUI(forUrl: ConnectionViewController.kSwitcherCastMacOSDownloadLink)
    }

    public func sourceDidSelectSwitcherCastWinDownloadLink() {
        self.displayShareUI(forUrl: ConnectionViewController.kSwitcherCastWinDownloadLink)
    }

}
