//
//  AccountMessagingView.swift
//  Cap-iOS
//
//  Created by <PERSON> on 3/13/24.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//

import SwiftUI

class AccountMessagingViewModel: ObservableObject {

    @Published var showCsmHubspotBanner: Bool = false
    @Published var showMessage: Bool = false
    @Published var message: LocalizedStringKey = ""
    @Published var bgColor: Color = .clear
    @Published var txtColor: Color = .clear
    @Published var isDevServer: Bool = false

    let csmUrl: String?
    private let devConf = ConfigurationDevelopmentManager.shared

    init(isLoggedIn: Bool, csmUrl: String?) {
        self.csmUrl = csmUrl
        let userAccount: SSNUserAccount = SSNUserAccount.shared
        let userInfo: SwiUserInfo = userAccount.userInfo
        let expired = !userAccount.accessGranted
        let trialing = userInfo.isStatusTrialing
        let inAppSub = userInfo.shouldPromptWithIAS
        self.showCsmHubspotBanner = isLoggedIn && trialing && userInfo.isBusinessOrAbove
        self.isDevServer = devConf.isDevProfile

        if isLoggedIn && expired {
            self.showMessage = true
            self.message = !inAppSub ? "menu.message.expired_account" : "menu.message.no_active_subscriptions"
            self.bgColor = Color.messageYellow
            self.txtColor = Color.textPrimary
        } else if isLoggedIn && trialing {
            self.showMessage = true
            self.message = "menu.message.on_a_trial"
            self.bgColor = Color.messageBlue
            self.txtColor = Color.textPrimaryLight
        } else {
            self.showMessage = false
        }
    }

    func isSectionValid() -> Bool {
        return (showCsmHubspotBanner && csmUrl != nil) || showMessage || isDevServer
    }
}

struct AccountMessagingViewSection: View {
    @StateObject var viewModel: AccountMessagingViewModel
    @Binding var showCsmHubspotSheet: Bool

    init(isLoggedIn: Bool,
         csmUrl: String?,
         showCsmHubspotSheet: Binding<Bool>) {
        self._viewModel = StateObject(wrappedValue: AccountMessagingViewModel(isLoggedIn: isLoggedIn,
                                                                              csmUrl: csmUrl))
        self._showCsmHubspotSheet = showCsmHubspotSheet
    }

    var body: some View {
        if viewModel.isSectionValid() {
            Section {
                VStack(spacing: 12.0) {
                    if viewModel.showCsmHubspotBanner && viewModel.csmUrl != nil {
                        CsmHubspotBannerView()
                            .onTapGesture {
                                showCsmHubspotSheet = true
                            }
                    }

                    if viewModel.showMessage {
                        AccountBadge(message: viewModel.message,
                                     bgColor: viewModel.bgColor,
                                     txtColor: viewModel.txtColor)
                    }

                    if viewModel.isDevServer {
                        AccountBadge(message: "Dev",
                                     bgColor: .red,
                                     txtColor: .white)
                    }
                }
                .padding(.bottom, (viewModel.showMessage || viewModel.isDevServer) ? 22.0 : 0.0)
            }
            .removeInsetRowSeparator()
            .listSectionSpacing(.compact)
        }
    }
}

private struct AccountBadge: View {
    let message: LocalizedStringKey
    let bgColor: Color
    let txtColor: Color

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 8.0)
                .foregroundColor(self.bgColor)

            Text(message)
                .frame(maxWidth: .infinity, alignment: .center)
                .foregroundColor(self.txtColor)
                .font(.tooltipTextBody)
        }
        .frame(height: 34.0)
    }
}
