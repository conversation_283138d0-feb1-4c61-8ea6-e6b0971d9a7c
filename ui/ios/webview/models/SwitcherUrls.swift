//
//  SwitcherUrls.swift
//  Cap-iOS
//
//  Created by <PERSON> on 11/21/24.
//  Copyright © 2024 Switcher Inc. All rights reserved.
//

import Foundation
import SwiftUI

public struct SwitcherUrls: Codable {
    let helpUrl: String?
    let brandProfileUrl: String?
    let scheduleCallUrl: String?
    let accountDeletionUrl: String?
    let createAccountUrl: String?
    let forgotPasswordUrl: String?
    let getStartedCatalogUrl: String?
    let sourceLogsSamplingRate: Double?

    public enum CodingKeys: String, CodingKey {
        case helpUrl = "HelpUrl"
        case brandProfileUrl = "BrandProfileUrl"
        case scheduleCallUrl = "ScheduleCallUrl"
        case accountDeletionUrl = "AccountDeletionUrl"
        case createAccountUrl = "CreateAccountUrl"
        case forgotPasswordUrl = "ForgotPasswordUrl"
        case getStartedCatalogUrl = "GetStartedCatalogUrl"
        case sourceLogsSamplingRate = "SourceLogsSamplingRate"

        var key: String { self.stringValue }

        var knowledgeBaseFilt<PERSON>: Bo<PERSON> {
             return switch self {
             case .helpUrl: true
             default: false
             }
         }

        var title: LocalizedStringKey {
            return switch self {
            case .helpUrl: "menu.knowledge-base.title"
            case .brandProfileUrl: "menu.brand-profile.title"
            case .scheduleCallUrl: "menu.message.csm-hubspot.webview.title"
            case .accountDeletionUrl: "welcome-d3.button.delete-account"
            case .createAccountUrl: ""
            case .forgotPasswordUrl: "welcome-d3.field.forgot.password"
            case .getStartedCatalogUrl: ""
            case .sourceLogsSamplingRate: ""
            }
        }

        var webSheetStyle: WebSheetStyle {
            return switch self {
            case .helpUrl: .normal
            case .accountDeletionUrl,
                    .forgotPasswordUrl: .none
            case .getStartedCatalogUrl,
                    .brandProfileUrl,
                    .scheduleCallUrl: .simple
            default: .none // Implement as needed for each case
            }
        }

        var isJsHandler: Bool {
            return switch self {
            case .helpUrl,
                    .getStartedCatalogUrl: false
            case .accountDeletionUrl,
                    .brandProfileUrl,
                    .forgotPasswordUrl: true
            default: false // Implement as needed for each case
            }
        }
    }

    func getUrl(for settingsEntryKey: SwitcherUrls.CodingKeys) -> String? {
        return switch settingsEntryKey {
        case .helpUrl: helpUrl
        case .brandProfileUrl: brandProfileUrl
        case .scheduleCallUrl: scheduleCallUrl
        case .accountDeletionUrl: accountDeletionUrl
        case .createAccountUrl: createAccountUrl
        case .forgotPasswordUrl: forgotPasswordUrl
        case .getStartedCatalogUrl: getStartedCatalogUrl
        case .sourceLogsSamplingRate: ""
        }
    }
}
