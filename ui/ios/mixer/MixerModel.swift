//
//  MixerModel.swift
//  Cap-iOS
//
//  Created by <PERSON><PERSON> on 26.02.22.
//  Copyright © 2022 Switcher Inc. All rights reserved.
//

import Foundation
import SwiftUI
import InternalBridging
import Combine

@MainActor
protocol MixerModelDisposeDelegate: AnyObject {
    func mixerModelWantToDispose(navigationInfo: SwitcherNavigationInfo)
    func mixerModelDidDispose()
}

@MainActor
protocol MixerModelDelegate: AnyObject {
    func prodStateDidChange()
    func prodPartiallyBrokenStateDidChange()
    func globalConfSwitchDidBegin()
    func globalConfSwitchDidEnd()
    func prodOutputDidChange() // director mode did change, output rec/bc profile did change
    func streamValidationDidBegin(silently: Bool)
    func streamValidationDidEnd()
    func cloudSyncDidBegin()
    func cloudSyncDidEnd()
    func prodClockDidStart()
    func prodClockDidStop()
    func canCreateNew<PERSON>roadcastingEvent() -> Bool
    func createNewBroadcastingEvent()
    func canEditCurrentBroadcastingEvent() -> Bool
    func editCurrentBroadcastingEvent()
    func setupRtmpChannels()
    func countdownDidStop()
    func scheduleNextEvent()
    func autoGeneratedEventDidComplete()
}

private let logger = LsLogger(subsystem: "swi.ui", category: "MixerModel")

@MainActor
class MixerModel {

    private var globalConfSwitchInProgress = 0
    private var exiting = false
    private var isUsingRemoteCameras = false // only UDP (SRT/RTP) cameras matter here
    private var applyResyncDelayForRemoteCameras = false
    private var resyncDelay: Float = 0.0
    private var isVideoChatAudioModeEnabled = false
    private var isVoiceModeForced = false
    private var audioWarning: StudioAlertError?
    private var durationReport: DurationReport?
    private let seemoCameraManager: SeemoCameraManager
    private let externalAvfCameraManager: ExternalAvfCameraManager

    weak var delegate: (any MixerModelDelegate)?
    weak var dismissDelegate: (any MixerModelDisposeDelegate)?

    let sourceList: SourceList
    let prodManager: ProdManager
    let sourcePanelLogger = SourcePanelLogger()
    let prodTimer = ProdTimer()
    var countdownTimer: Timer?
    var didCountdownStopBeforeFinishing = false
    var isAutoReconnectShown: Bool = false
    let toolLibrary: ToolLibrary
    let globalTimersManager: GlobalTimersManager

    private(set) var env: RLCapEnv
    private(set) var cameraConfigurator: CameraConfigurator
    private(set) var cameraSource: SourceListEntry?
    private(set) var audioSource: SourceListEntry?
    private(set) var storedElementOrganizer: StoredElementOrganizer
    private(set) var vContext: VContext
    private(set) var vClock: VClock
    private(set) var mainMixer: MainMixer
    private(set) var audioMixer: AudioMixer?
    private var audioEngineNotifier: AudioEngineNotifier?
    private(set) var capBridgeMixerApi: CapBridgeMixerApi
    private(set) var capBridgeUiApi: MixerCapBridgeUiApi
    private(set) var capBridgeTimerApi: CapBridgeTimerApi
    private(set) var capBridgeMarkerApi: CapBridgeMarkerApi
    private(set) var capBridgeProdApi: CapBridgeProdApi
    private(set) var webrtcCameraManager: WebRtcCameraManager
    private(set) var webrtcOutputManager: WebRtcOutputManager
    private(set) var camoOutputManager: CamoOutputManager
    private(set) var actionManager: ElementCollectionActionManager
    private(set) var infoSentinel: RLInfoSentinel
    private(set) var isGlobalConfSwitchInProgress = false
    private(set) var markersService: MarkersLiveService
    private(set) var sinkMarkerService: AnyCancellable?
    private(set) var navigation: StudioNavigationViewModel

    private var videoLibraryUsage = VideoLibraryUsageService()

    var vclock: UnsafeMutablePointer<VCLOCK> { return vClock.vclock }

    var isCloudSyncInProgress: Bool {
        StreamingProviderList.shared.isRequestingFromCloud
    }

    // Cloud Syncing in Progress should only block streaming, not record only
    var shouldCloudSyncBlockRec: Bool {
        isCloudSyncInProgress && BCProfileLibrary.shared.currentProfileType != .recordOnly
    }

    private var _allowSpontaneousModalPresentation = false
    var allowSpontaneousModalPresentation: Bool {
        get {
            _allowSpontaneousModalPresentation
        }
        set(allowSpontaneousModalPresentation) {
            if _allowSpontaneousModalPresentation != allowSpontaneousModalPresentation {
                _allowSpontaneousModalPresentation = allowSpontaneousModalPresentation

                // self.autoPromptForLogin = _allowSpontaneousModalPresentation;

                if _allowSpontaneousModalPresentation && !SSNUserAccount.shared.accessGranted {
                    displayLimitedAccessWarning(source: .enteringStudio)
                }

                if _allowSpontaneousModalPresentation && SSNUserAccount.shared.accessGranted {
                   displayWarningVideoLibrary()
                }

                if _allowSpontaneousModalPresentation && env.audioEngine?.state == .interrupted {
                    showAudioWarning()
                }

                if !_allowSpontaneousModalPresentation {
                    hideAudioWarning()
                }
            }
        }
    }

    init(env: RLCapEnv, navigation: StudioNavigationViewModel) {
        self.env = env
        self.navigation = navigation

        toolLibrary = ToolLibrary()
        Lab.shared.addAllDevTools(to: toolLibrary)
        CloudyToolLibrary.shared.addAllCloudyTools(to: toolLibrary)

        cameraConfigurator = CameraConfigurator(highQualityMode: true,
                                                outputProfile: OutputProfile.shared,
                                                bcProfileLib: BCProfileLibrary.shared)

        // be sure that resync delay corresponds to an official level
        let wifiResyncDelay = ResyncSettings.shared.resyncDelay
        let level = ResyncDelayLevel.level(resyncDelay: wifiResyncDelay)
        if wifiResyncDelay != level.value {
            ResyncSettings.shared.resyncDelay = level.value
        }

        resyncDelay = ResyncSettings.shared.minimumResyncDelay
        logger.info("resyncDelayForWiFiCameras=\(ResyncSettings.shared.resyncDelay)")
        logger.info("resyncDelay=\(resyncDelay)")

        vContext = VContextFactory.create()
        vClock = VClock()

        let far = OutputProfile.shared.targetAspectRatio
        let renderTarget = VContextFactory.defaultRenderTarget(frameAspectRatio: Double(far.numer) / Double(far.denom))

        let mainMixer = MainMixer(vContext: vContext,
                                  renderTarget: renderTarget)
        self.mainMixer = mainMixer
        self.actionManager = ElementCollectionActionManager(mainMixer: self.mainMixer,
                                                            targetAspectRatio: far,
                                                            navigation: self.navigation)
        // Attach the action manager back to the main mixer so we can pass through views for asset management
        self.mainMixer.actionManager = self.actionManager

        mainMixer.channelManager.outputClock = vClock
        mainMixer.resyncDelay = resyncDelay
        globalTimersManager = GlobalTimersManager(dataHub: mainMixer.dataHub)
        capBridgeTimerApi = CapBridgeTimerApi(globalTimersManager: globalTimersManager)
        capBridgeMarkerApi = CapBridgeMarkerApi(dataHub: mainMixer.dataHub)
        capBridgeProdApi = CapBridgeProdApi()

        let userInfo = SSNUserAccount.shared.userInfo!
        if userInfo.hasWatermark {
            let factory = WatermarkFactory(targetAspectRatio: CGFloat(far.numer) / CGFloat(far.denom))
            // position of watermark is calculated in mtech -> vfilter_banner.c -> method _paint()
            mainMixer.watermark = M2MixerWatermark(image: factory.image)
        }

        if let audioEngine = env.audioEngine {
            assert(env.audio is LocalAudio)

            audioMixer = AudioMixer(engine: audioEngine)
            cameraConfigurator.configureAudioMixer(audioMixer!)
            cameraConfigurator.configureAudioEngine(audioEngine)
            mainMixer.channelManager.audioMixer = audioMixer
            audioEngineNotifier = AudioEngineNotifier(audioEngine: audioEngine)
        }

        storedElementOrganizer = StoredElementOrganizer(mainMixer: mainMixer, targetAspectRatio: far)

        prodManager = ProdManager(mainMixer: self.mainMixer)
        prodManager.audioMixer = audioMixer

        sourceList = SourceList()
        sourceList.cameraConfigurator = cameraConfigurator
        sourceList.mainMixer = self.mainMixer
        sourceList.resyncDelay = resyncDelay

        if let camera = self.env.camera {
            cameraSource = sourceList.addLocalSource(camera)
        }
        if let audio = self.env.audio {
            audioSource = sourceList.addLocalSource(audio)
        }
        cameraSource?.competitionRule = .localAvf
        audioSource?.competitionRule = .localAvf
        cameraSource?.hasImplicitSystemRotationAutoMode = self.env.video?.isDeviceRotationDetectionAllowed ?? false
        cameraSource?.emulateProdOrientation = true

        audioSource?.isHidden = true

        let isLocalVideoAllowed = RecProfile.shared.isocamMode != .full
        audioSource?.isLocked = !isLocalVideoAllowed
        cameraSource?.isLocked = !isLocalVideoAllowed

        capBridgeUiApi = MixerCapBridgeUiApi(targetAspectRatio: far,
                                             navigationViewModel: self.navigation)
        capBridgeMixerApi = CapBridgeMixerApi(mainMixer: self.mainMixer)
        webrtcCameraManager = WebRtcCameraManager(mainMixer: self.mainMixer)
        webrtcOutputManager = WebRtcOutputManager()
        webrtcOutputManager.audioMixer = audioMixer
        camoOutputManager = CamoOutputManager(resyncDelay: UInt32(round(resyncDelay * 1000)))
        camoOutputManager.audioMixer = audioMixer
        infoSentinel = RLInfoSentinel(mainMixer: self.mainMixer)

        seemoCameraManager = SeemoCameraManager(sourceList: sourceList)
        seemoCameraManager.isCameraUseAllowed = isLocalVideoAllowed

        externalAvfCameraManager = ExternalAvfCameraManager(sourceList: sourceList)
        externalAvfCameraManager.isCameraUseAllowed = isLocalVideoAllowed

        self.markersService = MarkersLiveService(dataHub: mainMixer.dataHub)

        prodManager.delegate = self

        sourceList.addDelegate(self)

        BCProfileLibrary.shared.addChangeDelegate(self)

        RecProfile.shared.addDelegate(self)

        AppStateWatcher.shared.addGraceTimeDelegate(self)

        capBridgeUiApi.mixerModel = self

        RNExceptionLogger.shared.delegate = self

        webrtcOutputManager.addDelegate(self)

        ResyncSettings.shared.addDelegate(self)
        AudioSettings.shared.addDelegate(self)

        env.audioEngine?.addObserver(self)

        StreamingProviderList.shared.addDelegate(self)
        if StreamingProviderList.shared.isRequestingFromCloud {
            streamingProviderListRequestDidBegin()
        }

        updateWebRtcManager()

        setMonitoring()
    }

    deinit {
        ResyncSettings.shared.removeDelegate(self)
        sourceList.removeDelegate(self)
        env.audioEngine?.removeObserver(self)
        cameraSource?.competitionRule = .none
        audioSource?.competitionRule = .none
    }

    func dispose(_ completion: (() -> Void)? = nil) {

        mainMixer.cancelSpeedTest()

        RNExceptionLogger.shared.delegate = nil

        seemoCameraManager.dispose()
        externalAvfCameraManager.dispose()

        webrtcCameraManager.dispose()

        webrtcOutputManager.audioMixer = nil
        camoOutputManager.audioMixer = nil

        sourceList.dispose({
            self.mainMixer.dispose {
                completion?()
                self.dismissDelegate?.mixerModelDidDispose()
            }
        })
    }

    var prodIndicatorText: String {
        let rec = prodManager.isRecordingEnabled
        let bc = prodManager.isBroadcastingEnabled
        let autoSave = BCProfileLibrary.shared.currentProfileType == .recordOnlyAutoSave
        let isPractice = BCProfileLibrary.shared.currentProfileType == .practice

        if isPractice {
            return "PRACTICE"
        }
        // Special case for Record Live Output disabled in auto-save mode
        if bc && !rec && autoSave {
            return "NONE"
        }

        if bc {
            if rec && autoSave {
                return "REC + AS"
            } else if rec {
                return "LIVE + REC"
            } else {
                return "LIVE"
            }
        } else {
            if rec {
                return "REC"
            } else {
                return "NONE"
            }
        }
    }

    func recButtonEnabled(checkGlobalConfSwitch: Bool = false) -> Bool {
        if BCProfileLibrary.shared.currentProfileType == .practice {
            return isPracticeModeEnabled
        }
        let bcEnabled = prodManager.isBroadcastingEnabled
        let recEnabled = prodManager.isRecordingEnabled
        let syncInProgress = (bcEnabled && isCloudSyncInProgress)
        let autoSaveWithoutRec = (!recEnabled && BCProfileLibrary.shared.currentProfileType == .recordOnlyAutoSave)
        let enabled = (bcEnabled || recEnabled)
        && !syncInProgress
        && !autoSaveWithoutRec // Special case for Record Live Output disabled in auto-save mode

        /*
         * We do not disable the button during global conf switches because this
         * makes the rec button changing its color when we swap between front and
         * back camera.
         * enable = enable && !self.mixerModel.isGlobalConfSwitchInProgress;
         */
        if checkGlobalConfSwitch {
            return enabled && !isGlobalConfSwitchInProgress
        } else {
            return enabled
        }
    }

    var isPracticeModeEnabled: Bool {
        return prodManager.isBroadcastingEnabled &&
        !isCloudSyncInProgress &&
        BCProfileLibrary.shared.practiceModeState != .inProgress // Disable REC button while creating the event
    }

    // MARK: - Private

    // just for analytics and to inform prod summary that there is timestamps
    private func bindMarkerService() {
        sinkMarkerService = markersService.$markersLive.sink { [weak self] markers in
            self?.prodManager.analyticsHelper.timestampCount = Int32(markers.count)
        }
    }

    private func unbindMarkerService() {
        sinkMarkerService = nil
    }

    func updateWebRtcManager() {
        let audioConf = cameraConfigurator.getAudioConf()
        webrtcCameraManager.audioConf = audioConf
    }

    func askForGlobalConfig() {
        if let am = audioMixer {
            cameraConfigurator.configureAudioMixer(am)
        }
        if let ae = env.audioEngine {
            cameraConfigurator.configureAudioEngine(ae)
        }
        sourceList.askForGlobalConfig()
    }

    func updateResyncDelay() {
        var resyncDelay: Float
        if applyResyncDelayForRemoteCameras {
            resyncDelay = ResyncSettings.shared.resyncDelay
        } else {
            resyncDelay = ResyncSettings.shared.minimumResyncDelay
        }

        if self.resyncDelay == resyncDelay {
            return
        }

        self.resyncDelay = resyncDelay

        logger.info("resyncDelay=\(resyncDelay)")

        mainMixer.resyncDelay = resyncDelay
        sourceList.resyncDelay = resyncDelay
        camoOutputManager.resyncDelay = UInt32(round(resyncDelay * 1000))
        askForGlobalConfig()
    }

    func showAudioWarning() {

        let restartButton = StudioAlertButton(title: "MixerModel.audio-warning.restart") {
            let engine = self.env.audioEngine
            engine?.recover()

            if let aw = self.audioWarning {
                self.navigation.dismissError(error: aw)
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    self.navigation.present(error: aw)
                }
            }

        }

        self.audioWarning = StudioAlertError(title: "MixerModel.audio-warning.title".localized(),
                                            message: "MixerModel.audio-warning.msg".localized(),
                                            buttons: [restartButton])

        self.navigation.present(error: self.audioWarning!)

    }

    func hideAudioWarning() {
        if let aw = self.audioWarning {
            self.navigation.dismissError(error: aw)
            self.audioWarning = nil
        }
    }

    // MARK: Local Sources

    func startLocalSourceConnection() {
        let isLocalVideoAllowed = RecProfile.shared.isocamMode != .full
        if let cameraSource = cameraSource, isLocalVideoAllowed {
            mainMixer.autoAddToProgram.autoGoLiveNextAddedLiveLocalVideoSource()
            /*
             * Warning:
             * The following call could immediately calls MainMixer::addChannel
             * and trigger the main mixer delegate.
             */
            sourceList.startSourceConnection(cameraSource)
        } else if let audioSource = audioSource {
            sourceList.startSourceConnection(audioSource)
        }
    }

    private func updateLocalSource() {
        let isLocalVideoAllowed = RecProfile.shared.isocamMode != .full
        let isLocalVideoConnected = cameraSource?.state == .connecting || cameraSource?.state == .connected

        if !isLocalVideoAllowed && isLocalVideoConnected, let audioSource = self.audioSource {
            sourceList.startSourceConnection(audioSource)
        }

        audioSource?.isLocked = !isLocalVideoAllowed
        cameraSource?.isLocked = !isLocalVideoAllowed
        seemoCameraManager.isCameraUseAllowed = isLocalVideoAllowed
        externalAvfCameraManager.isCameraUseAllowed = isLocalVideoAllowed
    }

    // MARK: - Cloudy Tools

    private var _cloudyToolContext: RNAppContext?
    var cloudyToolContext: RNAppContext? {
        if _cloudyToolContext == nil {
            if let url = CloudyToolManager.shared.currentBundleBodyUrl {
                _cloudyToolContext = RNAppContext(bundleURL: url, contextOfUse: .cloudyTool, mixerModel: self)
            }
        }
        return _cloudyToolContext!
    }

    // MARK: - ProdManager

    private var failed = false // failed is true if an alert has been displayed
    private var prodAlert: StudioAlertError?
    private var prodSummaryViewModel: ProdSummaryLoaderViewModel?
    private var hasProdSummaryDisplayed: Bool = false

    enum ProdState {
        case idle
        case running
        case terminating

        var name: String {
            return switch self {
            case .idle: "idle"
            case .running: "running"
            case .terminating: "terminating"
            }
        }

        var isInProgress: Bool {
            return switch self {
            case .idle: false
            case .running, .terminating: true
            }
        }
    }

    private(set) var prodState: ProdState = .idle {
        didSet {
            if prodState != oldValue {
                self.updateMarkersService()
                delegate?.prodStateDidChange()
                capBridgeProdApi.prodState = CapBridgeProdApi.ProdState(
                    state: prodState,
                    gid: prodState == .idle ? nil : prodManager.gid)
                Task { @MainActor in
                    SwitcherState.shared.prodState = prodState
                }
            }
        }
    }

    private func updateMarkersService() {
        markersService.canAddMarkers = prodState == .running

        if prodState == .running {
            bindMarkerService()
        } else {
            unbindMarkerService()
        }

        if prodState == .idle {
            markersService.resetAllLiveMarkers()
            markersService.startTime = 0
        }
    }

    private(set) var isProdPartiallyBroken = false { // is recording while broadcast failed
        didSet {
            if isProdPartiallyBroken != oldValue {
                delegate?.prodPartiallyBrokenStateDidChange()
            }
        }
    }

    @MainActor func checkDeviceConfigurationBeforeStarting() async -> Bool {

        let service = CheckingDeviceConfigurationService(uploadSpeed: mainMixer.uploadSpeed)

        let showWarningStreamQuality = service.showWarningStreamQuality
        let showWarningBitRateSuggestion = service.showWarningBitRateSuggestion

        let continueProcess =
            if showWarningStreamQuality {
                await showRecommendedStreamQuality(recommendedQuality: service.recommendQuality ?? .undefined)
            } else if showWarningBitRateSuggestion {
                await showBitrateSuggestion(videoBitRate: service.currentProfile?.videoBitRate ?? 0,
                                            maxBitRate: service.maxBitRate ?? 0)
            } else { true }

        if !continueProcess {
            return false
        }

        if service.showWarningStorage {
            // in the case where we have the 2 warning - we should wait before to show the 2nd one
            if showWarningStreamQuality || showWarningBitRateSuggestion {
                do {
                    try await Task.sleep(for: .seconds(1.5))
                } catch {
                    return true
                }
            }
            return await showStorageWarning(availableStorage: service.availableStorage ?? 0)
        } else {
            return true
        }
    }

    @MainActor
    private func showRecommendedStreamQuality(recommendedQuality: SourceConf.Size) async -> CheckingDeviceContinueProcess {
        let continueProcess = await withCheckedContinuation { continuation in
            navigation.present(type: .recommendQuality(recommendedQuality: recommendedQuality,
                                                       completion: { continueProcess in
                continuation.resume(returning: continueProcess)
            }))
        }
        return continueProcess
    }

    @MainActor
    private func showBitrateSuggestion(videoBitRate: Int, maxBitRate: Int) async -> CheckingDeviceContinueProcess {
        let continueProcess = await withCheckedContinuation { continuation in
            navigation.present(type: .recommendBitRateChange(currentBitRate: videoBitRate,
                                                             maxBitRate: maxBitRate,
                                                             completion: { keepCurrent in
                if keepCurrent {
                    continuation.resume(returning: true)
                } else {
                    self.delegate?.setupRtmpChannels()
                    continuation.resume(returning: false)
                }
            }))
        }
        return continueProcess
    }

    @MainActor
    private func showStorageWarning(availableStorage: Int64) async -> CheckingDeviceContinueProcess {
        let continueProcess = await withCheckedContinuation { continuation in
            navigation.present(type: .storageWarning(availableStorage: availableStorage, completion: { continueProcess in
                continuation.resume(returning: continueProcess)
            }))
        }
        return continueProcess
    }

    func prodStartStop() {

        if !SSNUserAccount.shared.accessGranted {
            displayLimitedAccessWarning(source: .recordButton)
            return
        }

        ClarityLogger.shared.pause()

        mainMixer.cancelSpeedTest()

        prodManager.dumpState()

        switch prodManager.state {
        case .idle:
            if cameraSource?.state == .connecting || audioSource?.state == .connecting {
                /*
                 * This can happen at startup or when we switch on the isocam mode which
                 * disconnects the local camera and connects the local audio.
                 */
                return
            }
            if isGlobalConfSwitchInProgress {
                return
            }
            if prodManager.isBroadcastingEnabled && shouldCloudSyncBlockRec {
                return
            }
            durationReport = Analytics.shared.beginReportingDuration(name: "Recording Delay")
            hasProdSummaryDisplayed = false
            prodManager.start()
        case .finishing:
            break
        case .requestingAction:
            /*
             * This should never happen, but in the past we has problems with
             * the react native UI which prevented the alert view that manages
             * this state to be displayed.
             * At that moment, this case was posssible and we did our best to
             * manage it and keep the ui consistent.
             */
            if prodManager.actionChoice.contains(.stopStreaming) {
                prodManager.performAction(.stopStreaming)
            } else {
                prodManager.performAction(.stopAll)
            }
        default:
            prodManager.stop()
        }
    }

    func startCountdown(completion: @escaping () -> Void) {
        countdownTimer?.invalidate()
        countdownTimer = Timer.scheduledTimer(withTimeInterval: 3.5, repeats: false) { [weak self] _ in
            DispatchQueue.main.async { [weak self] in
                completion()
                self?.prodManager.countdownDidFinish()
                self?.countdownTimer?.invalidate()
                self?.countdownTimer = nil
                self?.didCountdownStopBeforeFinishing = false
            }
        }

    }

    func stopCountdown() {
        countdownTimer?.invalidate()
        countdownTimer = nil
        self.prodManager.countdownDidntFinish()
        delegate?.countdownDidStop()
        didCountdownStopBeforeFinishing = true
    }

    var isProdClockRunning: Bool {
        return prodTimer.isRunning
    }

    func autoReconnectAttemptComplete() {
        isAutoReconnectShown = false
    }

    func prodStateDidChange() {
        if exiting {
            return
        }

        let state = prodManager.state

        if state == .finishing {
            prodState = .terminating
        } else if state == .idle {
            if prodState.isInProgress {
                prodState = .idle
                isProdPartiallyBroken = false

                if applyResyncDelayForRemoteCameras && !isUsingRemoteCameras {
                    applyResyncDelayForRemoteCameras = false
                    updateResyncDelay()
                }
            }
        } else {
            prodState = .running
        }

        if state == .requestingAction {
            if !hasProdSummaryDisplayed && prodManager.attemptAutoReconnect() {
                isAutoReconnectShown = true
                if let actionError = self.prodManager.actionError {
                    self.logError(error: actionError, errorPressedValue: "auto")
                }
            } else {
                let actionError = prodManager.actionError!
                failed = true
                stopCountdown()
                isProdPartiallyBroken = true

                if actionError.errorCode == .invalidAccount {
                    delegate?.createNewBroadcastingEvent()

                    DispatchQueue.main.async(execute: {
                        self.prodManager.performAction(.stopAll)
                    })
                } else {
                    // Catch error when there's no multistreaming even created and change the error code
                    if actionError.errorCode == .badHTTPResponseCode &&
                        BCProfileLibrary.shared.currentProfileId?.name == nil {
                        actionError.errorCode = .invalidEvent
                    }

                    let title = actionError.friendlyLocalizedTitle
                    let subtitle = actionError.friendlyLocalizedMessage

                    var alert = StudioAlertError(title: title ?? subtitle,
                                                 message: title != nil ? subtitle : "")

                    let canCreateNewEvent = delegate?.canCreateNewBroadcastingEvent() ?? false
                    let canEditCurrentEvent = delegate?.canEditCurrentBroadcastingEvent() ?? false

                    let noRtmpChannel = actionError.noRtmpChannel
                    let suggestEventCreation = (actionError.suggestsCreatingNewEvent && canCreateNewEvent) ||
                    (actionError.suggestsEditingCurrentEvent && canCreateNewEvent && !canEditCurrentEvent)
                    let suggestEditing = actionError.suggestsEditingCurrentEvent && canEditCurrentEvent

                    if suggestEventCreation {
                        alert.title = "MixerModel.create-new-event.title".localized()
                        alert.message = "MixerModel.create-new-event.subtitle".localized()
                        alert.buttons.append(
                            StudioAlertButton(title: "MixerModel.create-new-event.button.short",
                                              action: {
                                                  self.errorAction(actionError, mask: .stopAll)
                                                  self.delegate?.createNewBroadcastingEvent()
                                              }))

                        alert.buttons.append(
                            StudioAlertButton(title: "global-action.cancel.title",
                                              action: { self.errorAction(actionError, mask: .stopAll) }))

                    } else if noRtmpChannel {
                        alert.title = "MixerModel.no-rtmp-channel.title".localized()
                        alert.message = "MixerModel.no-rtmp-channel.subtitle".localized()
                        alert.buttons.append(
                            StudioAlertButton(title: "MixerModel.no-rtmp-channel.button",
                                              action: {
                                                  self.errorAction(actionError, mask: .stopAll)
                                                  self.delegate?.setupRtmpChannels()
                                              }))

                        alert.buttons.append(
                            StudioAlertButton(title: "global-action.cancel.title",
                                              action: { self.errorAction(actionError, mask: .stopAll) }))

                    } else if suggestEditing {
                        alert.buttons.append(
                            StudioAlertButton(title: "MixerModel.create-edit-event.title",
                                              action: {
                                                  self.errorAction(actionError, mask: .stopAll)
                                                  self.delegate?.editCurrentBroadcastingEvent()
                                              }))

                        alert.buttons.append(
                            StudioAlertButton(title: "global-action.cancel.title",
                                              action: { self.errorAction(actionError, mask: .stopAll) }))

                    } else if prodManager.actionChoice.contains(.stopStreaming) {
                        let stopAll: LocalizedStringKey = "prod-action.alert.stop-rec-and-broadcast-button.title"
                        let stopStream: LocalizedStringKey = "prod-action.alert.stop-broadcast-button.title"
                        let recoverStream: LocalizedStringKey = "prod-action.alert.recover-broadcast-button.title"

                        alert.buttons.append(
                            StudioAlertButton(title: stopAll,
                                              action: { self.errorAction(actionError, mask: .stopAll) }))

                        alert.buttons.append(
                            StudioAlertButton(title: stopStream,
                                              action: { self.errorAction(actionError, mask: .stopStreaming) }))

                        if prodManager.actionChoice.contains(.recoverStreaming) {
                            alert.buttons.append(
                                StudioAlertButton(title: recoverStream,
                                                  action: { self.errorAction(actionError, mask: .recoverStreaming) }))
                        }
                    } else if prodManager.actionChoice.contains(.recoverStreaming) {
                        alert.buttons.append(
                            StudioAlertButton(title: "prod-action.alert.stop-button.title",
                                              action: { self.errorAction(actionError, mask: .stopAll) }))

                        alert.buttons.append(
                            StudioAlertButton(title: "prod-action.alert.recover-broadcast-button.title",
                                              action: { self.errorAction(actionError, mask: .recoverStreaming) }))
                    } else {
                        alert.buttons.append(
                            StudioAlertButton(title: "global-action.ok.title",
                                              action: { self.errorAction(actionError, mask: .stopAll) }))
                    }

                    if prodSummaryViewModel != nil {
                        cancelProdSummary()
                    } else if let alert = prodAlert {
                        self.navigation.dismissError(error: alert)
                    }

                    self.navigation.present(error: alert)
                    prodAlert = alert
                }
            }
        }

        if state == .flushing && !AppStateWatcher.shared.isQuitting {

            if let alert = prodAlert {
                self.navigation.dismissError(error: alert)
                prodAlert = nil
            }

            if !didCountdownStopBeforeFinishing {
                displayProdSummary()
            }

            didCountdownStopBeforeFinishing = false
        }

        if state == .idle {
            if let alert = prodAlert {
                self.navigation.dismissError(error: alert)
                prodAlert = nil
            }

            if failed {
                cancelProdSummary()
            } else {
                if !didCountdownStopBeforeFinishing {
                    displayProdSummary()
                    prodSummaryViewModel?.prodSummaryIsReady = true
                }

                didCountdownStopBeforeFinishing = false
            }

            StreamingProviderList.shared.requestFromCloud()
            failed = false
        }

        if state == .idle || state == .flushing || state == .finishing {
            if prodTimer.isRunning {
                prodTimer.stop()
                prodTimer.state = .unknown
                delegate?.prodClockDidStop()
            }
        }

        delegate?.prodStateDidChange()
    }

    private func errorAction(_ actionError: ProdError, mask: ProdManagerActionMask) {
        switch mask {
        case .continueAction:
            break
        case .recoverStreaming:
            self.failed = false
            self.prodManager.performAction(.recoverStreaming)
            self.logErrorPressed(error: actionError,
                    errorPressed: .recoverStreaming)
        case .stopAll:
            self.prodManager.performAction(.stopAll)
            self.logErrorPressed(error: actionError,
                                 errorPressed: .stopAll)
        case .stopStreaming:
            self.prodManager.performAction(.stopStreaming)
            self.logErrorPressed(error: actionError,
                                 errorPressed: .stopStreaming)
        default:
            break
        }
    }

    func logErrorPressed(error: ProdError, errorPressed: ProdManagerActionMask) {
        logError(error: error, errorPressedValue: errorPressed.analyticsKey)
    }

    func logError(error: ProdError, errorPressedValue: String) {
        var properties = ["error_code": error.errorCode.rawValue,
                          "error_name": error.errorName ?? "",
                          "error_message": error.localizedMessage ?? "",
                          "error_pressed": errorPressedValue
        ] as [String: Any]
        properties["prodState"] = prodState.name
        if let broadcastId = BCProfileLibrary.shared.profile(withId:
                                    BCProfileLibrary.shared.currentProfileId)?.broadcastId {
            properties["broadcastId"] = broadcastId
        }

        Analytics.shared.reportEvent(name: "prodmanager_error",
                                     properties: properties)
    }

    func audioStateDidChange(_ state: RLAudioEngineState) {
        if exiting {
            return
        }

        if state == .interrupted {
            var props = [String: Any]()
            if let debugInfo = env.audioEngine?.interruptionReason?.debugInfo {
                for (key, value) in debugInfo {
                    props[key] = value
                }
            }
            props["context"] = "mixer"
            props["prodInProgress"] = NSNumber(value: prodState != .idle)
            Analytics.shared.reportEvent(
                name: "Debug",
                properties: props)
        }

        if state == .interrupted && allowSpontaneousModalPresentation {
            showAudioWarning()
        } else {
            hideAudioWarning()
        }
    }
}

extension MixerModel: ProdManagerDelegate {

    nonisolated func prodManagerStateDidChange() {
        DispatchQueue.main.async {
            self.prodStateDidChange()
        }
    }

    nonisolated func prodManagerStreamValidationDidBegin(silently: Bool) {
        DispatchQueue.main.async {
            self.delegate?.streamValidationDidBegin(silently: silently)
        }
    }

    nonisolated func prodManagerStreamValidationDidEnd() {
        DispatchQueue.main.async {
            self.delegate?.streamValidationDidEnd()
        }
    }

    nonisolated func prodManagerStartTimeBecameAvailable() {
        DispatchQueue.main.async {
            let state = self.prodManager.state
            let running = state != .idle && state != .flushing && state != .finishing
            if self.prodManager.isStartTimeAvailable && running {
                if !self.prodTimer.isRunning {
                    self.prodTimer.reset()
                    self.prodTimer.state = self.prodManager.broadcastSanityState
                    self.prodTimer.start(from: self.prodManager.startTime())
                    self.delegate?.prodClockDidStart()
                }
                self.markersService.startTime = self.prodManager.startTime()
                self.durationReport?.end()
            }
        }
    }

    nonisolated func prodManagerSanityStateDidChange() {
        DispatchQueue.main.async {
            if self.prodTimer.isRunning {
                self.prodTimer.state = self.prodManager.broadcastSanityState
            }
        }
    }
}

extension MixerModel: BCProfileLibraryChangeDelegate {
    func autoGeneratedEventDidComplete() {
        delegate?.autoGeneratedEventDidComplete()
    }

    func activeBCProfileDidChange(onlyStreamingQuality: Bool) {
        askForGlobalConfig()
        updateWebRtcManager()
        delegate?.prodOutputDidChange()

        if !onlyStreamingQuality {
            displayWarningVideoLibrary()
            // run the speed when we change the event
            mainMixer.runSpeedTest()
        }

    }

    func inactiveBCProfilesDidChange() {
        // do nothing
    }

    func programRecordingDidChange() {
        updateWebRtcManager()
        delegate?.prodOutputDidChange()
    }
}

extension MixerModel: RecProfileDelegate {

    nonisolated func recProfileDidChange() {
        DispatchQueue.main.async {
            self.updateLocalSource()
            self.askForGlobalConfig()
            self.updateWebRtcManager()
            self.delegate?.prodOutputDidChange()
        }
    }
}

extension MixerModel: StreamingProviderListDelegate {

    func streamingProviderListRequestDidBegin() {
        delegate?.cloudSyncDidBegin()
    }

    func streamingProviderListRequestDidEnd() {
        delegate?.cloudSyncDidEnd()
    }

    func streamingProviderListDidChange() {
    }
}

extension MixerModel: SourceListDelegate {

    nonisolated func sourceDidAppear(_ entry: SourceListEntry) {
        // do nothing
    }

    nonisolated func sourceDidDisappear(_ entry: SourceListEntry) {
        // do nothing
    }

    nonisolated func sourceDidChange(_ entry: SourceListEntry) {
        DispatchQueue.main.async {
            // is there any remote cameras in use
            var found = false
            for entry in self.sourceList.list {
                if entry.isRemoteCamera && entry.state != .idle {
                    found = true
                    break
                }
            }

            self.isUsingRemoteCameras = found

            if self.isUsingRemoteCameras && !self.applyResyncDelayForRemoteCameras {
                self.applyResyncDelayForRemoteCameras = true
                self.updateResyncDelay()
            }

            if !self.isUsingRemoteCameras && self.applyResyncDelayForRemoteCameras && self.prodState == .idle {
                self.applyResyncDelayForRemoteCameras = false
                self.updateResyncDelay()
            }
        }
    }

    nonisolated func source(_ entry: SourceListEntry,
                            connectionDidBreakWithReason reason: Int32,
                            diagInfo: [AnyHashable: Any]?) {
        // do nothing
    }

    nonisolated func sourceWillInterruptRecording(_ entry: SourceListEntry) {
        DispatchQueue.main.async {
            let studioError = StudioNavigationErrorSender(navigation: self.navigation)
            studioError.showRecordingInterupted()
        }
    }

    nonisolated func globalConfSwitchDidBegin() {
        DispatchQueue.main.async {
            self.isGlobalConfSwitchInProgress = true
            self.delegate?.globalConfSwitchDidBegin()
        }
    }

    nonisolated func globalConfSwitchDidEnd() {
        DispatchQueue.main.async {
            self.isGlobalConfSwitchInProgress = false
            self.delegate?.globalConfSwitchDidEnd()
        }
    }
}

extension MixerModel: GraceTimeDelegate {

    func needsMoreGraceTime(_ graceTime: Double) -> Bool {
        if !exiting {
            exiting = true

            vClock.isEnabled = false
            env.audioEngine?.stop()

            sourceList.dispose({
                self.mainMixer.dispose(completion: nil)
            })

            prodManager.forceStop()

            if let audio = env.audio, audio.isRecording {
                audio.stopRec()
            }
            if let video = env.video, video.isRecording {
                video.stopRec()
            }

            if let seemoCamera = seemoCameraManager.camera {
                if seemoCamera.isRecording {
                    seemoCamera.stopRec()
                }
            }
        }

        let writingToDisk = (env.video?.isRecording ?? false) || (env.video?.isFlushingToDisk ?? false) ||
                            (env.audio?.isRecording ?? false) || (env.audio?.isFlushingToDisk ?? false) ||
                            (seemoCameraManager.camera?.isRecording ?? false) ||
                            (seemoCameraManager.camera?.isFlushingToDisk ?? false) ||
                            prodManager.isRecordingToLocalDisk

        /*
         * TODO: monitor communication to know when it is finished
         * in order to avoid waiting if everything is done.
         */

        return graceTime < 0.5 || ((graceTime < 5.0) && writingToDisk)
    }
}

extension MixerModel: ResyncSettingsDelegate {

    nonisolated func resyncSettings(_ resyncSettings: ResyncSettings,
                                    resyncDelayDidChange changes: ResyncSettingsChanges) {
        DispatchQueue.main.async {
            if changes.valueDidChange {
                self.updateResyncDelay()
            }
        }
    }
}

extension MixerModel {

    @MainActor
    public func cancelProdSummary() {
        // prevent prod summary to pop up again once the prod is finished/cancelled
        if prodManager.state != .idle {
            failed = true
        }
        navigation.dismiss()
        self.prodSummaryViewModel = nil
        prodManager.dumpState()
        prodManager.stop()
        prodManager.resetAutoGeneratedEvent()
    }

    @MainActor
    public func closeProdSummary(scheduleNextEvent: Bool = false) {
        navigation.dismiss()
        self.prodManager.dumpState()
        self.prodManager.stop()
        self.prodManager.resetAutoGeneratedEvent()
        if scheduleNextEvent {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.delegate?.scheduleNextEvent()
            }
        }
        self.prodSummaryViewModel = nil
        prodManager.dumpState()
        prodManager.stop()
    }
}

extension MixerModel {

    func displayProdSummary() {
        if self.prodSummaryViewModel != nil || hasProdSummaryDisplayed {
            return
        }
        self.prodSummaryViewModel = ProdSummaryLoaderViewModel(mixerModel: self)
        self.navigation.present(type: .prodSummary(model: self.prodSummaryViewModel!), force: true)
        self.hasProdSummaryDisplayed = true
    }

    func displayLimitedAccessWarning(source: SubscriptionViewSource) {
        if SSNUserAccount.shared.userInfo.shouldPromptWithIAS {
            self.navigation.present(type: .subscription(source: source))
        } else {
            self.navigation.present(type: .limitedAccess)
        }
    }
}

// MARK: - Output Popup

extension MixerModel {

    func displayWarningVideoLibrary(streaming: Bool = true) {

        if streaming {

            let currentProfileType = BCProfileLibrary.shared.currentProfileType

            // for dashboard user, we show always the video storage limit (except for custom rtmp)
            if SSNUserAccount.shared.userInfo.isDashboardUser {
                if currentProfileType == .customRtmp {
                    return
                }
            } else {
                // for appstore user, we don't show the banner if record only or custom rtmp
                if currentProfileType == .customRtmp || currentProfileType == .recordOnly {
                    return
                }
            }
        }

        if videoLibraryUsage.usageState.okToUpload {
            return
        }

        self.navigation.present(type: .libraryWarning(capacity: self.videoLibraryUsage.usageCapacity,
                                                      streaming: streaming))
    }

    private func presentVideoLibrary() {
        self.navigation.present(type: .library, force: true)
    }

}

extension MixerModel: RNExceptionLoggerDelegate {

    func reactNativeDidReportException() {
        assert(Thread.isMainThread)

        let buttons: [StudioAlertButton] = [
            StudioAlertButton(title: "mixer.error.reactnative.restart", action: RNExceptionLogger.shared.reload),
            StudioAlertButton(title: "mixer.error.reactnative.ignore")
        ]

        let alert = StudioAlertError(
            title: "mixer.error.reactnative.title",
            message: "mixer.error.reactnative.message",
            buttons: buttons)

        self.navigation.present(error: alert)

    }
}

extension MixerModel: RLAudioEngineObserver {

    nonisolated func audioEngineSourceDidChange() {
        // do nothing
    }

    nonisolated func audioEngineStateDidChange(_ state: RLAudioEngineState) {
        DispatchQueue.main.async {
            self.audioStateDidChange(state)
        }
    }
}

extension MixerModel: AudioSettingsDelegate {

    func setMonitoring() {
        var voiceMode = AudioSettings.shared.isVoiceModeEnabled
        if isVideoChatAudioModeEnabled && isVoiceModeForced {
            voiceMode = true
        }

        if voiceMode || isVideoChatAudioModeEnabled {
            audioMixer?.setMonitoring(AM_CORE_MON_MODE_NO_LOCAL_MIC, voiceMode)
        } else {
            if AudioSettings.shared.isAudioMonitoringEnabled {
                audioMixer?.setMonitoring(AM_CORE_MON_MODE_PROG, AudioSettings.shared.isVoiceModeEnabled)
            } else {
                audioMixer?.setMonitoring(AM_CORE_MON_MODE_MUTED, AudioSettings.shared.isVoiceModeEnabled)
            }
        }
    }

    func audioMonitoringDidChange(audioSettings: AudioSettings) {
        setMonitoring()
    }

    func voiceModeDidChange(audioSettings: AudioSettings) {
        setMonitoring()
    }
}

extension MixerModel: WebRtcOutputManagerDelegate {

    func webRtcOutputBegin() {
    }

    func webRtcOutputEnd() {
    }

    func videoChatAudioModeDidChange(enabled: Bool) {
        if isVideoChatAudioModeEnabled != enabled {
            isVideoChatAudioModeEnabled = enabled
            setMonitoring()
        }
    }

    func voiceModeForcedDidChange(enabled: Bool) {
        if isVoiceModeForced != enabled {
            isVoiceModeForced = enabled
            setMonitoring()
        }
    }
}
